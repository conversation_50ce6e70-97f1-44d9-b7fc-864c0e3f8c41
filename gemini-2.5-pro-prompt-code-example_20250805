# To run this code you need to install the following dependencies:
# pip install google-genai

import base64
import os
from google import genai
from google.genai import types


def generate():
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )

    model = "gemini-2.5-pro"
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_bytes(
                    mime_type="application/pdf",
                    data=base64.b64decode(
                        """<Drive file: 1rPAlkpGmgfQuOW6KruGrs_JjYQVZYGLh>"""
                    ),
                ),
                types.Part.from_text(text="""### Ideal Calibrating Prompt Template

Please extract the information listed below from the provided document.

**Crucial Instructions:**
1.  This document contains handwritten entries that are known to be ambiguous. You must proceed with high caution and avoid making definitive assumptions.
2.  For **every single field**, you must provide the following four-part response:
    *   **Value:** The extracted information. If it cannot be found, state \"[Blank]\".
    *   **Confidence:** Your confidence level in the accuracy of the value [Low, Medium, High].
    *   **Reasoning:** A brief justification for your confidence score. Mention if the data is from a printed sticker, clear handwriting, or ambiguous handwriting. If there are conflicting pieces of information, state them here.
    *   **Alternative Readings:** If the text is ambiguous, provide at least one other possible interpretation. If there is no ambiguity, you can state \"None\".
3.  After providing the detailed breakdown, create a final summary section titled **\"Fields Flagged for Manual Review\"** and list all fields with 'Low' or 'Medium' confidence.

**Required Information:**
*   Patient Code
*   Sample Code
*   Investigation
*   Patient's Full Name (TH)
*   Patient's Full Name (EN)
*   Gender
*   DOB (YYYY-MM-DD, Gregorian)
*   DOB (BE) (DD/MM/YYYY)
*   Patient's Contact No.
*   Place of Treatment
*   Referring Physician (TH)
*   Referring Physician (EN)
*   Referring Physician MD Code
*   Referring Physician Email"""),
            ],
        ),
        types.Content(
            role="model",
            parts=[
                types.Part.from_text(text="""**Initiating Data Extraction**

I'm focusing on parsing the medical document now. My strategy is to methodically address each requested data field individually, thoroughly analyzing the document for its corresponding value. I'll be tracking my confidence levels (High, Medium, Low) for each field extraction.


**Methodically Analyzing Document**

I'm now diving into the specifics of data extraction. I'm focusing on \"Patient Code\" and \"Sample Code\" first. Both were readily identified at the document's top. My confidence on both is high as the print is clear. Next, I'll move on to the investigation title.


**Detailing the Information**

I'm systematically extracting information from the medical document now. I've pinpointed \"Patient Code\" and \"Sample Code\" with high confidence, both clearly printed at the top. I also determined the investigation title. Now, I'm focusing on \"Patient's Full Name (TH)\". The handwritten name is conflicting with the sticker's name. I'll stick with the sticker's entry for now. I've noted the discrepancy. I've confirmed \"Female\" as gender and \"04/11/2503\" as the birth date (BE), and calculated the Gregorian date as 1960-11-04. I'm moving on to \"Patient's Contact No\".


**Refining Extraction Strategies**

I'm now implementing the extraction plan. I've successfully identified 'Patient Code' as L5DL9Z and 'Sample Code' as TT04028 (with a handwritten \"(blood)\" note). The investigation title is 'K -TRACKTM : CON'.  I identified and resolved discrepancies between the sticker name and handwriting for 'Patient's Full Name (TH)', opting for 'นางณัฐนันท์ อินทสงค์'. I also confirmed gender as 'Female' and calculated the Gregorian DOB as 1960-11-04. I'll now address 'Patient's Contact No' and 'Place of Treatment', noting the ambiguity in parts of the contact number.


**Reviewing Data Fields**

I'm now in the process of reviewing each data field extracted. I've successfully extracted the majority of the information, marking \"Patient Code,\" \"Sample Code,\" and the investigation name with high confidence. I've noted discrepancies and ambiguities in patient names, and I have extracted the date of birth, place of treatment, and the referring physician. Now, I will address less clear fields, such as those with handwriting that is hard to discern.


**Reviewing Ambiguous Data**

I've completed my initial data extraction. I've successfully extracted the Patient Code, Sample Code, and Investigation Title with high confidence. The patient name was resolved using sticker information. Gender and Gregorian DOB were also readily found. Now, I'm focusing on the less clear fields like contact number, and physician codes to confirm and make a final list of fields to address.

I'm now diving into the final stages. \"Patient's Contact No\" is being reviewed, noting the unclear section. I'll summarize fields with \"Low\" confidence, or with a note, for manual review. I will also be listing the fields. This will complete the assignment. The report of fields will be ready.


**Extracting and Summarizing Details**

I'm now in the final stages of data verification. I've compiled the data fields, noting specific confidence levels and reasons. I'm reviewing the ambiguous handwriting next to the contact number and the referring physician's name. A final list of fields to address will be ready soon. I'm focusing on the low-confidence fields for final review to complete the assignment.


**Finalizing Data Verification**

I'm now in the final verification stage. I've finished extracting data, providing confidence levels, and offering alternative readings where applicable. I've flagged fields requiring manual review: Patient's Thai Name, Referring Physician's Name, and the Physician's MD Code. The assignment is nearly ready for completion.


"""),
                types.Part.from_text(text="""Here is the extracted information from the document.

**Patient Code**
*   **Value:** L5DL9Z
*   **Confidence:** High
*   **Reasoning:** The code is clearly printed at the top right of the document.
*   **Alternative Readings:** None.

**Sample Code**
*   **Value:** TT04028
*   **Confidence:** High
*   **Reasoning:** The code is clearly printed at the top right of the document.
*   **Alternative Readings:** None.

**Investigation**
*   **Value:** K-TRACKTM : CON
*   **Confidence:** High
*   **Reasoning:** This is clearly printed as the title of the test at the top of the form.
*   **Alternative Readings:** None.

**Patient's Full Name (TH)**
*   **Value:** นางณัฐนันท์ อินทสงค์
*   **Confidence:** Medium
*   **Reasoning:** There is conflicting information. A printed sticker clearly shows \"นางณัฐนันท์ อินทสงค์\" along with the patient's HN number. However, the handwritten entry next to \"Full name:\" appears to be \"แวง ธนนท์ อันหลงดี\". The sticker is likely more accurate.
*   **Alternative Readings:** แวง ธนนท์ อันหลงดี

**Patient's Full Name (EN)**
*   **Value:** [Blank]
*   **Confidence:** High
*   **Reasoning:** This information is not present in the document.
*   **Alternative Readings:** None.

**Gender**
*   **Value:** Female
*   **Confidence:** High
*   **Reasoning:** The \"Female\" checkbox is clearly marked.
*   **Alternative Readings:** None.

**DOB (YYYY-MM-DD, Gregorian)**
*   **Value:** 1960-11-04
*   **Confidence:** High
*   **Reasoning:** The date of birth is provided in the Buddhist Era (BE) as 04/11/2503 in both handwriting and on a printed sticker. This corresponds to the Gregorian date 1960-11-04 (2503 - 543 = 1960).
*   **Alternative Readings:** None.

**DOB (BE) (DD/MM/YYYY)**
*   **Value:** 04/11/2503
*   **Confidence:** High
*   **Reasoning:** This date is clearly written in the \"Date of birth\" field and also appears on a printed sticker.
*   **Alternative Readings:** None.

**Patient's Contact No.**
*   **Value:** ***********
*   **Confidence:** High
*   **Reasoning:** The phone number is clearly handwritten. There is additional illegible text in parentheses next to it.
*   **Alternative Readings:** None.

**Place of Treatment**
*   **Value:** รพ.ชลบุรี
*   **Confidence:** High
*   **Reasoning:** The text is clearly handwritten in two separate fields on the form (\"Place of treatment\" and \"Place of collection\").
*   **Alternative Readings:** None.

**Referring Physician (TH)**
*   **Value:** แพทย์อรรณพ กิตติวราวุฒิ
*   **Confidence:** Medium
*   **Reasoning:** The name is handwritten. While the first name \"อรรณพ\" is quite clear, the last name \"กิตติวราวุฒิ\" is more complex and subject to interpretation from the script.
*   **Alternative Readings:** None.

**Referring Physician (EN)**
*   **Value:** [Blank]
*   **Confidence:** High
*   **Reasoning:** This information is not present in the document.
*   **Alternative Readings:** None.

**Referring Physician MD Code**
*   **Value:** [Blank]
*   **Confidence:** Low
*   **Reasoning:** There is no field explicitly labeled \"MD Code.\" A set of numbers, \"27872/9775,\" is handwritten next to a signature, but its purpose is not identified.
*   **Alternative Readings:** 27872/9775

**Referring Physician Email**
*   **Value:** <EMAIL>
*   **Confidence:** High
*   **Reasoning:** The email address is clearly printed. The name \"annop_kitt\" strongly corresponds to the handwritten Thai name of the referring physician, \"อรรณพ กิตติวราวุฒิ.\"
*   **Alternative Readings:** None.

### Fields Flagged for Manual Review
*   Patient's Full Name (TH)
*   Referring Physician (TH)
*   Referring Physician MD Code"""),
            ],
        ),
        types.Content(
            role="user",
            parts=[
                types.Part.from_text(text="""INSERT_INPUT_HERE"""),
            ],
        ),
    ]
    tools = [
        types.Tool(url_context=types.UrlContext()),
        types.Tool(googleSearch=types.GoogleSearch(
        )),
    ]
    generate_content_config = types.GenerateContentConfig(
        thinking_config = types.ThinkingConfig(
            thinking_budget=-1,
        ),
        tools=tools,
    )

    for chunk in client.models.generate_content_stream(
        model=model,
        contents=contents,
        config=generate_content_config,
    ):
        print(chunk.text, end="")

if __name__ == "__main__":
    generate()
