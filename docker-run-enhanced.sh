#!/bin/bash
# ChromoForge AgentOS-Compliant Docker Runner v2.0.0
# Enhanced with cleanup capabilities, security checks, and compliance monitoring

set -euo pipefail  # Strict error handling

# Script metadata
readonly SCRIPT_VERSION="2.0.0"
readonly SCRIPT_NAME="ChromoForge Docker Runner"
readonly AGENTOS_TARGET_SCORE=85
readonly SPACE_RECOVERY_TARGET_GB=30

# Colors and formatting
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m'
readonly BOLD='\033[1m'

# Logging functions
log() { 
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() { 
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() { 
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() { 
    echo -e "${RED}[ERROR]${NC} $1"
}

info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

debug() {
    if [[ "${DEBUG:-false}" == "true" ]]; then
        echo -e "${PURPLE}[DEBUG]${NC} $1"
    fi
}

banner() {
    echo -e "${BOLD}${BLUE}================================${NC}"
    echo -e "${BOLD}${BLUE} $1${NC}"
    echo -e "${BOLD}${BLUE}================================${NC}"
}

# Error handling
trap 'error "Script failed on line $LINENO"' ERR

# Utility functions
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        error "Docker is not running. Please start Docker Desktop."
        exit 1
    fi
    debug "Docker daemon is running"
}

check_compose() {
    if ! docker-compose --version > /dev/null 2>&1; then
        error "docker-compose not found. Please install Docker Compose."
        exit 1
    fi
    debug "Docker Compose is available"
}

get_version() {
    if [[ -f "VERSION" ]]; then
        export CHROMOFORGE_VERSION=$(cat VERSION)
        debug "Using version from VERSION file: $CHROMOFORGE_VERSION"
    else
        export CHROMOFORGE_VERSION="1.0.0"
        warn "VERSION file not found, using default: $CHROMOFORGE_VERSION"
    fi
}

# Security functions
check_security() {
    local security_issues=0
    
    log "Performing security audit..."
    
    # Check for exposed credentials in .env
    if [[ -f ".env" ]]; then
        if grep -q "AIzaSy" .env 2>/dev/null; then
            error "Exposed Google API credentials detected in .env file!"
            error "SECURITY RISK: API keys visible in environment files"
            ((security_issues++))
        fi
        
        if grep -qE "sk-[a-zA-Z0-9]{48}" .env 2>/dev/null; then
            error "Exposed OpenAI API key detected in .env file!"
            ((security_issues++))
        fi
        
        if grep -q "postgres://" .env 2>/dev/null; then
            warn "Database connection string detected in .env file"
        fi
    fi
    
    # Check for credential files in working directory
    if find . -maxdepth 2 -name "*.key" -o -name "*.pem" -o -name "*credentials*" | grep -q .; then
        warn "Credential files found in working directory"
    fi
    
    # Check .dockerignore effectiveness
    if [[ ! -f ".dockerignore" ]]; then
        error "Missing .dockerignore file - security risk!"
        ((security_issues++))
    elif ! grep -q ".env" .dockerignore 2>/dev/null; then
        error ".dockerignore does not exclude .env files - security risk!"
        ((security_issues++))
    fi
    
    if [[ $security_issues -gt 0 ]]; then
        error "$security_issues critical security issues detected"
        error "Run './docker-run-enhanced.sh secure-env' to remediate"
        return 1
    else
        success "No critical security issues detected"
        return 0
    fi
}

secure_env() {
    banner "Security Environment Setup"
    
    log "Setting up secure environment configuration..."
    
    # Create secrets directory with proper permissions
    if [[ ! -d "secrets" ]]; then
        mkdir -p secrets
        chmod 700 secrets
        success "Created secrets directory with restrictive permissions"
    fi
    
    # Backup existing .env if it exists
    if [[ -f ".env" ]]; then
        local backup_name=".env.backup.$(date +%Y%m%d_%H%M%S)"
        mv .env "$backup_name"
        log "Backed up existing .env to $backup_name"
    fi
    
    # Create secure .env template
    cat > .env << 'EOF'
# ChromoForge Secure Configuration
# WARNING: Never commit actual credentials to version control
# Use Docker secrets for production deployments

# Google API Configuration (Use secrets in production)
GOOGLE_API_KEY=your_api_key_here

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key

# Application Settings
ENABLE_ULTRA_THINK=true
CONFIDENCE_THRESHOLD=0.7
MAX_CONCURRENT_REQUESTS=10
LOG_LEVEL=INFO
OBFUSCATION_METHOD=black_box

# Environment
CHROME_FORGE_ENV=development
NODE_ENV=development
EOF
    
    # Create secure .dockerignore if it doesn't exist
    if [[ ! -f ".dockerignore" ]]; then
        cp .dockerignore.secure .dockerignore 2>/dev/null || cat > .dockerignore << 'EOF'
# Security-focused .dockerignore
.env*
*.env
secrets/
*.key
*.pem
*.crt
credentials.json
auth.json
original-pdf-examples/
processed/
batch-results/
test-results/
EOF
        success "Created secure .dockerignore"
    fi
    
    success "Secure environment template created"
    warn "Please edit .env with your actual credentials"
    warn "Never commit .env to version control"
    info "For production, use Docker secrets: ./docker-run-enhanced.sh setup-secrets"
}

setup_secrets() {
    banner "Docker Secrets Setup"
    
    log "Setting up Docker secrets for production..."
    
    if [[ ! -d "secrets" ]]; then
        mkdir -p secrets
        chmod 700 secrets
    fi
    
    # Interactive secret setup
    read -s -p "Enter Google API Key: " google_key
    echo
    read -s -p "Enter Supabase URL: " supabase_url
    echo
    read -s -p "Enter Supabase Anon Key: " supabase_anon
    echo
    read -s -p "Enter Supabase Service Key: " supabase_service
    echo
    
    # Write secrets to files with proper permissions
    echo "$google_key" > secrets/google_api_key.txt
    echo "$supabase_url" > secrets/supabase_url.txt
    echo "$supabase_anon" > secrets/supabase_anon_key.txt
    echo "$supabase_service" > secrets/supabase_service_key.txt
    
    chmod 600 secrets/*.txt
    
    success "Secrets configured securely"
    info "Use docker-compose -f docker-compose.secrets.yml for production"
}

# Cleanup functions
get_docker_usage() {
    docker system df --format "table {{.Type}}\t{{.Total}}\t{{.Active}}\t{{.Size}}\t{{.Reclaimable}}"
}

cleanup_build_cache() {
    banner "Docker Build Cache Cleanup"
    
    log "Analyzing build cache usage..."
    local before_usage=$(docker system df --format "{{.Size}}" | head -n4 | tail -n1)
    
    log "Current build cache usage: $before_usage"
    
    read -p "Proceed with build cache cleanup? This will remove ALL build cache. (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log "Build cache cleanup cancelled"
        return 0
    fi
    
    log "Cleaning Docker build cache..."
    docker builder prune --all --force
    
    local after_usage=$(docker system df --format "{{.Size}}" | head -n4 | tail -n1)
    success "Build cache cleaned"
    info "Before: $before_usage | After: $after_usage"
    
    log "Estimated recovery: ~15GB"
}

cleanup_images() {
    banner "Docker Images Cleanup"
    
    log "Analyzing image usage..."
    local unused_images=$(docker images -f "dangling=true" -q | wc -l)
    local all_images=$(docker images -q | wc -l)
    
    info "Total images: $all_images"
    info "Unused images: $unused_images"
    
    if [[ $unused_images -eq 0 ]]; then
        success "No unused images to clean"
        return 0
    fi
    
    log "This will remove ALL unused images (not just dangling)"
    warn "You will need to rebuild images after this operation"
    read -p "Proceed with image cleanup? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log "Image cleanup cancelled"
        return 0
    fi
    
    log "Removing unused Docker images..."
    docker image prune --all --force
    
    success "Unused images removed"
    log "Estimated recovery: ~17GB"
}

cleanup_volumes() {
    banner "Docker Volumes Cleanup"
    
    log "Analyzing volume usage..."
    local unused_volumes=$(docker volume ls -qf dangling=true | wc -l)
    
    if [[ $unused_volumes -eq 0 ]]; then
        success "No unused volumes to clean"
        return 0
    fi
    
    info "Unused volumes found: $unused_volumes"
    warn "This will permanently delete unused volume data"
    read -p "Proceed with volume cleanup? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log "Volume cleanup cancelled"
        return 0
    fi
    
    log "Removing unused Docker volumes..."
    docker volume prune --force
    
    success "Unused volumes removed"
    log "Estimated recovery: ~173MB"
}

cleanup_containers() {
    banner "Docker Containers Cleanup"
    
    log "Analyzing container usage..."
    local stopped_containers=$(docker ps -aq -f status=exited | wc -l)
    
    if [[ $stopped_containers -eq 0 ]]; then
        success "No stopped containers to clean"
        return 0
    fi
    
    info "Stopped containers found: $stopped_containers"
    
    log "Removing stopped containers..."
    docker container prune --force
    
    success "Stopped containers removed"
    log "Estimated recovery: ~9MB"
}

cleanup_networks() {
    banner "Docker Networks Cleanup"
    
    log "Removing unused networks..."
    local removed_networks=$(docker network prune --force 2>&1 | grep -c "Deleted Networks" || echo "0")
    
    if [[ $removed_networks -gt 0 ]]; then
        success "Removed $removed_networks unused networks"
    else
        success "No unused networks to remove"
    fi
}

cleanup_comprehensive() {
    banner "Comprehensive Docker Cleanup"
    
    # Pre-cleanup validation
    check_docker
    
    log "Starting comprehensive Docker cleanup process..."
    warn "This will remove unused containers, images, volumes, networks, and build cache"
    warn "Ensure you have backed up any important data"
    
    # Show current usage
    log "Current Docker space usage:"
    get_docker_usage
    
    # Final confirmation
    echo
    read -p "Proceed with comprehensive cleanup? This action cannot be undone. (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log "Comprehensive cleanup cancelled"
        return 0
    fi
    
    # Create backup metadata
    mkdir -p .cleanup-backup
    docker images > .cleanup-backup/images-before-$(date +%Y%m%d_%H%M%S).txt
    docker ps -a > .cleanup-backup/containers-before-$(date +%Y%m%d_%H%M%S).txt
    docker system df > .cleanup-backup/usage-before-$(date +%Y%m%d_%H%M%S).txt
    
    # Execute cleanup sequence (order matters for safety)
    log "Executing cleanup sequence..."
    
    # 1. Stop running services first
    if docker-compose ps -q | head -n 1 | read; then
        warn "Stopping ChromoForge services..."
        docker-compose down || true
    fi
    
    # 2. Containers (safest)
    cleanup_containers
    
    # 3. Networks (low risk)
    cleanup_networks
    
    # 4. Volumes (data loss risk)
    cleanup_volumes
    
    # 5. Images (rebuild required)
    cleanup_images
    
    # 6. Build cache (highest impact)
    cleanup_build_cache
    
    # Show results
    log "Post-cleanup Docker space usage:"
    get_docker_usage
    
    # Save post-cleanup state
    docker images > .cleanup-backup/images-after-$(date +%Y%m%d_%H%M%S).txt
    docker ps -a > .cleanup-backup/containers-after-$(date +%Y%m%d_%H%M%S).txt
    docker system df > .cleanup-backup/usage-after-$(date +%Y%m%d_%H%M%S).txt
    
    success "Comprehensive cleanup completed!"
    info "Backup metadata saved to .cleanup-backup/"
    
    # Test build capability
    test_build_capability
}

test_build_capability() {
    log "Testing build capability post-cleanup..."
    
    if docker build -t chromoforge-test . > /dev/null 2>&1; then
        success "Build capability verified"
        docker rmi chromoforge-test > /dev/null 2>&1
    else
        error "Build capability compromised - manual intervention required"
        info "Try: ./docker-run-enhanced.sh build"
    fi
}

# AgentOS compliance functions
agentOS_check() {
    banner "AgentOS Compliance Assessment"
    
    local score=0
    local max_score=100
    local issues=()
    local successes=()
    
    log "Evaluating AgentOS compliance..."
    
    # 1. Security checks (20 points)
    if check_security; then
        score=$((score + 20))
        successes+=("Security: No exposed credentials")
    else
        issues+=("Security: Exposed credentials detected")
    fi
    
    # 2. Dockerfile best practices (25 points)
    if [[ -f "Dockerfile" ]]; then
        local dockerfile_score=0
        
        if grep -q "USER" Dockerfile; then
            dockerfile_score=$((dockerfile_score + 8))
            successes+=("Security: Non-root user configured")
        else
            issues+=("Security: No non-root user in Dockerfile")
        fi
        
        if grep -q "HEALTHCHECK" Dockerfile; then
            dockerfile_score=$((dockerfile_score + 8))
            successes+=("Reliability: Health check configured")
        else
            issues+=("Reliability: No health check in Dockerfile")
        fi
        
        if grep -q "FROM.*as.*" Dockerfile; then
            dockerfile_score=$((dockerfile_score + 9))
            successes+=("Optimization: Multi-stage build detected")
        else
            issues+=("Optimization: No multi-stage build")
        fi
        
        score=$((score + dockerfile_score))
    else
        issues+=("Critical: Dockerfile not found")
    fi
    
    # 3. Docker Compose configuration (20 points)
    if [[ -f "docker-compose.yml" ]]; then
        local compose_score=0
        
        if grep -q "resources:" docker-compose.yml; then
            compose_score=$((compose_score + 10))
            successes+=("Resources: Constraints configured")
        else
            issues+=("Resources: No resource constraints")
        fi
        
        if grep -q "healthcheck:" docker-compose.yml; then
            compose_score=$((compose_score + 5))
            successes+=("Reliability: Service health checks")
        else
            issues+=("Reliability: No service health checks")
        fi
        
        if grep -q "security_opt:" docker-compose.yml; then
            compose_score=$((compose_score + 5))
            successes+=("Security: Security options configured")
        else
            issues+=("Security: No security options")
        fi
        
        score=$((score + compose_score))
    else
        issues+=("Critical: docker-compose.yml not found")
    fi
    
    # 4. Documentation and versioning (15 points)
    local doc_score=0
    
    if [[ -f "README.md" ]]; then
        doc_score=$((doc_score + 5))
        successes+=("Documentation: README.md present")
    else
        issues+=("Documentation: Missing README.md")
    fi
    
    if [[ -f "CLAUDE.md" ]]; then
        doc_score=$((doc_score + 5))
        successes+=("Documentation: CLAUDE.md present")
    else
        issues+=("Documentation: Missing CLAUDE.md")
    fi
    
    if [[ -f "VERSION" ]]; then
        doc_score=$((doc_score + 5))
        successes+=("Versioning: VERSION file present")
    else
        issues+=("Versioning: No VERSION file")
    fi
    
    score=$((score + doc_score))
    
    # 5. Build optimization (10 points)
    if [[ -f ".dockerignore" ]]; then
        score=$((score + 5))
        successes+=("Optimization: .dockerignore present")
    else
        issues+=("Optimization: Missing .dockerignore")
    fi
    
    if [[ -f "Dockerfile.optimized" ]]; then
        score=$((score + 5))
        successes+=("Optimization: Optimized Dockerfile available")
    else
        issues+=("Optimization: No optimized Dockerfile")
    fi
    
    # 6. Development experience (10 points)
    if [[ -f "docker-run.sh" ]] || [[ -f "docker-run-enhanced.sh" ]]; then
        score=$((score + 5))
        successes+=("DevEx: Docker runner script present")
    else
        issues+=("DevEx: No Docker runner script")
    fi
    
    if grep -q "help\|--help" docker-run*.sh 2>/dev/null; then
        score=$((score + 5))
        successes+=("DevEx: Help functionality present")
    else
        issues+=("DevEx: No help functionality")
    fi
    
    # Display results
    echo
    log "AgentOS Compliance Assessment Results:"
    echo -e "${BOLD}Score: $score/$max_score${NC}"
    
    # Show successes
    if [[ ${#successes[@]} -gt 0 ]]; then
        echo
        echo -e "${GREEN}✅ Compliance Successes:${NC}"
        for success in "${successes[@]}"; do
            echo -e "   ${GREEN}• $success${NC}"
        done
    fi
    
    # Show issues
    if [[ ${#issues[@]} -gt 0 ]]; then
        echo
        echo -e "${RED}❌ Compliance Issues:${NC}"
        for issue in "${issues[@]}"; do
            echo -e "   ${RED}• $issue${NC}"
        done
    fi
    
    echo
    
    # Provide assessment
    if [[ $score -ge $AGENTOS_TARGET_SCORE ]]; then
        success "AgentOS compliance achieved! (Target: $AGENTOS_TARGET_SCORE+)"
        success "Your project meets AgentOS container-first development standards"
    elif [[ $score -ge 70 ]]; then
        warn "Good compliance, but room for improvement"
        info "Target score: $AGENTOS_TARGET_SCORE+ (Current: $score)"
        info "Focus on resolving the issues above to reach target"
    else
        error "Low compliance score - immediate attention needed"
        error "Current: $score | Target: $AGENTOS_TARGET_SCORE"
        info "Consider running: ./docker-run-enhanced.sh compliance-fix"
    fi
    
    # Improvement recommendations
    if [[ $score -lt $AGENTOS_TARGET_SCORE ]]; then
        echo
        info "Improvement recommendations:"
        info "1. Run security audit: ./docker-run-enhanced.sh security-check"
        info "2. Apply optimizations: ./docker-run-enhanced.sh optimize"
        info "3. Update documentation: Add missing README/CLAUDE.md"
        info "4. Enable health checks: Update docker-compose.yml"
    fi
}

compliance_fix() {
    banner "AgentOS Compliance Auto-Fix"
    
    log "Attempting to automatically fix compliance issues..."
    
    # Fix 1: Create VERSION file if missing
    if [[ ! -f "VERSION" ]]; then
        echo "1.0.0" > VERSION
        success "Created VERSION file"
    fi
    
    # Fix 2: Create secure .dockerignore if missing
    if [[ ! -f ".dockerignore" ]]; then
        if [[ -f ".dockerignore.secure" ]]; then
            cp .dockerignore.secure .dockerignore
            success "Applied secure .dockerignore template"
        fi
    fi
    
    # Fix 3: Secure environment setup
    if ! check_security >/dev/null 2>&1; then
        warn "Security issues detected - run manual fix:"
        info "./docker-run-enhanced.sh secure-env"
    fi
    
    # Fix 4: Copy optimized configurations if available
    if [[ -f "Dockerfile.optimized" ]] && [[ ! -f "Dockerfile.backup" ]]; then
        cp Dockerfile Dockerfile.backup
        info "Backed up original Dockerfile to Dockerfile.backup"
        info "To apply optimized Dockerfile: cp Dockerfile.optimized Dockerfile"
    fi
    
    if [[ -f "docker-compose.optimized.yml" ]]; then
        info "Optimized Docker Compose available: docker-compose.optimized.yml"
        info "To apply: cp docker-compose.optimized.yml docker-compose.yml"
    fi
    
    success "Auto-fix completed - run agentOS-check to verify improvements"
}

# Build functions with optimization
build() {
    banner "ChromoForge Build Process"
    
    check_docker
    get_version
    
    local dockerfile="Dockerfile"
    local build_args=""
    
    # Use optimized Dockerfile if available and requested
    if [[ "${OPTIMIZED:-false}" == "true" ]] && [[ -f "Dockerfile.optimized" ]]; then
        dockerfile="Dockerfile.optimized"
        info "Using optimized Dockerfile"
    fi
    
    # Enable BuildKit for better performance
    export DOCKER_BUILDKIT=1
    export BUILDKIT_PROGRESS=plain
    
    log "Building ChromoForge Docker image..."
    log "Version: $CHROMOFORGE_VERSION"
    log "Dockerfile: $dockerfile"
    
    # Build with advanced caching if cache directory exists
    if [[ -d "/tmp/.buildx-cache" ]]; then
        log "Using build cache"
        build_args="--cache-from type=local,src=/tmp/.buildx-cache --cache-to type=local,dest=/tmp/.buildx-cache-new,mode=max"
    fi
    
    if docker build \
        -f "$dockerfile" \
        --tag "chromoforge:$CHROMOFORGE_VERSION" \
        --tag "chromoforge:latest" \
        $build_args \
        .; then
        
        # Rotate cache if it exists
        if [[ -d "/tmp/.buildx-cache-new" ]]; then
            rm -rf /tmp/.buildx-cache 2>/dev/null || true
            mv /tmp/.buildx-cache-new /tmp/.buildx-cache
        fi
        
        success "Docker image built successfully!"
        info "Tags: chromoforge:$CHROMOFORGE_VERSION, chromoforge:latest"
        
        # Show image size
        local image_size=$(docker images chromoforge:$CHROMOFORGE_VERSION --format "{{.Size}}")
        info "Image size: $image_size"
        
    else
        error "Docker build failed"
        return 1
    fi
}

# System information and monitoring
system_info() {
    banner "Docker System Information"
    
    check_docker
    
    log "Docker system overview:"
    docker system info --format "Version: {{.ServerVersion}}"
    echo
    
    log "Docker space usage:"
    get_docker_usage
    echo
    
    log "Docker images:"
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedSince}}"
    echo
    
    log "Docker containers:"
    docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"
    echo
    
    log "Docker volumes:"
    docker volume ls --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}"
    echo
    
    log "Docker networks:"
    docker network ls --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}"
}

space_analysis() {
    banner "Docker Space Analysis"
    
    check_docker
    
    log "Detailed space usage analysis..."
    
    # Get detailed space breakdown
    local total_usage=$(docker system df --format "{{.Size}}" | head -n1)
    local images_usage=$(docker system df --format "{{.Size}}" | sed -n '2p')
    local containers_usage=$(docker system df --format "{{.Size}}" | sed -n '3p')
    local volumes_usage=$(docker system df --format "{{.Size}}" | sed -n '4p')
    local cache_usage=$(docker system df --format "{{.Size}}" | sed -n '5p')
    
    echo -e "${BOLD}Space Usage Breakdown:${NC}"
    echo "Total:      $total_usage"
    echo "Images:     $images_usage"
    echo "Containers: $containers_usage"
    echo "Volumes:    $volumes_usage"
    echo "Cache:      $cache_usage"
    echo
    
    # Reclaimable space analysis
    local reclaimable=$(docker system df --format "{{.Reclaimable}}" | tail -n +2 | sed 's/B//' | awk '{sum+=$1} END {print sum "B"}')
    
    if [[ "$reclaimable" != "B" ]] && [[ "$reclaimable" != "0B" ]]; then
        warn "Reclaimable space detected: $reclaimable"
        info "Run cleanup commands to recover space:"
        info "• ./docker-run-enhanced.sh cleanup-comprehensive"
        info "• ./docker-run-enhanced.sh cleanup-cache (safest)"
    else
        success "No significant reclaimable space detected"
    fi
    
    # Image analysis
    echo
    log "Image analysis:"
    local total_images=$(docker images -q | wc -l)
    local unused_images=$(docker images -f "dangling=true" -q | wc -l)
    
    echo "Total images: $total_images"
    echo "Unused images: $unused_images"
    
    if [[ $unused_images -gt 0 ]]; then
        warn "$unused_images unused images consuming space"
        info "Run: ./docker-run-enhanced.sh cleanup-images"
    fi
    
    # Container analysis
    echo
    log "Container analysis:"
    local total_containers=$(docker ps -aq | wc -l)
    local stopped_containers=$(docker ps -aq -f status=exited | wc -l)
    
    echo "Total containers: $total_containers"
    echo "Stopped containers: $stopped_containers"
    
    if [[ $stopped_containers -gt 0 ]]; then
        warn "$stopped_containers stopped containers consuming space"
        info "Run: ./docker-run-enhanced.sh cleanup-containers"
    fi
}

# Enhanced help function
show_help() {
    echo -e "${BOLD}$SCRIPT_NAME v$SCRIPT_VERSION${NC}"
    echo "AgentOS-compliant container-first development platform"
    echo
    echo -e "${BOLD}USAGE:${NC}"
    echo "  $0 <command> [arguments]"
    echo
    echo -e "${BOLD}BUILD COMMANDS:${NC}"
    echo "  build                           Build ChromoForge OCR service"
    echo "  build-optimized                 Build with optimized Dockerfile"
    echo "  build-dashboard                 Build ChromoForge Dashboard"
    echo "  build-all                       Build all services"
    echo
    echo -e "${BOLD}SERVICE MANAGEMENT:${NC}"
    echo "  start                           Start OCR service only"
    echo "  start-dashboard                 Start dashboard only"
    echo "  start-all                       Start all services"
    echo "  stop                            Stop all containers"
    echo "  restart                         Restart all services"
    echo "  status                          Show container status"
    echo
    echo -e "${BOLD}DEVELOPMENT COMMANDS:${NC}"
    echo "  dev-dashboard                   Start dashboard with hot reload"
    echo "  dev-all                         Start all services with hot reload"
    echo "  shell                           Open shell in OCR container"
    echo "  dashboard-shell                 Open shell in dashboard container"
    echo "  logs                            View OCR service logs"
    echo "  logs-all                        View all service logs"
    echo
    echo -e "${BOLD}CLEANUP COMMANDS:${NC}"
    echo "  cleanup-comprehensive           Full cleanup (~32GB recovery)"
    echo "  cleanup-cache                   Clean build cache (~15GB recovery)"
    echo "  cleanup-images                  Remove unused images (~17GB recovery)"
    echo "  cleanup-volumes                 Remove unused volumes (~173MB recovery)"
    echo "  cleanup-containers              Remove stopped containers"
    echo
    echo -e "${BOLD}SECURITY COMMANDS:${NC}"
    echo "  security-check                  Audit security configuration"
    echo "  secure-env                      Setup secure environment"
    echo "  setup-secrets                   Configure Docker secrets"
    echo
    echo -e "${BOLD}AGENTOS COMPLIANCE:${NC}"
    echo "  agentOS-check                   Assess AgentOS compliance"
    echo "  compliance-fix                  Auto-fix compliance issues"
    echo "  optimize                        Apply optimization recommendations"
    echo
    echo -e "${BOLD}MONITORING & ANALYSIS:${NC}"
    echo "  system-info                     Show Docker system information"
    echo "  space-analysis                  Analyze Docker space usage"
    echo "  health-check                    Check service health"
    echo
    echo -e "${BOLD}OCR PROCESSING:${NC}"
    echo "  process-file <file> [output]    Process a single PDF file"
    echo "  process-batch [input] [output]  Process all PDFs in directory"
    echo
    echo -e "${BOLD}EXAMPLES:${NC}"
    echo "  $0 security-check               # Audit security"
    echo "  $0 cleanup-comprehensive        # Recover ~32GB space"
    echo "  $0 agentOS-check               # Check compliance score"
    echo "  $0 build-optimized             # Build with optimization"
    echo
    echo -e "${BOLD}ENVIRONMENT VARIABLES:${NC}"
    echo "  OPTIMIZED=true                  Use optimized configurations"
    echo "  DEBUG=true                      Enable debug output"
    echo "  CHROMOFORGE_VERSION             Override version (default: from VERSION file)"
    echo
    echo -e "${CYAN}ChromoForge Medical OCR Pipeline${NC}"
    echo -e "${CYAN}AgentOS Container-First Development${NC}"
}

# Main command router
main() {
    # Global setup
    check_docker
    check_compose
    
    case "${1:-help}" in
        # Build commands
        build)
            build
            ;;
        build-optimized)
            OPTIMIZED=true build
            ;;
        build-dashboard)
            get_version
            docker-compose build chromoforge-dashboard
            success "Dashboard built: chromoforge-dashboard:$CHROMOFORGE_VERSION"
            ;;
        build-all)
            get_version
            if [[ "${OPTIMIZED:-false}" == "true" ]]; then
                log "Building all services with optimization..."
                OPTIMIZED=true build
                docker-compose build chromoforge-dashboard
            else
                docker-compose build
            fi
            success "All services built"
            ;;
            
        # Service management
        start)
            docker-compose up -d chromoforge
            success "ChromoForge service started"
            ;;
        start-dashboard)
            docker-compose up -d chromoforge-dashboard
            success "Dashboard started at http://localhost:3001"
            ;;
        start-all)
            docker-compose up -d
            success "All services started"
            ;;
        stop)
            docker-compose down
            success "All services stopped"
            ;;
        restart)
            docker-compose restart
            success "All services restarted"
            ;;
        status)
            docker-compose ps
            ;;
            
        # Development commands
        dev-dashboard)
            log "Starting dashboard with hot reload..."
            docker-compose up chromoforge-dashboard
            ;;
        dev-all)
            log "Starting all services with hot reload..."
            docker-compose up
            ;;
        shell)
            docker-compose exec chromoforge /bin/bash
            ;;
        dashboard-shell)
            docker-compose exec chromoforge-dashboard /bin/bash
            ;;
        logs)
            docker-compose logs -f chromoforge
            ;;
        logs-all)
            docker-compose logs -f
            ;;
            
        # Cleanup commands
        cleanup-comprehensive)
            cleanup_comprehensive
            ;;
        cleanup-cache)
            cleanup_build_cache
            ;;
        cleanup-images)
            cleanup_images
            ;;
        cleanup-volumes)
            cleanup_volumes
            ;;
        cleanup-containers)
            cleanup_containers
            ;;
            
        # Security commands
        security-check)
            check_security && success "Security audit passed"
            ;;
        secure-env)
            secure_env
            ;;
        setup-secrets)
            setup_secrets
            ;;
            
        # AgentOS compliance
        agentOS-check)
            agentOS_check
            ;;
        compliance-fix)
            compliance_fix
            ;;
        optimize)
            log "Applying optimization recommendations..."
            info "1. Use optimized Dockerfile: build-optimized"
            info "2. Apply optimized compose: cp docker-compose.optimized.yml docker-compose.yml"
            info "3. Clean up space: cleanup-comprehensive"
            info "4. Security setup: secure-env"
            ;;
            
        # Monitoring
        system-info)
            system_info
            ;;
        space-analysis)
            space_analysis
            ;;
        health-check)
            docker-compose ps
            docker-compose exec chromoforge python -c "import sys; import src.main; print('OCR service healthy')" 2>/dev/null || warn "OCR service unhealthy"
            curl -f http://localhost:3001/health >/dev/null 2>&1 && success "Dashboard healthy" || warn "Dashboard unhealthy"
            ;;
            
        # OCR processing
        process-file)
            shift
            if [[ -z "${1:-}" ]]; then
                error "Please provide a file path"
                echo "Usage: $0 process-file <input_file> [output_dir]"
                exit 1
            fi
            local input_file="$1"
            local output_dir="${2:-test-results}"
            log "Processing file: $input_file"
            docker-compose exec chromoforge python -m src.main --input "$input_file" --output "$output_dir"
            ;;
        process-batch)
            local input_dir="${2:-original-pdf-examples}"
            local output_dir="${3:-batch-results}"
            log "Processing batch from: $input_dir"
            docker-compose exec chromoforge python -m src.main --input "$input_dir" --output "$output_dir" --verbose
            ;;
            
        # Help and version
        version)
            get_version
            echo -e "${BOLD}$SCRIPT_NAME v$SCRIPT_VERSION${NC}"
            echo "ChromoForge version: $CHROMOFORGE_VERSION"
            echo "Docker version: $(docker --version | cut -d' ' -f3 | cut -d',' -f1)"
            echo "Docker Compose version: $(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1)"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            error "Unknown command: ${1:-}"
            echo
            show_help
            exit 1
            ;;
    esac
}

# Execute main function with all arguments
main "$@"