# ChromoForge Dashboard - Multi-stage Docker Build
# Following AgentOS container-first development standards

# Development stage with hot reload
FROM node:18-alpine AS development

# Set environment variables for Node.js
ENV NODE_ENV=development
ENV NODE_OPTIONS="--max-old-space-size=4096"

# Enable hot reload and file watching
ENV WATCHPACK_POLLING=true
ENV CHOKIDAR_USEPOLLING=true
ENV CHOKIDAR_INTERVAL=1000

# Install system dependencies for Node.js and development tools
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++ \
    bash \
    curl

# Create app directory and set ownership
WORKDIR /app

# Create non-root user for security (AgentOS best practice)
RUN addgroup -g 1001 chromoforge && \
    adduser -D -s /bin/bash -u 1001 -G chromoforge chromoforge

# Copy package files for dependency installation
COPY package*.json ./
COPY tsconfig*.json ./
COPY tailwind.config.js ./
COPY postcss.config.js ./
COPY vite.config.ts ./

# Install dependencies (npm install will create package-lock.json for future builds)
RUN npm install

# Copy source code and configuration
COPY . .

# Set proper ownership for development
RUN chown -R chromoforge:chromoforge /app

# Switch to non-root user
USER chromoforge

# Expose development port
EXPOSE 3001

# Development command with hot reload
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "3001"]

# Production build stage
FROM node:18-alpine AS build

# Set production environment
ENV NODE_ENV=production
ENV NODE_OPTIONS="--max-old-space-size=4096"

# Install system dependencies for build
RUN apk add --no-cache python3 make g++

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig*.json ./
COPY tailwind.config.js ./
COPY postcss.config.js ./
COPY vite.config.ts ./

# Install all dependencies for build process
RUN npm install

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine AS production

# Install curl for health checks
RUN apk add --no-cache curl

# Copy custom nginx configuration
COPY <<EOF /etc/nginx/conf.d/default.conf
server {
    listen 3001;
    server_name localhost;
    
    # Security headers for medical application
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "camera=(), microphone=(), geolocation=()" always;
    
    # Serve static files from build
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files \$uri \$uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # No cache for HTML files
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # API proxy to main ChromoForge service (if needed)
    location /api/ {
        proxy_pass http://chromoforge:8000/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
}
EOF

# Copy built application from build stage
COPY --from=build /app/dist /usr/share/nginx/html

# Create non-root user for security
RUN addgroup -g 1001 chromoforge && \
    adduser -D -s /bin/bash -u 1001 -G chromoforge chromoforge

# Set proper permissions
RUN chown -R chromoforge:chromoforge /usr/share/nginx/html && \
    chown -R chromoforge:chromoforge /var/cache/nginx && \
    touch /var/run/nginx.pid && \
    chown chromoforge:chromoforge /var/run/nginx.pid

# Switch to non-root user
USER chromoforge

# Expose production port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Production command
CMD ["nginx", "-g", "daemon off;"]