# ChromoForge Dashboard

A modern, accessible React dashboard for the ChromoForge Medical OCR Pipeline Management system. Built with TypeScript, Tailwind CSS, and following the ChromoForge Design System specifications.

## ✨ Features

### 🎨 ChromoForge Design System Implementation
- **Teal Gradient System**: Deep Medical Teal (#0F766E), ChromaForge Teal (#14B8A6), Mint Gradient (#5EEAD4)
- **Accent Colors**: BioLime (#84CC16), Genome Gold (#F59E0B)
- **Professional Typography**: Noto Sans Thai with Lora for logos
- **Medical-Grade Interface**: Professional, trustworthy, and precision-focused

### 🏥 Medical-Specific Features
- **Thai Language Support**: Full support for Thai text with Buddhist Era dates
- **PII Detection Indicators**: Visual indicators for personally identifiable information
- **Confidence Scoring**: Color-coded confidence levels for OCR results
- **Medical Record Management**: Comprehensive medical document tracking

### 🚀 Technical Excellence
- **React 18** with TypeScript for type safety
- **Tailwind CSS** with custom ChromoForge design tokens
- **Radix UI** components for accessibility compliance
- **TanStack Table** for advanced data grid functionality
- **React Query** for efficient data fetching and caching
- **React Router** for navigation

### ♿ Accessibility Compliance
- **WCAG 2.1 AA** color contrast ratios
- **Full keyboard navigation** support
- **Screen reader optimization** with comprehensive ARIA
- **Focus indicators** with BioLime (#84CC16) accents
- **Motion reduction** support for users with vestibular disorders

### 📱 Responsive Design
- **Mobile-first approach** with collapsible sidebar
- **Breakpoint system**: sm (640px), md (768px), lg (1024px), xl (1280px), 2xl (1536px)
- **Touch-friendly interactions** with 44px minimum touch targets
- **Adaptive layouts** that work across all device sizes

### 🔧 Component Library
- **Base Components**: Button, Card, Input, Badge, Avatar, Spinner
- **Complex Components**: DataTable, SearchInput, ConfidenceBadge, StatusBadge
- **Layout Components**: DashboardLayout, Header, Sidebar
- **Medical Components**: MedicalLoading, BuddhistEraDatePicker (planned)

## 🏗️ Project Structure

```
dashboard/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── ui/             # Base design system components
│   │   ├── features/       # Complex feature components
│   │   └── layouts/        # Layout components
│   ├── pages/              # Application pages
│   ├── styles/             # Global styles and design tokens
│   ├── types/              # TypeScript type definitions
│   ├── lib/                # Utility functions
│   └── hooks/              # Custom React hooks
├── public/                 # Static assets
└── dist/                   # Build output
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm/yarn
- Modern browser with ES2020 support

### Installation

```bash
# Navigate to dashboard directory
cd dashboard

# Install dependencies
npm install

# Start development server
npm run dev

# Open http://localhost:3001 in your browser
```

### Development Commands

```bash
# Development server
npm run dev

# Type checking
npm run type-check

# Build for production
npm run build

# Preview production build
npm run preview

# Linting
npm run lint
```

## 🎯 Pages & Features

### 📊 Dashboard Overview
- **Statistics Cards**: Total records, processing queue, confidence metrics, error rates
- **Recent Activity**: Real-time activity feed with user avatars and timestamps
- **System Status**: Live system health indicators
- **Quick Actions**: One-click access to common tasks

### 📋 Medical Records Management
- **Advanced Data Table**: Sortable, filterable, paginated medical records
- **Thai Language Support**: Dual language display (Thai/English names)
- **Buddhist Era Dates**: Thai Buddhist calendar support
- **PII Detection Status**: Visual indicators for sensitive data
- **Confidence Scoring**: Color-coded OCR confidence levels
- **Bulk Operations**: Multi-select for batch actions

### 📤 Document Upload
- **Drag & Drop Interface**: Intuitive file upload experience
- **Progress Tracking**: Real-time upload and processing status
- **Error Handling**: Clear error messages with retry functionality
- **File Validation**: PDF format and size limit enforcement

## 🎨 Design System Integration

### Color Palette
```css
/* Primary Teal System */
--color-teal-700: #0F766E  /* Deep Medical Teal */
--color-teal-500: #14B8A6  /* ChromaForge Teal */
--color-teal-300: #5EEAD4  /* Mint Gradient */

/* Accent Colors */
--color-lime: #84CC16      /* BioLime */
--color-amber: #F59E0B     /* Genome Gold */

/* Professional Grays */
--color-gray-700: #374151  /* Research Charcoal */
--color-gray-500: #6B7280  /* Lab Gray */
--color-gray-100: #F3F4F6  /* Clinical Silver */
```

### Typography
```css
/* Logo Typography - NEVER change font weight */
.chromoforge-logo {
  font-family: 'Lora', serif;
  font-weight: 400; /* CRITICAL: Normal weight only */
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Body Typography */
font-family: 'Noto Sans Thai', 'Noto Sans', system-ui, sans-serif;
```

### Component Variants
- **Medical Variants**: Special styling for medical contexts
- **Gradient Backgrounds**: Subtle gradients for premium feel
- **Confidence Indicators**: Color-coded confidence levels
- **Status Badges**: Consistent status representation

## 🔧 Component Usage Examples

### Button Component
```tsx
import { Button } from '@/components/ui/button'

// Primary medical button
<Button variant="medical" size="lg">
  Process Documents
</Button>

// Success button with icon
<Button variant="success" className="gap-2">
  <CheckCircle className="h-4 w-4" />
  Complete
</Button>
```

### Data Table
```tsx
import { DataTable } from '@/components/features/DataTable'

<DataTable
  columns={medicalRecordColumns}
  data={medicalRecords}
  searchPlaceholder="Search by patient code or name..."
  showColumnToggle={true}
  showPagination={true}
  pageSize={10}
/>
```

### Badge Components
```tsx
import { StatusBadge, ConfidenceBadge } from '@/components/ui/badge'

<StatusBadge status="completed" />
<ConfidenceBadge score={0.94} showPercentage={true} />
```

## 🌐 Internationalization

### Thai Language Support
- **Font Loading**: Optimized Noto Sans Thai font loading
- **Buddhist Era Dates**: Automatic conversion (Gregorian year + 543)
- **Cultural Adaptations**: Thai-specific UI patterns
- **RTL Preparation**: Ready for Arabic/Hebrew markets

### Date Formatting
```tsx
import { formatBuddhistDate, formatDate } from '@/lib/utils'

// Thai Buddhist Era: 15/03/2568
const thaiDate = formatBuddhistDate(new Date('2025-03-15'), 'th')

// International: Mar 15, 2025
const intlDate = formatDate(new Date('2025-03-15'), 'en')
```

## 🔒 Security Features
- **PII Detection**: Visual indicators for sensitive information
- **Data Encryption**: Support for encrypted medical data
- **Access Control**: Role-based component visibility
- **Audit Logging**: User action tracking capabilities

## 📱 Mobile Experience
- **Responsive Tables**: Tables convert to cards on mobile
- **Touch Interactions**: Optimized for touch devices
- **Collapsible Sidebar**: Space-optimized navigation
- **Mobile-First Design**: Progressively enhanced for larger screens

## 🎯 Performance Optimizations
- **Code Splitting**: Lazy loading of route components
- **Bundle Optimization**: Vendor chunks for better caching
- **Image Optimization**: WebP support with fallbacks
- **Tree Shaking**: Unused code elimination

## 🧪 Testing Strategy
- **Component Testing**: Comprehensive UI component tests
- **Accessibility Testing**: Automated a11y validation
- **Visual Regression**: Screenshot-based testing
- **Performance Testing**: Core Web Vitals monitoring

## 📚 Browser Support
- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Mobile Browsers**: iOS Safari 14+, Chrome Mobile 90+
- **Accessibility**: Full screen reader support
- **Progressive Enhancement**: Graceful degradation for older browsers

## 🤝 Contributing

### Development Guidelines
1. Follow ChromoForge Design System specifications exactly
2. Maintain WCAG 2.1 AA accessibility compliance
3. Use TypeScript for all new components
4. Follow established component patterns
5. Test across multiple devices and browsers

### Code Style
- **TypeScript**: Strict mode enabled
- **ESLint**: Airbnb configuration with React hooks
- **Prettier**: Automated code formatting
- **Naming**: PascalCase components, camelCase functions

## 📄 License
MIT License - see LICENSE file for details

## 🏥 Medical Compliance
This dashboard is designed for medical document processing and follows healthcare industry best practices for:
- **Data Privacy**: PII detection and protection
- **Security**: Encrypted data handling
- **Accessibility**: ADA/Section 508 compliance
- **Audit**: Comprehensive activity logging

---

**ChromoForge Dashboard** - Professional medical-grade interface for the modern healthcare industry.

Built with ❤️ by the ChromoForge Team