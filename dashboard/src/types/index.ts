/**
 * ChromoForge Dashboard - Type Definitions
 * Medical OCR Pipeline Management Interface Types
 */

// Base types
export interface BaseEntity {
  id: string
  createdAt: Date
  updatedAt: Date
}

// Medical Record Types
export interface MedicalRecord extends BaseEntity {
  // Patient Information
  patientCode: string
  patientNameTh?: string
  patientNameEn?: string
  dateOfBirth?: Date
  
  // Sample Information
  sampleCode?: string
  investigation?: string
  
  // Medical Facility
  hospitalName?: string
  physicianName?: string
  
  // Contact Information
  phoneNumber?: string
  email?: string
  
  // Processing Information
  documentPath: string
  originalFilename: string
  fileSize: number
  mimeType: string
  
  // OCR Results
  extractedText: string
  ocrConfidence: number
  overallConfidence: number
  
  // PII Detection
  piiDetected: PIIMatch[]
  hasPii: boolean
  
  // Processing Status
  status: ProcessingStatus
  processingStarted?: Date
  processingCompleted?: Date
  errorMessage?: string
  
  // Organization & User
  organizationId: string
  userId: string
  sessionId?: string
}

// PII Detection Types
export interface PIIMatch {
  type: PIIType
  value: string
  confidence: number
  startIndex: number
  endIndex: number
  isEncrypted: boolean
  obfuscated: boolean
}

export type PIIType = 
  | 'thai_national_id'
  | 'hospital_number'
  | 'lab_number'
  | 'thai_name'
  | 'phone_number'
  | 'email'
  | 'medical_record_number'
  | 'patient_code'
  | 'sample_code'

// Processing Status
export type ProcessingStatus = 
  | 'pending'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'draft'

// User & Authentication Types
export interface User extends BaseEntity {
  email: string
  name: string
  avatar?: string
  role: UserRole
  organizationId: string
  isActive: boolean
  lastLoginAt?: Date
  preferences: UserPreferences
}

export type UserRole = 'admin' | 'editor' | 'viewer' | 'analyst'

export interface UserPreferences {
  language: 'th' | 'en'
  theme: 'light' | 'dark' | 'system'
  dateFormat: 'gregorian' | 'buddhist'
  timezone: string
  emailNotifications: boolean
  smsNotifications: boolean
}

// Organization Types
export interface Organization extends BaseEntity {
  name: string
  nameEn: string
  nameTh: string
  logo?: string
  settings: OrganizationSettings
  isActive: boolean
}

export interface OrganizationSettings {
  defaultLanguage: 'th' | 'en'
  allowedFileTypes: string[]
  maxFileSize: number
  retentionPeriod: number
  encryptionEnabled: boolean
  auditLogRetention: number
}

// Dashboard & UI Types
export interface DashboardStats {
  totalRecords: number
  recordsToday: number
  recordsThisWeek: number
  recordsThisMonth: number
  processingQueue: number
  averageConfidence: number
  piiDetectionRate: number
  errorRate: number
}

export interface TableColumn<T = any> {
  id: string
  header: string
  accessorKey?: keyof T
  cell?: (props: { row: { original: T } }) => React.ReactNode
  sortable?: boolean
  filterable?: boolean
  width?: number
  align?: 'left' | 'center' | 'right'
}

export interface TableState {
  sorting: SortingState
  filtering: FilteringState
  pagination: PaginationState
  selection: SelectionState
}

export interface SortingState {
  id: string
  desc: boolean
}

export interface FilteringState {
  [key: string]: any
}

export interface PaginationState {
  pageIndex: number
  pageSize: number
}

export interface SelectionState {
  [key: string]: boolean
}

// API Response Types
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
  error?: string
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Form Types
export interface MedicalRecordFormData {
  patientCode: string
  patientNameTh?: string
  patientNameEn?: string
  dateOfBirth?: Date
  sampleCode?: string
  investigation?: string
  hospitalName?: string
  physicianName?: string
  phoneNumber?: string
  email?: string
}

// Upload Types
export interface FileUploadProgress {
  file: File
  progress: number
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error'
  error?: string
  result?: MedicalRecord
}

// Search & Filter Types
export interface SearchFilters {
  query?: string
  status?: ProcessingStatus[]
  investigation?: string[]
  dateRange?: {
    from: Date
    to: Date
  }
  confidenceRange?: {
    min: number
    max: number
  }
  hasPii?: boolean
}

// Notification Types
export interface Notification extends BaseEntity {
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  isRead: boolean
  userId: string
  actionUrl?: string
}

// Audit Log Types
export interface AuditLog extends BaseEntity {
  action: string
  resourceType: string
  resourceId: string
  userId: string
  userEmail: string
  ipAddress: string
  userAgent: string
  metadata: Record<string, any>
  organizationId: string
}

// Configuration Types
export interface AppConfig {
  apiUrl: string
  supabaseUrl: string
  supabaseAnonKey: string
  environment: 'development' | 'staging' | 'production'
  features: {
    enhancedOcr: boolean
    databaseIntegration: boolean
    auditLogging: boolean
    piiEncryption: boolean
  }
  limits: {
    maxFileSize: number
    maxConcurrentUploads: number
    confidenceThreshold: number
  }
}