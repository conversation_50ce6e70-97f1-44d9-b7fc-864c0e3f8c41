import * as React from "react"
import * as AvatarPrimitive from "@radix-ui/react-avatar"
import { cva, type VariantProps } from "class-variance-authority"

import { cn, getInitials } from "@/lib/utils"

const avatarVariants = cva(
  "relative flex shrink-0 overflow-hidden rounded-full border-2 border-white shadow-sm",
  {
    variants: {
      size: {
        xs: "h-6 w-6 text-xs",
        sm: "h-8 w-8 text-sm",
        default: "h-10 w-10 text-base",
        lg: "h-12 w-12 text-lg",
        xl: "h-16 w-16 text-xl",
        "2xl": "h-20 w-20 text-2xl"
      },
      variant: {
        default: "bg-neutral-100",
        medical: "bg-gradient-to-br from-primary-100 to-primary-200",
        success: "bg-green-100",
        warning: "bg-amber-100",
        error: "bg-red-100"
      }
    },
    defaultVariants: {
      size: "default",
      variant: "default"
    }
  }
)

export interface AvatarProps
  extends React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>,
    VariantProps<typeof avatarVariants> {
  src?: string
  alt?: string
  name?: string
  showStatus?: boolean
  status?: 'online' | 'offline' | 'busy' | 'away'
}

const Avatar = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Root>,
  AvatarProps
>(({ className, size, variant, src, alt, name, showStatus, status = 'offline', ...props }, ref) => {
  const initials = name ? getInitials(name) : ''

  const statusColors = {
    online: 'bg-green-500',
    offline: 'bg-neutral-400',
    busy: 'bg-red-500',
    away: 'bg-amber-500'
  }

  return (
    <div className="relative">
      <AvatarPrimitive.Root
        ref={ref}
        className={cn(avatarVariants({ size, variant }), className)}
        {...props}
      >
        <AvatarImage src={src} alt={alt || name} />
        <AvatarFallback variant={variant}>
          {initials || '?'}
        </AvatarFallback>
      </AvatarPrimitive.Root>
      
      {showStatus && (
        <div 
          className={cn(
            "absolute bottom-0 right-0 rounded-full border-2 border-white",
            statusColors[status],
            size === 'xs' && "h-2 w-2",
            size === 'sm' && "h-2.5 w-2.5",
            size === 'default' && "h-3 w-3",
            size === 'lg' && "h-3.5 w-3.5",
            size === 'xl' && "h-4 w-4",
            size === '2xl' && "h-5 w-5"
          )}
          title={`Status: ${status}`}
        />
      )}
    </div>
  )
})
Avatar.displayName = AvatarPrimitive.Root.displayName

const AvatarImage = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Image>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>
>(({ className, ...props }, ref) => (
  <AvatarPrimitive.Image
    ref={ref}
    className={cn("aspect-square h-full w-full object-cover", className)}
    {...props}
  />
))
AvatarImage.displayName = AvatarPrimitive.Image.displayName

const AvatarFallback = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Fallback>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback> & {
    variant?: 'default' | 'medical' | 'success' | 'warning' | 'error'
  }
>(({ className, variant = 'default', ...props }, ref) => {
  const fallbackColors = {
    default: "text-neutral-600",
    medical: "text-primary-700",
    success: "text-green-700",
    warning: "text-amber-700",
    error: "text-red-700"
  }

  return (
    <AvatarPrimitive.Fallback
      ref={ref}
      className={cn(
        "flex h-full w-full items-center justify-center font-medium",
        fallbackColors[variant],
        className
      )}
      {...props}
    />
  )
})
AvatarFallback.displayName = AvatarPrimitive.Fallback.displayName

// Avatar Group Component for showing multiple avatars
export interface AvatarGroupProps {
  avatars: Array<{
    src?: string
    name: string
    alt?: string
  }>
  max?: number
  size?: VariantProps<typeof avatarVariants>['size']
  variant?: VariantProps<typeof avatarVariants>['variant']
}

const AvatarGroup = React.forwardRef<HTMLDivElement, AvatarGroupProps>(
  ({ avatars, max = 3, size = 'default', variant = 'default' }, ref) => {
    const displayed = avatars.slice(0, max)
    const remaining = avatars.length - max

    return (
      <div ref={ref} className="flex -space-x-2">
        {displayed.map((avatar, index) => (
          <Avatar
            key={index}
            src={avatar.src}
            name={avatar.name}
            alt={avatar.alt}
            size={size}
            variant={variant}
            className="ring-2 ring-white"
          />
        ))}
        
        {remaining > 0 && (
          <div
            className={cn(
              avatarVariants({ size, variant }),
              "flex items-center justify-center bg-neutral-200 text-neutral-600 font-medium ring-2 ring-white"
            )}
          >
            +{remaining}
          </div>
        )}
      </div>
    )
  }
)
AvatarGroup.displayName = "AvatarGroup"

export { Avatar, AvatarImage, AvatarFallback, AvatarGroup, avatarVariants }