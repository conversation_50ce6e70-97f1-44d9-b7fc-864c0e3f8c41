import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { Loader2 } from "lucide-react"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-medical focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-lime-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        primary: [
          "bg-gradient-to-r from-primary-700 to-primary-500 text-white shadow-card",
          "hover:from-primary-800 hover:to-primary-600 hover:shadow-lg hover:-translate-y-0.5",
          "active:scale-[0.98]"
        ],
        secondary: [
          "border-2 border-primary-700 bg-transparent text-primary-700",
          "hover:bg-primary-50 hover:border-primary-800",
          "active:bg-primary-100"
        ],
        success: [
          "bg-green-600 text-white shadow-card",
          "hover:bg-green-700 hover:shadow-lg",
          "active:scale-[0.98]"
        ],
        destructive: [
          "bg-red-600 text-white shadow-card",
          "hover:bg-red-700 hover:shadow-lg",
          "active:scale-[0.98]"
        ],
        outline: [
          "border border-neutral-300 bg-transparent text-neutral-700",
          "hover:bg-neutral-50 hover:border-neutral-400",
          "focus:border-primary-500"
        ],
        ghost: [
          "text-neutral-700 bg-transparent",
          "hover:bg-neutral-100 hover:text-neutral-900",
          "focus:bg-neutral-100"
        ],
        link: [
          "text-primary-700 underline-offset-4",
          "hover:underline hover:text-primary-800",
          "focus:underline"
        ],
        // Medical-specific variants
        medical: [
          "bg-gradient-to-br from-primary-700 via-primary-600 to-primary-500 text-white shadow-card",
          "hover:shadow-lg hover:shadow-primary-200/50 hover:-translate-y-0.5",
          "active:scale-[0.98]"
        ],
        lime: [
          "bg-lime-500 text-white shadow-card",
          "hover:bg-lime-600 hover:shadow-lg",
          "active:scale-[0.98]"
        ]
      },
      size: {
        xs: "h-7 px-2 text-xs",
        sm: "h-8 px-3 text-sm",
        default: "h-10 px-4 py-2",
        lg: "h-12 px-6 text-base",
        xl: "h-14 px-8 text-lg",
        icon: "h-10 w-10",
        "icon-sm": "h-8 w-8",
        "icon-lg": "h-12 w-12"
      }
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
  loadingText?: string
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, loading = false, loadingText, children, disabled, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            {loadingText || "Loading..."}
          </>
        )}
        {!loading && children}
      </Comp>
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }