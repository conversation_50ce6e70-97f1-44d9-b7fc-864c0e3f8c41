import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent bg-primary-600 text-white hover:bg-primary-700",
        secondary: "border-transparent bg-neutral-100 text-neutral-800 hover:bg-neutral-200",
        destructive: "border-transparent bg-red-600 text-white hover:bg-red-700",
        outline: "border-neutral-300 text-neutral-700 hover:bg-neutral-50",
        success: "border-transparent bg-green-600 text-white hover:bg-green-700",
        warning: "border-transparent bg-amber-500 text-white hover:bg-amber-600",
        info: "border-transparent bg-blue-600 text-white hover:bg-blue-700",
        // Medical-specific variants
        medical: "border-transparent bg-gradient-to-r from-primary-600 to-primary-500 text-white shadow-sm",
        confidence: "border-transparent bg-primary-100 text-primary-800",
        status: "border-neutral-200 text-neutral-700 bg-white",
        // Investigation type badges
        investigation: "border-primary-200 bg-primary-50 text-primary-700 font-medium",
        // PII related badges
        pii: "border-red-200 bg-red-50 text-red-700",
        encrypted: "border-green-200 bg-green-50 text-green-700"
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        default: "px-2.5 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default"
    }
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  icon?: React.ReactNode
  removable?: boolean
  onRemove?: () => void
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant, size, icon, removable, onRemove, children, ...props }, ref) => {
    return (
      <div 
        className={cn(badgeVariants({ variant, size }), className)} 
        ref={ref} 
        {...props}
      >
        {icon && <span className="mr-1">{icon}</span>}
        {children}
        {removable && onRemove && (
          <button
            type="button"
            onClick={onRemove}
            className="ml-1 hover:bg-black/10 rounded-full p-0.5 transition-colors"
          >
            <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        )}
      </div>
    )
  }
)
Badge.displayName = "Badge"

// Status Badge Component
export interface StatusBadgeProps extends Omit<BadgeProps, 'variant'> {
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'draft'
}

const StatusBadge = React.forwardRef<HTMLDivElement, StatusBadgeProps>(
  ({ status, ...props }, ref) => {
    const getStatusConfig = (status: string) => {
      switch (status.toLowerCase()) {
        case 'completed':
          return { variant: 'success' as const, text: 'Completed' }
        case 'processing':
          return { variant: 'info' as const, text: 'Processing' }
        case 'pending':
          return { variant: 'warning' as const, text: 'Pending' }
        case 'failed':
          return { variant: 'destructive' as const, text: 'Failed' }
        case 'draft':
          return { variant: 'secondary' as const, text: 'Draft' }
        default:
          return { variant: 'secondary' as const, text: status }
      }
    }

    const config = getStatusConfig(status)

    return (
      <Badge ref={ref} variant={config.variant} {...props}>
        {config.text}
      </Badge>
    )
  }
)
StatusBadge.displayName = "StatusBadge"

// Confidence Badge Component
export interface ConfidenceBadgeProps extends Omit<BadgeProps, 'variant' | 'children'> {
  score: number
  showPercentage?: boolean
}

const ConfidenceBadge = React.forwardRef<HTMLDivElement, ConfidenceBadgeProps>(
  ({ score, showPercentage = true, ...props }, ref) => {
    const getConfidenceConfig = (score: number) => {
      const percentage = Math.round(score * 100)
      
      if (score >= 0.8) {
        return { 
          variant: 'success' as const, 
          text: showPercentage ? `${percentage}%` : 'High',
          level: 'High Confidence'
        }
      } else if (score >= 0.6) {
        return { 
          variant: 'warning' as const, 
          text: showPercentage ? `${percentage}%` : 'Medium',
          level: 'Medium Confidence'
        }
      } else {
        return { 
          variant: 'destructive' as const, 
          text: showPercentage ? `${percentage}%` : 'Low',
          level: 'Low Confidence'
        }
      }
    }

    const config = getConfidenceConfig(score)

    return (
      <Badge 
        ref={ref} 
        variant={config.variant} 
        title={config.level}
        {...props}
      >
        {config.text}
      </Badge>
    )
  }
)
ConfidenceBadge.displayName = "ConfidenceBadge"

export { Badge, StatusBadge, ConfidenceBadge, badgeVariants }