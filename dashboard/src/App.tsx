import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { DashboardLayout } from './components/layouts/DashboardLayout'
import { DashboardPage } from './pages/DashboardPage'
import { MedicalRecordsPage } from './pages/MedicalRecordsPage'
import { UploadPage } from './pages/UploadPage'

function App() {
  return (
    <div className="App">
      <Routes>
        {/* Redirect root to dashboard */}
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        
        {/* Dashboard routes */}
        <Route path="/" element={<DashboardLayout />}>
          <Route path="dashboard" element={<DashboardPage />} />
          <Route path="records" element={<MedicalRecordsPage />} />
          <Route path="upload" element={<UploadPage />} />
          
          {/* Catch-all route */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Route>
      </Routes>
    </div>
  )
}

export default App