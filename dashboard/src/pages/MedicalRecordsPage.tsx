import React from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { 
  Edit, 
  Eye, 
  FileText, 
  Filter, 
  MoreHorizontal, 
  Plus, 
  Shield, 
  Trash2 
} from 'lucide-react'
import { MedicalRecord } from '@/types'
import { DataTable, DataTableColumnHeader } from '@/components/features/DataTable'
import { Button } from '@/components/ui/button'
import { Badge, StatusBadge, ConfidenceBadge } from '@/components/ui/badge'
import { Avatar } from '@/components/ui/avatar'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { formatDate, formatBuddhistDate } from '@/lib/utils'

// Mock data for medical records
const mockMedicalRecords: MedicalRecord[] = [
  {
    id: '1',
    patientCode: 'TT00123',
    patientNameTh: 'นายสมชาย ใจดี',
    patientNameEn: 'Mr. So<PERSON><PERSON><PERSON>',
    dateOfBirth: new Date('1985-03-15'),
    sampleCode: 'SAM001',
    investigation: 'K-TRACK',
    hospitalName: 'โรงพยาบาลจุฬาลงกรณ์',
    physicianName: 'Dr. Siriporn Tanaka',
    phoneNumber: '************',
    email: '<EMAIL>',
    documentPath: '/uploads/medical-001.pdf',
    originalFilename: 'medical-report-001.pdf',
    fileSize: 2048576,
    mimeType: 'application/pdf',
    extractedText: 'Medical report content...',
    ocrConfidence: 0.94,
    overallConfidence: 0.91,
    piiDetected: [],
    hasPii: true,
    status: 'completed',
    processingStarted: new Date('2024-01-15T10:00:00'),
    processingCompleted: new Date('2024-01-15T10:05:00'),
    organizationId: 'org-1',
    userId: 'user-1',
    sessionId: 'session-1',
    createdAt: new Date('2024-01-15T10:00:00'),
    updatedAt: new Date('2024-01-15T10:05:00'),
  },
  {
    id: '2',
    patientCode: 'TT00124',
    patientNameTh: 'นางสาวมาลี รักดี',
    patientNameEn: 'Miss Malee Rakdee',
    dateOfBirth: new Date('1992-07-22'),
    sampleCode: 'SAM002',
    investigation: 'SPOT-MAS',
    hospitalName: 'โรงพยาบาลศิริราช',
    physicianName: 'Dr. Nattapong Chen',
    phoneNumber: '************',
    documentPath: '/uploads/medical-002.pdf',
    originalFilename: 'medical-report-002.pdf',
    fileSize: 1524288,
    mimeType: 'application/pdf',
    extractedText: 'Medical report content...',
    ocrConfidence: 0.88,
    overallConfidence: 0.85,
    piiDetected: [],
    hasPii: true,
    status: 'processing',
    processingStarted: new Date('2024-01-15T11:00:00'),
    organizationId: 'org-1',
    userId: 'user-2',
    sessionId: 'session-2',
    createdAt: new Date('2024-01-15T11:00:00'),
    updatedAt: new Date('2024-01-15T11:02:00'),
  },
  {
    id: '3',
    patientCode: 'TT00125',
    patientNameTh: 'นายพงศ์ศักดิ์ มั่นคง',
    patientNameEn: 'Mr. Pongsak Mankong',
    dateOfBirth: new Date('1978-12-03'),
    sampleCode: 'SAM003',
    investigation: 'K4CARE',
    hospitalName: 'โรงพยาบาลรามาธิบดี',
    physicianName: 'Dr. Ploy Suwannarat',
    phoneNumber: '************',
    documentPath: '/uploads/medical-003.pdf',
    originalFilename: 'medical-report-003.pdf',
    fileSize: 3072512,
    mimeType: 'application/pdf',
    extractedText: 'Medical report content...',
    ocrConfidence: 0.76,
    overallConfidence: 0.74,
    piiDetected: [],
    hasPii: false,
    status: 'failed',
    processingStarted: new Date('2024-01-15T12:00:00'),
    errorMessage: 'Low OCR confidence detected',
    organizationId: 'org-1',
    userId: 'user-1',
    sessionId: 'session-3',
    createdAt: new Date('2024-01-15T12:00:00'),
    updatedAt: new Date('2024-01-15T12:03:00'),
  },
]

// Define columns for the medical records table
const columns: ColumnDef<MedicalRecord>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <input
        type="checkbox"
        checked={table.getIsAllPageRowsSelected()}
        onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
        className="h-4 w-4 rounded border-neutral-300 text-primary-600 focus:ring-primary-500"
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <input
        type="checkbox"
        checked={row.getIsSelected()}
        onChange={(e) => row.toggleSelected(e.target.checked)}
        className="h-4 w-4 rounded border-neutral-300 text-primary-600 focus:ring-primary-500"
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'patientCode',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Patient Code" />
    ),
    cell: ({ row }) => (
      <div className="font-medium text-primary-700">
        {row.getValue('patientCode')}
      </div>
    ),
  },
  {
    accessorKey: 'patientNameTh',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="ชื่อผู้ป่วย / Patient Name" />
    ),
    cell: ({ row }) => (
      <div className="space-y-1 min-w-48">
        <div className="font-medium text-neutral-900">
          {row.original.patientNameTh}
        </div>
        <div className="text-sm text-neutral-600">
          {row.original.patientNameEn}
        </div>
      </div>
    ),
  },
  {
    accessorKey: 'dateOfBirth',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="วันเกิด / DOB" />
    ),
    cell: ({ row }) => {
      const date = row.getValue('dateOfBirth') as Date
      return date ? (
        <div className="space-y-1">
          <div className="text-sm font-medium">
            {formatBuddhistDate(date, 'th')}
          </div>
          <div className="text-xs text-neutral-600">
            {formatDate(date, 'en')}
          </div>
        </div>
      ) : (
        <span className="text-neutral-400">N/A</span>
      )
    },
  },
  {
    accessorKey: 'investigation',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Investigation" />
    ),
    cell: ({ row }) => (
      <Badge variant="investigation" size="sm">
        {row.getValue('investigation')}
      </Badge>
    ),
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: 'sampleCode',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Sample Code" />
    ),
    cell: ({ row }) => (
      <div className="font-mono text-sm">
        {row.getValue('sampleCode') || 'N/A'}
      </div>
    ),
  },
  {
    accessorKey: 'overallConfidence',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Confidence" />
    ),
    cell: ({ row }) => (
      <ConfidenceBadge 
        score={row.getValue('overallConfidence')} 
        showPercentage={true}
      />
    ),
  },
  {
    accessorKey: 'hasPii',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="PII Status" />
    ),
    cell: ({ row }) => {
      const hasPii = row.getValue('hasPii')
      return (
        <div className="flex items-center gap-2">
          {hasPii ? (
            <Badge variant="pii" size="sm" className="gap-1">
              <Shield className="h-3 w-3" />
              Contains PII
            </Badge>
          ) : (
            <Badge variant="secondary" size="sm">
              Clean
            </Badge>
          )}
        </div>
      )
    },
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => <StatusBadge status={row.getValue('status')} />,
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created" />
    ),
    cell: ({ row }) => {
      const date = row.getValue('createdAt') as Date
      return (
        <div className="text-sm text-neutral-600">
          {formatBuddhistDate(date, 'th')}
        </div>
      )
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const record = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(record.id)}
            >
              Copy ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="gap-2">
              <Eye className="h-4 w-4" />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem className="gap-2">
              <Edit className="h-4 w-4" />
              Edit Record
            </DropdownMenuItem>
            <DropdownMenuItem className="gap-2">
              <FileText className="h-4 w-4" />
              Download PDF
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="gap-2 text-red-600">
              <Trash2 className="h-4 w-4" />
              Delete Record
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]

export function MedicalRecordsPage() {
  const [data] = React.useState<MedicalRecord[]>(mockMedicalRecords)

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-neutral-900">Medical Records</h1>
          <p className="text-neutral-600 mt-1">
            Manage and review processed medical documents with PII detection
          </p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="gap-2">
            <Filter className="h-4 w-4" />
            Advanced Filters
          </Button>
          <Button variant="primary" className="gap-2">
            <Plus className="h-4 w-4" />
            Add Record
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Records</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.length}</div>
            <p className="text-xs text-neutral-600">All time</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {data.filter(r => r.status === 'completed').length}
            </div>
            <p className="text-xs text-neutral-600">Successfully processed</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Processing</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-600">
              {data.filter(r => r.status === 'processing').length}
            </div>
            <p className="text-xs text-neutral-600">In progress</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Contains PII</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {data.filter(r => r.hasPii).length}
            </div>
            <p className="text-xs text-neutral-600">Requires attention</p>
          </CardContent>
        </Card>
      </div>

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={data}
        searchPlaceholder="Search by patient code, name, or sample code..."
        showColumnToggle={true}
        showFilters={true}
        showPagination={true}
        pageSize={10}
      />
    </div>
  )
}