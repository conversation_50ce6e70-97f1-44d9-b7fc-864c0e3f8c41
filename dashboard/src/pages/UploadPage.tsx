import React from 'react'
import { Upload, FileText, CheckCircle, AlertCircle, X } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { MedicalLoading } from '@/components/ui/spinner'

interface UploadFile {
  id: string
  file: File
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error'
  progress: number
  error?: string
}

export function UploadPage() {
  const [files, setFiles] = React.useState<UploadFile[]>([])
  const [dragOver, setDragOver] = React.useState(false)

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    const droppedFiles = Array.from(e.dataTransfer.files)
    handleFiles(droppedFiles)
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFiles = Array.from(e.target.files)
      handleFiles(selectedFiles)
    }
  }

  const handleFiles = (fileList: File[]) => {
    const newFiles: UploadFile[] = fileList
      .filter(file => file.type === 'application/pdf')
      .map(file => ({
        id: Math.random().toString(36).substr(2, 9),
        file,
        status: 'pending' as const,
        progress: 0,
      }))

    setFiles(prev => [...prev, ...newFiles])
    
    // Simulate upload process for each file
    newFiles.forEach(uploadFile => {
      simulateUpload(uploadFile.id)
    })
  }

  const simulateUpload = (fileId: string) => {
    setFiles(prev => prev.map(f => 
      f.id === fileId ? { ...f, status: 'uploading' as const } : f
    ))

    // Simulate upload progress
    const interval = setInterval(() => {
      setFiles(prev => prev.map(f => {
        if (f.id === fileId && f.status === 'uploading') {
          const newProgress = f.progress + Math.random() * 20
          if (newProgress >= 100) {
            clearInterval(interval)
            return { ...f, progress: 100, status: 'processing' as const }
          }
          return { ...f, progress: newProgress }
        }
        return f
      }))
    }, 500)

    // Simulate processing completion
    setTimeout(() => {
      setFiles(prev => prev.map(f => 
        f.id === fileId && f.status === 'processing' 
          ? { ...f, status: Math.random() > 0.2 ? 'completed' : 'error', error: Math.random() > 0.2 ? undefined : 'Low OCR confidence detected' }
          : f
      ))
    }, 3000)
  }

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId))
  }

  const retryFile = (fileId: string) => {
    setFiles(prev => prev.map(f => 
      f.id === fileId ? { ...f, status: 'pending', progress: 0, error: undefined } : f
    ))
    simulateUpload(fileId)
  }

  const completedFiles = files.filter(f => f.status === 'completed').length
  const processingFiles = files.filter(f => f.status === 'uploading' || f.status === 'processing').length
  const errorFiles = files.filter(f => f.status === 'error').length

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-neutral-900">Upload Documents</h1>
          <p className="text-neutral-600 mt-1">
            Upload medical documents for OCR processing and PII detection
          </p>
        </div>
      </div>

      {/* Upload Statistics */}
      {files.length > 0 && (
        <div className="grid gap-4 sm:grid-cols-3">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{completedFiles}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Processing</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-amber-600">{processingFiles}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Errors</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{errorFiles}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Upload Area */}
      <Card className="border-2 border-dashed">
        <CardContent className="p-8">
          <div
            className={`text-center space-y-4 transition-colors ${
              dragOver ? 'bg-primary-50 border-primary-300' : ''
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <div className="mx-auto h-16 w-16 rounded-full bg-primary-100 flex items-center justify-center">
              <Upload className="h-8 w-8 text-primary-600" />
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-neutral-900">
                Drop your medical documents here
              </h3>
              <p className="text-neutral-600">
                or click to browse your files
              </p>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-neutral-500">
                Supported formats: PDF files only
              </p>
              <p className="text-sm text-neutral-500">
                Maximum file size: 10MB per file
              </p>
            </div>
            
            <input
              type="file"
              id="file-upload"
              className="hidden"
              multiple
              accept=".pdf"
              onChange={handleFileSelect}
            />
            <label htmlFor="file-upload">
              <Button variant="primary" className="cursor-pointer" asChild>
                <span>Choose Files</span>
              </Button>
            </label>
          </div>
        </CardContent>
      </Card>

      {/* File List */}
      {files.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Upload Progress</CardTitle>
            <CardDescription>
              Track the progress of your document uploads and processing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {files.map((uploadFile) => (
                <div
                  key={uploadFile.id}
                  className="flex items-center gap-4 p-4 border border-neutral-200 rounded-lg"
                >
                  <div className="flex-shrink-0">
                    <FileText className="h-8 w-8 text-primary-600" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <p className="font-medium text-neutral-900 truncate">
                        {uploadFile.file.name}
                      </p>
                      <div className="flex items-center gap-2">
                        {uploadFile.status === 'completed' && (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        )}
                        {uploadFile.status === 'error' && (
                          <AlertCircle className="h-5 w-5 text-red-600" />
                        )}
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => removeFile(uploadFile.id)}
                          className="h-6 w-6"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <p className="text-sm text-neutral-600">
                        {(uploadFile.file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                      
                      {uploadFile.status === 'pending' && (
                        <Badge variant="secondary">Pending</Badge>
                      )}
                      
                      {uploadFile.status === 'uploading' && (
                        <div className="flex items-center gap-2">
                          <div className="w-32 h-2 bg-neutral-200 rounded-full">
                            <div 
                              className="h-2 bg-primary-600 rounded-full transition-all"
                              style={{ width: `${uploadFile.progress}%` }}
                            />
                          </div>
                          <span className="text-xs text-neutral-600">
                            {Math.round(uploadFile.progress)}%
                          </span>
                        </div>
                      )}
                      
                      {uploadFile.status === 'processing' && (
                        <div className="flex items-center gap-2">
                          <Badge variant="info">Processing</Badge>
                          <div className="h-2 w-2 bg-primary-600 rounded-full animate-pulse" />
                        </div>
                      )}
                      
                      {uploadFile.status === 'completed' && (
                        <Badge variant="success">Completed</Badge>
                      )}
                      
                      {uploadFile.status === 'error' && (
                        <div className="flex items-center gap-2">
                          <Badge variant="destructive">Error</Badge>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => retryFile(uploadFile.id)}
                          >
                            Retry
                          </Button>
                        </div>
                      )}
                    </div>
                    
                    {uploadFile.error && (
                      <p className="text-sm text-red-600 mt-1">
                        {uploadFile.error}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            {processingFiles > 0 && (
              <div className="mt-6 p-4 bg-primary-50 rounded-lg">
                <MedicalLoading 
                  message={`Processing ${processingFiles} document${processingFiles > 1 ? 's' : ''}...`}
                  showLogo={false}
                />
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}