import React from 'react'
import { BarChart3, FileText, Shield, TrendingUp, Users, Upload } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarGroup } from '@/components/ui/avatar'

// Mock data for dashboard statistics
const dashboardStats = {
  totalRecords: 1247,
  recordsToday: 23,
  recordsThisWeek: 156,
  recordsThisMonth: 587,
  processingQueue: 8,
  averageConfidence: 0.87,
  piiDetectionRate: 0.92,
  errorRate: 0.03
}

const recentActivity = [
  {
    id: '1',
    type: 'upload',
    title: 'Medical Report - Patient TT00123',
    description: 'K-TRACK investigation completed',
    timestamp: '2 minutes ago',
    user: { name: 'Dr. Sir<PERSON><PERSON> Tanaka', avatar: undefined },
    confidence: 0.94
  },
  {
    id: '2',
    type: 'processed',
    title: 'Batch Processing Complete',
    description: '15 documents processed successfully',
    timestamp: '15 minutes ago',
    user: { name: 'System', avatar: undefined },
    confidence: 0.91
  },
  {
    id: '3',
    type: 'alert',
    title: 'High PII Content Detected',
    description: 'Document requires manual review',
    timestamp: '1 hour ago',
    user: { name: 'Auto-Detection', avatar: undefined },
    confidence: 0.78
  }
]

const teamMembers = [
  { name: 'Dr. Siriporn Tanaka', src: undefined },
  { name: 'Dr. Nattapong Chen', src: undefined },
  { name: 'Ploy Suwannarat', src: undefined },
  { name: 'Dr. Kitichai Wong', src: undefined },
]

export function DashboardPage() {
  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-neutral-900">Dashboard</h1>
          <p className="text-neutral-600 mt-1">
            Medical OCR Pipeline Management Overview
          </p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="gap-2">
            <BarChart3 className="h-4 w-4" />
            View Reports
          </Button>
          <Button variant="primary" className="gap-2">
            <Upload className="h-4 w-4" />
            Upload Documents
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <Card variant="medical" hoverable>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Records</CardTitle>
            <FileText className="h-4 w-4 text-primary-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-neutral-900">
              {dashboardStats.totalRecords.toLocaleString()}
            </div>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="success" size="sm">
                +{dashboardStats.recordsToday} today
              </Badge>
              <p className="text-xs text-neutral-600">
                {dashboardStats.recordsThisWeek} this week
              </p>
            </div>
          </CardContent>
        </Card>

        <Card variant="medical" hoverable>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing Queue</CardTitle>
            <TrendingUp className="h-4 w-4 text-amber-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-neutral-900">
              {dashboardStats.processingQueue}
            </div>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="warning" size="sm">
                In Progress
              </Badge>
              <p className="text-xs text-neutral-600">
                Est. 12 min remaining
              </p>
            </div>
          </CardContent>
        </Card>

        <Card variant="medical" hoverable>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Confidence</CardTitle>
            <Shield className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-neutral-900">
              {Math.round(dashboardStats.averageConfidence * 100)}%
            </div>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="success" size="sm">
                High Quality
              </Badge>
              <p className="text-xs text-neutral-600">
                {Math.round(dashboardStats.piiDetectionRate * 100)}% PII detected
              </p>
            </div>
          </CardContent>
        </Card>

        <Card variant="medical" hoverable>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <BarChart3 className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-neutral-900">
              {Math.round(dashboardStats.errorRate * 100)}%
            </div>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="success" size="sm">
                Excellent
              </Badge>
              <p className="text-xs text-neutral-600">
                Well below 5% target
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Recent Activity */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest medical document processing activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div
                    key={activity.id}
                    className="flex items-start gap-4 p-4 border border-neutral-200 rounded-lg hover:bg-neutral-50 transition-colors"
                  >
                    <div className="flex-shrink-0">
                      {activity.type === 'upload' && (
                        <div className="h-10 w-10 bg-primary-100 rounded-full flex items-center justify-center">
                          <Upload className="h-5 w-5 text-primary-700" />
                        </div>
                      )}
                      {activity.type === 'processed' && (
                        <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                          <FileText className="h-5 w-5 text-green-700" />
                        </div>
                      )}
                      {activity.type === 'alert' && (
                        <div className="h-10 w-10 bg-amber-100 rounded-full flex items-center justify-center">
                          <Shield className="h-5 w-5 text-amber-700" />
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="font-medium text-neutral-900 truncate">
                          {activity.title}
                        </p>
                        <Badge variant="confidence" size="sm">
                          {Math.round(activity.confidence * 100)}%
                        </Badge>
                      </div>
                      <p className="text-sm text-neutral-600 mt-1">
                        {activity.description}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <Avatar 
                          name={activity.user.name} 
                          src={activity.user.avatar}
                          size="xs"
                        />
                        <span className="text-xs text-neutral-500">
                          {activity.user.name} • {activity.timestamp}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 text-center">
                <Button variant="outline" className="w-full">
                  View All Activity
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Stats & Team */}
        <div className="space-y-6">
          {/* System Status */}
          <Card variant="gradient">
            <CardHeader>
              <CardTitle className="text-primary-800">System Status</CardTitle>
              <CardDescription className="text-primary-700">
                All systems operational
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-primary-700">OCR Processing</span>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                    <span className="text-sm font-medium text-primary-800">Online</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-primary-700">Database</span>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                    <span className="text-sm font-medium text-primary-800">Synced</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-primary-700">PII Detection</span>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                    <span className="text-sm font-medium text-primary-800">Active</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Team Members */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Active Team
              </CardTitle>
              <CardDescription>
                Medical professionals currently using the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <AvatarGroup avatars={teamMembers} max={4} size="default" />
                <div className="text-center">
                  <p className="text-sm text-neutral-600">
                    {teamMembers.length} active users
                  </p>
                  <Button variant="ghost" size="sm" className="mt-2">
                    Manage Team
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start gap-2">
                  <Upload className="h-4 w-4" />
                  Upload New Documents
                </Button>
                <Button variant="outline" className="w-full justify-start gap-2">
                  <FileText className="h-4 w-4" />
                  View All Records
                </Button>
                <Button variant="outline" className="w-full justify-start gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Generate Report
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}