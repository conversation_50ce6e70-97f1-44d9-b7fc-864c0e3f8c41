@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* ChromoForge Design System CSS Custom Properties */
    
    /* Primary Teal System */
    --color-teal-900: 19 78 74;     /* #134E4A */
    --color-teal-700: 15 118 110;   /* #0F766E - Deep Medical Teal */
    --color-teal-500: 20 184 166;   /* #14B8A6 - ChromaForge Teal */
    --color-teal-300: 94 234 212;   /* #5EEAD4 - <PERSON> Gradient */
    --color-teal-100: 204 251 241;  /* #CCFBF1 */
    --color-teal-50: 240 253 250;   /* #F0FDFA */
    
    /* Accent Colors */
    --color-lime: 132 204 22;       /* #84CC16 - BioLime */
    --color-amber: 245 158 11;      /* #F59E0B - Genome Gold */
    
    /* Professional Grays */
    --color-gray-700: 55 65 81;     /* #374151 - Research Charcoal */
    --color-gray-500: 107 114 128;  /* #6B7280 - <PERSON> */
    --color-gray-100: 243 244 246;  /* #F3F4F6 - Clinical Silver */
    --color-white: 255 255 255;     /* #FFFFFF */
    
    /* Semantic Colors */
    --color-success: 16 185 129;    /* #10B981 */
    --color-warning: 245 158 11;    /* #F59E0B */
    --color-error: 239 68 68;       /* #EF4444 */
    --color-info: 59 130 246;       /* #3B82F6 */
    
    /* Base CSS Variables for shadcn/ui */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 174 100% 29%;         /* Deep Medical Teal */
    --primary-foreground: 0 0% 100%;
    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 98%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 98%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 174 100% 29%;            /* Deep Medical Teal */
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 174 100% 50%;         /* ChromaForge Teal for dark mode */
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 174 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* ChromoForge Typography System */
  h1, h2, h3, h4, h5, h6 {
    @apply font-medium text-neutral-900;
    line-height: 1.2;
  }
  
  .chromoforge-logo {
    font-family: 'Lora', serif;
    font-weight: 400; /* NEVER change this - design system requirement */
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1;
  }
  
  /* Medical-grade focus indicators */
  .focus-visible {
    @apply outline-none ring-2 ring-lime-500 ring-offset-2;
  }
  
  /* Smooth transitions for medical interface */
  .transition-medical {
    @apply transition-all duration-200 ease-in-out;
  }
  
  /* ChromoForge gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, rgb(var(--color-teal-700)) 0%, rgb(var(--color-teal-500)) 50%, rgb(var(--color-teal-300)) 100%);
  }
  
  .gradient-background {
    background: linear-gradient(180deg, rgb(var(--color-teal-50)) 0%, rgb(var(--color-teal-100)) 100%);
  }
  
  .gradient-hero {
    background: linear-gradient(45deg, rgb(var(--color-teal-700)) 0%, rgb(var(--color-teal-500)) 100%);
  }
  
  /* Medical-grade shadows */
  .shadow-card {
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  }
  
  .shadow-modal {
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
  }
  
  .shadow-dropdown {
    box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  }
  
  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .border {
      @apply border-2;
    }
  }
  
  /* Print styles for medical documents */
  @media print {
    .no-print {
      display: none !important;
    }
    
    .print-break {
      page-break-before: always;
    }
    
    * {
      background: white !important;
      color: black !important;
    }
  }
}