# ChromoForge Practical Usage Guide

This guide provides complete step-by-step instructions for using ChromoForge to process medical PDFs and store results in Supabase database.

## 🚀 Quick Start

### Prerequisites
- Docker Desktop installed and running
- Google Gemini API key
- Supabase account (optional, for database features)

### 1. Initial Setup

```bash
# Clone and navigate to project
git clone <repository-url>
cd ChromoForge

# Copy environment template
cp .env.example .env

# Edit .env with your credentials (see Configuration section below)
nano .env  # or your preferred editor
```

### 2. Build and Start

```bash
# Build the Docker image
./docker-run.sh build

# Start the container
./docker-run.sh start

# Test the setup
./docker-run.sh test
```

## ⚙️ Configuration

Edit your `.env` file with the following required settings:

### Required Settings
```env
# Google Gemini API (Required)
GOOGLE_API_KEY=your_google_gemini_api_key_here

# Supabase Database (Required for --database flag)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### Optional Settings
```env
# Processing Configuration
CONFIDENCE_THRESHOLD=0.7
OBFUSCATION_METHOD=black_box
MAX_CONCURRENT_REQUESTS=10

# Medical Features
MEDICAL_FIELD_EXTRACTION=true
THAI_CROSS_REFERENCE=true
CONTEXTUAL_NAME_MAPPING=true
```

## 📁 File Organization

Organize your files as follows:
```
ChromoForge/
├── original-pdf-examples/    # Input PDFs go here
├── processed/               # Output files (auto-created)
├── batch-results/          # Batch processing results
└── temp/                   # Temporary files
```

## 🔧 Usage Examples

### Basic OCR Processing

#### Process Single File
```bash
# Basic OCR with PII obfuscation
./docker-run.sh process-file "original-pdf-examples/medical-report.pdf"

# Custom output directory
./docker-run.sh process-file "original-pdf-examples/medical-report.pdf" "my-results"
```

#### Process Multiple Files
```bash
# Process all PDFs in directory
./docker-run.sh process-batch

# Custom input/output directories
./docker-run.sh process-batch "my-pdfs" "my-results"
```

### Medical Data Extraction

#### Enable Medical Field Extraction
```bash
# Access container shell for advanced features
./docker-run.sh shell

# Inside container - medical extraction only
python -m src.main \
  --input "original-pdf-examples/medical-report.pdf" \
  --output "./processed" \
  --medical

# Medical extraction with custom settings
python -m src.main \
  --input "original-pdf-examples/" \
  --output "./processed" \
  --medical \
  --confidence-threshold 0.8 \
  --obfuscation-method blur \
  --verbose
```

### Database Integration

#### Store Results in Supabase
```bash
# Inside container shell
./docker-run.sh shell

# Single file with database storage
python -m src.main \
  --input "original-pdf-examples/medical-report.pdf" \
  --output "./processed" \
  --database

# Medical extraction + database storage
python -m src.main \
  --input "original-pdf-examples/medical-report.pdf" \
  --output "./processed" \
  --medical \
  --database \
  --user-id "user-123" \
  --session-id "session-456"
```

#### Batch Processing with Database
```bash
# Process directory and store all results in database
python -m src.main \
  --input "./original-pdf-examples" \
  --output "./processed" \
  --medical \
  --database \
  --max-concurrent 5 \
  --user-id "batch-user" \
  --session-id "batch-session-$(date +%s)"
```

## 📊 Understanding Output

### File Output Structure
```
processed/
├── medical-report_medical_results.json    # Extraction results
├── medical-report_obfuscated.pdf         # PII-obfuscated PDF
└── medical-report_pii_matches.json       # PII detection details
```

### Medical Extraction Fields
When using `--medical`, the system extracts:
- **Patient Code**: TT-prefixed patient identifiers
- **Sample Code**: 6-character alphanumeric codes
- **Investigation Type**: K-TRACK, SPOT-MAS, K4CARE, etc.
- **Patient Names**: Thai and English names
- **Date of Birth**: Gregorian and Buddhist Era formats
- **Contact Information**: Phone numbers, addresses
- **Physician Details**: Referring physician information

### Database Storage
When using `--database`, data is stored in Supabase with:
- Encrypted PII using organization-specific keys
- Audit logging for compliance
- Row-level security policies
- Soft deletes for data recovery
## 🔍 Advanced Usage

### Custom Processing Options
```bash
# OCR only (no obfuscation)
python -m src.main \
  --input "file.pdf" \
  --output "./processed" \
  --no-obfuscation

# Specific file patterns
python -m src.main \
  --input "./documents" \
  --output "./processed" \
  --pattern "*.pdf" \
  --no-recursive

# Custom obfuscation method
python -m src.main \
  --input "file.pdf" \
  --output "./processed" \
  --obfuscation-method blur \
  --confidence-threshold 0.9
```

### Performance Tuning
```bash
# High-performance batch processing
python -m src.main \
  --input "./large-batch" \
  --output "./processed" \
  --max-concurrent 15 \
  --medical \
  --database

# Memory-conscious processing for large files
python -m src.main \
  --input "./large-files" \
  --output "./processed" \
  --max-concurrent 3 \
  --disable-ultra-think
```

## 🔧 Troubleshooting

### Common Issues

#### 1. API Key Issues
```bash
# Check if API key is set
./docker-run.sh shell
echo $GOOGLE_API_KEY

# Test API connectivity
python -c "import google.genai as genai; genai.configure(api_key='$GOOGLE_API_KEY'); print('API key valid')"
```

#### 2. Database Connection Issues
```bash
# Test Supabase connection
python -c "
from supabase import create_client
client = create_client('$NEXT_PUBLIC_SUPABASE_URL', '$NEXT_PUBLIC_SUPABASE_ANON_KEY')
print('Database connection successful')
"
```

#### 3. File Permission Issues
```bash
# Fix file permissions
chmod -R 755 original-pdf-examples/
chmod -R 755 processed/
```

#### 4. Memory Issues
- Reduce `--max-concurrent` value
- Use `--disable-ultra-think` for faster processing
- Process files in smaller batches

### Debug Mode
```bash
# Enable verbose logging
python -m src.main \
  --input "file.pdf" \
  --output "./processed" \
  --verbose

# Check container logs
./docker-run.sh logs
```

## 📋 Command Reference

### Required Arguments
- `--input, -i`: Input PDF file or directory
- `--output, -o`: Output directory

### Feature Flags
- `--medical`: Enable medical field extraction
- `--database`: Store results in Supabase
- `--no-obfuscation`: Skip PII obfuscation

### Processing Options
- `--pattern`: File pattern (default: *.pdf)
- `--no-recursive`: Disable recursive scanning
- `--confidence-threshold`: PII detection threshold (0.0-1.0)
- `--obfuscation-method`: black_box, blur, redact, white_box, hash_pattern
- `--max-concurrent`: Concurrent processing limit

### Utility Options
- `--user-id`: User ID for audit logging
- `--session-id`: Session ID for tracking
- `--organization-id`: Organization ID for database
- `--verbose, -v`: Enable debug logging
- `--version`: Show version information

## 🎯 Best Practices

1. **Start Small**: Test with single files before batch processing
2. **Monitor Resources**: Use appropriate `--max-concurrent` values
3. **Backup Data**: Keep original PDFs safe
4. **Use Database**: Enable `--database` for production workflows
5. **Audit Logging**: Always provide `--user-id` and `--session-id`
6. **Security**: Never commit `.env` file to version control

## 📞 Getting Help

```bash
# Show help
./docker-run.sh help
python -m src.main --help

# Check version
./docker-run.sh version

# Container status
./docker-run.sh status
```
## 🎯 Real-World Workflows

### Workflow 1: Medical Lab Processing Pipeline

This workflow demonstrates processing Thai medical lab reports with full database integration:

```bash
# Step 1: Setup environment
./docker-run.sh build
./docker-run.sh start

# Step 2: Prepare your PDFs
# Place medical PDFs in original-pdf-examples/
# Example files: CSF_TT04035.pdf, Blood_Test_TT04036.pdf

# Step 3: Process with full medical extraction
./docker-run.sh shell

# Inside container:
python -m src.main \
  --input "./original-pdf-examples" \
  --output "./processed" \
  --medical \
  --database \
  --user-id "lab-technician-001" \
  --session-id "morning-batch-$(date +%Y%m%d)" \
  --confidence-threshold 0.8 \
  --max-concurrent 5 \
  --verbose

# Step 4: Review results
ls -la processed/
# Check database entries in Supabase dashboard
```

### Workflow 2: Batch PII Obfuscation for Distribution

This workflow focuses on removing PII from medical documents for safe distribution:

```bash
# Step 1: Access container
./docker-run.sh shell

# Step 2: Process with high-confidence PII detection
python -m src.main \
  --input "./sensitive-documents" \
  --output "./safe-for-distribution" \
  --confidence-threshold 0.9 \
  --obfuscation-method black_box \
  --no-recursive \
  --user-id "privacy-officer" \
  --session-id "pii-removal-$(date +%s)" \
  --verbose

# Step 3: Verify obfuscation quality
# Review _obfuscated.pdf files
# Check _pii_matches.json for detected PII
```

### Workflow 3: Research Data Extraction

This workflow extracts structured data for medical research:

```bash
# Step 1: Research-focused processing
./docker-run.sh shell

python -m src.main \
  --input "./research-pdfs" \
  --output "./research-data" \
  --medical \
  --database \
  --no-obfuscation \
  --user-id "researcher-001" \
  --session-id "study-cohort-2024" \
  --organization-id "research-institute" \
  --max-concurrent 8 \
  --verbose

# Step 2: Export structured data from Supabase
# Use Supabase dashboard or API to export medical_extractions table
```

## 🔍 Output Examples

### Medical Extraction JSON Structure
```json
{
  "patient_code": {
    "value": "TT04035",
    "confidence": "HIGH",
    "reasoning": "Clear TT prefix pattern with 5 digits",
    "alternatives": ["TT04036"]
  },
  "sample_code": {
    "value": "ABC123",
    "confidence": "HIGH",
    "reasoning": "6-character alphanumeric pattern",
    "alternatives": []
  },
  "investigation": {
    "value": "K-TRACK",
    "confidence": "HIGH",
    "reasoning": "Standard investigation type pattern",
    "alternatives": ["K-TRACK MET"]
  },
  "patient_full_name_th": {
    "value": "นายสมชาย ใจดี",
    "confidence": "MEDIUM",
    "reasoning": "Thai name pattern with title",
    "alternatives": []
  },
  "patient_full_name_en": {
    "value": "Mr. Somchai Jaidee",
    "confidence": "MEDIUM",
    "reasoning": "English name with title",
    "alternatives": []
  },
  "date_of_birth_gregorian": {
    "value": "1985-03-15",
    "confidence": "HIGH",
    "reasoning": "Standard date format",
    "alternatives": []
  },
  "date_of_birth_buddhist": {
    "value": "2528-03-15",
    "confidence": "HIGH",
    "reasoning": "Buddhist Era date format",
    "alternatives": []
  }
}
```

### PII Detection Results
```json
{
  "pii_matches": [
    {
      "type": "THAI_NATIONAL_ID",
      "text": "1234567890123",
      "confidence": 0.95,
      "coordinates": {"x": 150, "y": 200, "width": 120, "height": 15},
      "page": 1
    },
    {
      "type": "THAI_NAME",
      "text": "นายสมชาย ใจดี",
      "confidence": 0.88,
      "coordinates": {"x": 100, "y": 150, "width": 180, "height": 20},
      "page": 1
    }
  ],
  "total_pii_detected": 2,
  "processing_time": 3.2
}
```

## 🚨 Important Notes

### Security Considerations
1. **Environment Variables**: Never commit `.env` file to version control
2. **API Keys**: Rotate Google Gemini API keys regularly
3. **Database Access**: Use least-privilege principles for Supabase keys
4. **PII Handling**: Always verify obfuscation quality before distribution
5. **Audit Trails**: Maintain comprehensive logging for compliance

### Performance Guidelines
1. **File Size**: Optimal performance with files under 10MB
2. **Concurrency**: Start with `--max-concurrent 5` and adjust based on system resources
3. **Memory**: Monitor Docker memory usage during batch processing
4. **API Limits**: Be aware of Google Gemini API rate limits

### Data Quality
1. **Confidence Thresholds**: Use 0.7-0.8 for balanced accuracy
2. **Manual Review**: Always review high-stakes extractions manually
3. **Alternative Readings**: Check `alternatives` field for ambiguous text
4. **Validation**: Implement business logic validation for extracted fields

## 📈 Monitoring and Maintenance

### Regular Checks
```bash
# Check container health
./docker-run.sh status

# View recent logs
./docker-run.sh logs | tail -100

# Monitor disk usage
du -sh processed/ batch-results/ temp/

# Clean up old temporary files
find temp/ -type f -mtime +7 -delete
```

### Database Maintenance
```sql
-- Check extraction counts by date
SELECT DATE(created_at), COUNT(*)
FROM medical_extractions
GROUP BY DATE(created_at)
ORDER BY DATE(created_at) DESC;

-- Monitor PII detection rates
SELECT
  AVG(ARRAY_LENGTH(pii_matches, 1)) as avg_pii_per_document,
  COUNT(*) as total_documents
FROM medical_extractions
WHERE created_at > NOW() - INTERVAL '7 days';
```

This comprehensive guide should help you effectively use ChromoForge for medical document processing with database integration.
