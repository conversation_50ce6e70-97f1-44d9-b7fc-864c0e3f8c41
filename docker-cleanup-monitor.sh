#!/bin/bash

# ChromoForge Docker Cleanup Monitor
# Real-time monitoring during cleanup orchestration
# Version: 1.0

set -euo pipefail

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m'

# Configuration
CRITICAL_CONTAINER="chromoforge-app"
REFRESH_INTERVAL=5

# Functions
get_container_status() {
    if docker ps | grep -q "$CRITICAL_CONTAINER"; then
        echo -e "${GREEN}✓ RUNNING${NC}"
    else
        echo -e "${RED}✗ DOWN${NC}"
    fi
}

get_container_uptime() {
    docker ps --format "{{.Status}}" -f name="$CRITICAL_CONTAINER" 2>/dev/null || echo "N/A"
}

get_cpu_usage() {
    docker stats --no-stream --format "{{.CPUPerc}}" "$CRITICAL_CONTAINER" 2>/dev/null || echo "N/A"
}

get_memory_usage() {
    docker stats --no-stream --format "{{.MemUsage}}" "$CRITICAL_CONTAINER" 2>/dev/null || echo "N/A"
}

get_space_info() {
    docker system df | grep -E "Images|Containers|Build Cache|TOTAL" | while read line; do
        echo "  $line"
    done
}

get_image_count() {
    docker images -q | wc -l
}

get_container_count() {
    echo "Running: $(docker ps -q | wc -l) | Stopped: $(docker ps -aq -f status=exited | wc -l)"
}

get_volume_count() {
    docker volume ls -q | wc -l
}

get_network_count() {
    docker network ls -q | wc -l
}

format_time() {
    local seconds=$1
    printf "%02d:%02d:%02d" $((seconds/3600)) $((seconds%3600/60)) $((seconds%60))
}

# Main monitoring loop
monitor() {
    local start_time=$(date +%s)
    
    while true; do
        clear
        
        # Header
        echo -e "${CYAN}╔════════════════════════════════════════════════════════════╗${NC}"
        echo -e "${CYAN}║       ChromoForge Docker Cleanup Monitor v1.0              ║${NC}"
        echo -e "${CYAN}╚════════════════════════════════════════════════════════════╝${NC}"
        echo ""
        
        # Current time and elapsed
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        echo -e "${BLUE}Current Time:${NC} $(date '+%Y-%m-%d %H:%M:%S')"
        echo -e "${BLUE}Elapsed Time:${NC} $(format_time $elapsed)"
        echo ""
        
        # Critical Service Status
        echo -e "${MAGENTA}═══ CRITICAL SERVICE STATUS ═══${NC}"
        echo -e "Container: ${YELLOW}$CRITICAL_CONTAINER${NC}"
        echo -e "Status:    $(get_container_status)"
        echo -e "Uptime:    $(get_container_uptime)"
        echo -e "CPU:       $(get_cpu_usage)"
        echo -e "Memory:    $(get_memory_usage)"
        echo ""
        
        # Resource Overview
        echo -e "${MAGENTA}═══ RESOURCE OVERVIEW ═══${NC}"
        echo -e "Images:     $(get_image_count)"
        echo -e "Containers: $(get_container_count)"
        echo -e "Volumes:    $(get_volume_count)"
        echo -e "Networks:   $(get_network_count)"
        echo ""
        
        # Space Usage
        echo -e "${MAGENTA}═══ SPACE USAGE ═══${NC}"
        get_space_info
        echo ""
        
        # Phase Progress (if cleanup is running)
        if pgrep -f "docker-cleanup-executor.sh" > /dev/null; then
            echo -e "${MAGENTA}═══ CLEANUP IN PROGRESS ═══${NC}"
            
            # Try to determine current phase from log
            if [ -f "backup/cleanup-*/cleanup.log" ]; then
                local last_phase=$(grep -E "PHASE|Stage" backup/cleanup-*/cleanup.log | tail -1)
                echo -e "${YELLOW}Current: $last_phase${NC}"
            fi
            
            # Show recent actions
            if [ -f "backup/cleanup-*/cleanup.log" ]; then
                echo -e "\n${BLUE}Recent Actions:${NC}"
                tail -5 backup/cleanup-*/cleanup.log | sed 's/^/  /'
            fi
        else
            echo -e "${MAGENTA}═══ STATUS ═══${NC}"
            echo -e "${GREEN}Cleanup not running - System stable${NC}"
        fi
        
        echo ""
        echo -e "${CYAN}Refreshing every $REFRESH_INTERVAL seconds... (Ctrl+C to exit)${NC}"
        
        sleep $REFRESH_INTERVAL
    done
}

# Trap Ctrl+C
trap 'echo -e "\n${YELLOW}Monitor stopped by user${NC}"; exit 0' INT

# Start monitoring
echo -e "${GREEN}Starting ChromoForge Docker Cleanup Monitor...${NC}"
echo -e "${YELLOW}This will refresh every $REFRESH_INTERVAL seconds${NC}"
echo ""
sleep 2

monitor