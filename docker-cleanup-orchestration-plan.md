# ChromoForge Docker Cleanup Orchestration Plan

## Executive Summary
Comprehensive Docker cleanup orchestration to recover 32.86GB of disk space while ensuring zero production disruption for ChromoForge OCR service.

**Critical Service**: chromoforge-app (Container: 771f1a310226) - MUST REMAIN OPERATIONAL
**Space Recovery Target**: 32.86GB
**Total Duration**: 75 minutes with safety checkpoints
**Risk Level**: Low (with proper orchestration)

## Current State Analysis

### Production Service Status
- **Running Container**: chromoforge-app (6 hours uptime)
- **Image**: chromoforge:1.0.0 (2.12GB)
- **Status**: OPERATIONAL - Critical OCR service
- **Dependencies**: chromoforge-network

### Resource Inventory
```yaml
containers:
  running: 1 (critical)
  stopped: 4 (dashboard containers - safe to remove)
  
images:
  total: 22
  used: 1 (chromoforge:1.0.0)
  dangling: 1
  reclaimable: 17.5GB
  
build_cache:
  total: 15.18GB
  reclaimable: 15.18GB
  
volumes:
  active: 1 (chromoforge-dashboard-node-modules)
  
networks:
  custom: 2 (chromoforge-network, chromoforge_default)
  in_use: 1 (chromoforge-network)
```

## Multi-Phase Orchestration Plan

### PHASE 2: Strategy Validation & Approval (T+0 to T+15)

#### Agent Coordination
**Lead**: project-orchestrator (current)
**Support**: system-architect (validation)

#### Timeline
```
T+0-5:   Dependency mapping and impact analysis
T+5-10:  Safety protocol implementation
T+10-15: Stakeholder approval and checkpoint validation
```

#### Actions
1. **Service Dependency Mapping**
   ```bash
   # Map critical service dependencies
   docker inspect chromoforge-app --format '{{json .HostConfig}}' > backup/service-config.json
   docker inspect chromoforge-app --format '{{json .Config}}' > backup/service-env.json
   ```

2. **Create Protection List**
   ```yaml
   protected_resources:
     images:
       - chromoforge:1.0.0 (ID: 3b351ee1aa20)
     networks:
       - chromoforge-network (ID: d1429bd1670a)
     containers:
       - chromoforge-app (ID: 771f1a310226)
   ```

3. **Validation Checkpoint**
   - Confirm production service health
   - Verify backup procedures
   - Get approval to proceed

### PHASE 3A: Resource Backup & Safety Preparation (T+15 to T+25)

#### Agent Coordination
**Lead**: system-architect
**Support**: semantic-dev-specialist

#### Timeline
```
T+15-18: Create system state snapshot
T+18-22: Backup critical configurations
T+22-25: Implement rollback procedures
```

#### Actions
1. **System State Backup**
   ```bash
   # Create backup directory with timestamp
   BACKUP_DIR="backup/cleanup-$(date +%Y%m%d-%H%M%S)"
   mkdir -p $BACKUP_DIR
   
   # Save Docker system state
   docker system df -v > $BACKUP_DIR/system-state-before.txt
   docker ps -a > $BACKUP_DIR/containers-before.txt
   docker images -a > $BACKUP_DIR/images-before.txt
   docker volume ls > $BACKUP_DIR/volumes-before.txt
   docker network ls > $BACKUP_DIR/networks-before.txt
   ```

2. **Service Configuration Backup**
   ```bash
   # Export running container
   docker export chromoforge-app > $BACKUP_DIR/chromoforge-app-export.tar
   
   # Save image if needed for rollback
   docker save chromoforge:1.0.0 -o $BACKUP_DIR/chromoforge-image.tar
   ```

3. **Create Rollback Script**
   ```bash
   cat > $BACKUP_DIR/rollback.sh << 'EOF'
   #!/bin/bash
   # Emergency rollback script
   echo "Starting emergency rollback..."
   
   # Restore container if needed
   if [ ! "$(docker ps -q -f name=chromoforge-app)" ]; then
     docker load -i chromoforge-image.tar
     docker run -d --name chromoforge-app --network chromoforge-network chromoforge:1.0.0
   fi
   
   echo "Rollback complete. Please verify service status."
   EOF
   chmod +x $BACKUP_DIR/rollback.sh
   ```

### PHASE 3B: Coordinated Cleanup Execution (T+25 to T+55)

#### Agent Coordination
**Lead**: semantic-dev-specialist
**Support**: project-orchestrator (monitoring)

#### Timeline
```
T+25-30: Stage 1 - Stop and remove dead containers
T+30-35: Stage 2 - Clean dangling images
T+35-40: Stage 3 - Remove unused dashboard images
T+40-45: Stage 4 - Clear build cache
T+45-50: Stage 5 - Clean unused volumes and networks
T+50-55: Stage 6 - Final optimization
```

#### Staged Execution Plan

##### Stage 1: Container Cleanup (T+25-30)
```bash
# Remove stopped containers (NOT the running one)
docker container prune -f

# Specifically remove dashboard containers
docker rm 782eba593aa3 289919e1470b 2a6b1a77958a 3c874e55c899

# Verify critical service still running
docker ps | grep chromoforge-app || echo "ALERT: Critical service down!"
```
**Expected Recovery**: ~200MB
**Risk**: None (only stopped containers)

##### Stage 2: Dangling Image Cleanup (T+30-35)
```bash
# Remove dangling images
docker image prune -f

# Verify protected image still exists
docker images | grep "chromoforge:1.0.0" || echo "ALERT: Critical image missing!"
```
**Expected Recovery**: ~1GB
**Risk**: None (only untagged images)

##### Stage 3: Dashboard Image Cleanup (T+35-40)
```bash
# Remove specific dashboard images (not in use)
docker rmi chromoforge-dashboard:dev
docker rmi chromoforge-dashboard:test
docker rmi chromoforge-dashboard:fixed
docker rmi chromoforge-dashboard:1.0.0

# Keep chromoforge-dashboard:latest for potential future use
```
**Expected Recovery**: ~5GB
**Risk**: Low (unused dashboard images)

##### Stage 4: Build Cache Cleanup (T+40-45)
```bash
# Clear build cache
docker builder prune -f --all

# Alternative selective cleanup
docker builder prune -f --filter until=24h
```
**Expected Recovery**: 15.18GB
**Risk**: Medium (will slow next build)

##### Stage 5: Volume and Network Cleanup (T+45-50)
```bash
# Remove unused volumes
docker volume prune -f

# Remove unused networks (keep chromoforge-network)
docker network prune -f

# Verify critical network exists
docker network ls | grep chromoforge-network || echo "ALERT: Critical network missing!"
```
**Expected Recovery**: ~500MB
**Risk**: Low (automated detection of unused resources)

##### Stage 6: Final Optimization (T+50-55)
```bash
# Remove duplicate tags (keep primary)
docker rmi chromoforge-gnesolutions:latest  # Keep :1.0.0
docker rmi chromoforge:latest  # Keep :1.0.0

# Final system cleanup
docker system prune -f --volumes
```
**Expected Recovery**: ~2GB
**Risk**: Low (removing duplicates)

### PHASE 3C: Integrity Verification & Maintenance (T+55 to T+75)

#### Agent Coordination
**Lead**: semantic-qa-specialist
**Support**: project-orchestrator (final validation)

#### Timeline
```
T+55-60: Service health verification
T+60-65: Space recovery validation
T+65-70: Performance testing
T+70-75: Documentation and handoff
```

#### Actions

1. **Service Health Check**
   ```bash
   # Verify container is running
   docker ps | grep chromoforge-app
   
   # Check container health
   docker exec chromoforge-app echo "Health check passed"
   
   # Verify logs
   docker logs --tail 50 chromoforge-app
   
   # Test OCR functionality
   curl -X POST http://localhost:8000/health || echo "Service health check failed"
   ```

2. **Space Recovery Validation**
   ```bash
   # Compare before and after
   docker system df
   
   # Calculate actual recovery
   echo "Target: 32.86GB"
   echo "Actual: $(docker system df | grep 'Total reclaimed')"
   ```

3. **Performance Verification**
   ```bash
   # Test container response time
   time docker exec chromoforge-app echo "Performance test"
   
   # Check resource usage
   docker stats --no-stream chromoforge-app
   ```

4. **Create Maintenance Report**
   ```bash
   cat > cleanup-report-$(date +%Y%m%d).md << EOF
   # Docker Cleanup Report
   
   Date: $(date)
   
   ## Space Recovery
   - Target: 32.86GB
   - Achieved: [ACTUAL]
   
   ## Service Status
   - chromoforge-app: RUNNING
   - Uptime: $(docker ps --format "{{.Status}}" -f name=chromoforge-app)
   
   ## Resources Cleaned
   - Stopped containers: 4
   - Unused images: [COUNT]
   - Build cache: 15.18GB
   - Dangling volumes: [COUNT]
   
   ## Next Maintenance
   - Scheduled: [DATE]
   - Actions: Regular cleanup, image updates
   EOF
   ```

## Safety Protocols & Checkpoints

### Critical Checkpoints
1. **CP1 (T+15)**: Pre-cleanup validation - MUST PASS
2. **CP2 (T+25)**: Backup verification - MUST PASS
3. **CP3 (T+30)**: After container cleanup - Service check
4. **CP4 (T+45)**: After cache cleanup - Service check
5. **CP5 (T+55)**: Final validation - MUST PASS

### Rollback Triggers
- Critical service stops responding
- Protected resources accidentally removed
- Disk space not recovering as expected
- Network connectivity lost
- Performance degradation > 20%

### Emergency Procedures
```bash
# Quick service restoration
docker start chromoforge-app || \
  docker run -d --name chromoforge-app-emergency \
    --network chromoforge-network \
    chromoforge:1.0.0

# Full rollback
./backup/cleanup-*/rollback.sh
```

## Agent Handoff Protocol

### Coordination Sequence
```yaml
phase_2:
  lead: project-orchestrator
  validates_with: system-architect
  duration: 15_minutes
  deliverables:
    - Approved cleanup plan
    - Protected resource list
    - Safety protocols

phase_3a:
  handoff_from: project-orchestrator
  lead: system-architect
  supports: semantic-dev-specialist
  duration: 10_minutes
  deliverables:
    - Complete system backup
    - Rollback procedures
    - Ready for execution

phase_3b:
  handoff_from: system-architect
  lead: semantic-dev-specialist
  monitors: project-orchestrator
  duration: 30_minutes
  deliverables:
    - Staged cleanup execution
    - 32.86GB space recovered
    - Service continuity maintained

phase_3c:
  handoff_from: semantic-dev-specialist
  lead: semantic-qa-specialist
  validates_with: project-orchestrator
  duration: 20_minutes
  deliverables:
    - Service verification
    - Performance validation
    - Maintenance report
```

## Risk Mitigation Matrix

| Risk | Probability | Impact | Mitigation | Recovery |
|------|------------|--------|------------|----------|
| Service disruption | Low | Critical | Protected resource list | Immediate restart |
| Incomplete cleanup | Medium | Low | Staged execution | Continue next stage |
| Network loss | Low | High | Network preservation | Recreate network |
| Image corruption | Very Low | High | Image backup | Restore from backup |
| Cache rebuild delay | High | Low | Expected behavior | Document for users |

## Success Criteria

### Must Achieve
- ✅ chromoforge-app remains operational throughout
- ✅ Minimum 30GB space recovered
- ✅ No data loss or corruption
- ✅ Complete audit trail maintained

### Should Achieve
- ✅ 32.86GB target space recovery
- ✅ < 75 minute total duration
- ✅ Zero service interruptions
- ✅ Comprehensive documentation

### Nice to Have
- ✅ Improved container startup time
- ✅ Optimized image layers
- ✅ Automated cleanup schedule

## Post-Cleanup Maintenance Schedule

### Immediate (Day 1)
- Monitor service stability for 24 hours
- Document any issues or optimizations

### Weekly
- Run `docker system prune -f` (safe mode)
- Check for unused containers

### Monthly
- Full cleanup following this orchestration plan
- Review and update protection lists
- Optimize image builds

### Quarterly
- Architecture review for efficiency
- Update cleanup procedures
- Training for team members

## Communication Protocol

### Stakeholder Updates
- T+0: Cleanup initiated, service protected
- T+15: Strategy approved, beginning execution
- T+30: Stage 1 complete, service verified
- T+45: Major cleanup complete, validating
- T+60: Cleanup successful, final checks
- T+75: Complete with [X]GB recovered

### Alert Escalation
1. Warning: Slack notification to team
2. Error: Page on-call engineer
3. Critical: Automatic rollback + escalation

## Appendix: Quick Commands

### Pre-flight Check
```bash
# One-line safety check
docker ps | grep chromoforge-app && echo "✓ Service running" || echo "✗ Service DOWN"
```

### Progress Monitor
```bash
# Real-time space tracking
watch -n 5 'docker system df | grep "Total reclaimable"'
```

### Health Monitor
```bash
# Continuous health check during cleanup
while true; do 
  docker ps | grep chromoforge-app > /dev/null && echo "✓" || echo "✗ ALERT"
  sleep 10
done
```

---

**Document Version**: 1.0
**Created**: $(date)
**Orchestrator**: project-orchestrator
**Status**: READY FOR EXECUTION