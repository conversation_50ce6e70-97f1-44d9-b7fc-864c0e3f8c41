# ===================================================================
# ChromoForge OCR Pipeline Environment Configuration
# ===================================================================
# Copy this file to .env and fill in your actual values
# This template supports all environments (development, test, production)

# ===================================================================
# APPLICATION CONFIGURATION
# ===================================================================

# Application version and environment
CHROMOFORGE_VERSION=1.0.0
# Environment: development, test, production
ENVIRONMENT=development

# ===================================================================
# GOOGLE GEMINI API CONFIGURATION
# ===================================================================

# Required: Your Google Gemini API key
# Get from: https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your_google_gemini_api_key_here

# Gemini model configuration
GEMINI_MODEL=gemini-2.5-pro
GEMINI_TEMPERATURE=0.1
GEMINI_MAX_TOKENS=8192

# Enhanced thinking capabilities (recommended for Thai medical documents)
GEMINI_THINKING_BUDGET=-1
ENABLE_ULTRA_THINK=true
ENABLE_GOOGLE_SEARCH=true
ENABLE_URL_CONTEXT=true

# ===================================================================
# SUPABASE DATABASE CONFIGURATION
# ===================================================================

# Required: Supabase project URL
# Format: https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co

# Required: Supabase anonymous key (public, safe for client-side)
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here

# Required: Supabase service role key (private, server-side only)
# WARNING: Keep this secret! Has admin privileges
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Database transaction recording
ENABLE_DATABASE_RECORDING=true
# Optional: Default organization ID for single-tenant setups
DEFAULT_ORGANIZATION_ID=

# ===================================================================
# OCR PROCESSING CONFIGURATION
# ===================================================================

# API retry configuration
MAX_RETRIES=3
RETRY_DELAY=1.0

# Batch processing settings
BATCH_SIZE=5
MAX_CONCURRENT_REQUESTS=10

# Enhanced Thai OCR settings
THAI_CROSS_REFERENCE=true
CONTEXTUAL_NAME_MAPPING=true
MEDICAL_FIELD_EXTRACTION=true

# ===================================================================
# FILE PROCESSING CONFIGURATION
# ===================================================================

# File size limits (in MB)
MAX_FILE_SIZE_MB=50

# Supported file formats (comma-separated)
# Options: pdf, jpg, jpeg, png, tiff
SUPPORTED_FORMATS=pdf

# Directory paths (relative to project root or absolute)
OUTPUT_DIR=./processed
TEMP_DIR=./temp

# ===================================================================
# PII DETECTION & OBFUSCATION CONFIGURATION
# ===================================================================

# PII detection confidence threshold (0.0-1.0)
# Higher values = stricter detection, lower false positives
CONFIDENCE_THRESHOLD=0.7

# Enable coordinate tracking for precise obfuscation
ENABLE_COORDINATE_TRACKING=true

# Obfuscation method for PII in output PDFs
# Options: black_box, blur, redact, white_box, hash_pattern
OBFUSCATION_METHOD=black_box

# ===================================================================
# LOGGING CONFIGURATION
# ===================================================================

# Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# Log format: json, text
# json is recommended for production environments
LOG_FORMAT=json

# ===================================================================
# ENVIRONMENT-SPECIFIC OVERRIDES
# ===================================================================
# Uncomment and modify based on your environment

# Development environment settings
# GEMINI_MODEL=gemini-2.5-pro
# LOG_LEVEL=DEBUG
# ENABLE_GOOGLE_SEARCH=true
# ENABLE_URL_CONTEXT=true
# ENABLE_DATABASE_RECORDING=true

# Test environment settings
# GOOGLE_API_KEY=test_api_key_for_testing
# NEXT_PUBLIC_SUPABASE_URL=https://test.supabase.co
# NEXT_PUBLIC_SUPABASE_ANON_KEY=test_anon_key
# SUPABASE_SERVICE_ROLE_KEY=test_service_key
# GEMINI_MODEL=gemini-2.5-pro
# ENABLE_GOOGLE_SEARCH=false
# ENABLE_URL_CONTEXT=false
# ENABLE_DATABASE_RECORDING=false
# DEFAULT_ORGANIZATION_ID=test_org_id

# Production environment settings
# LOG_LEVEL=WARNING
# GEMINI_TEMPERATURE=0.05
# MAX_CONCURRENT_REQUESTS=20
# CONFIDENCE_THRESHOLD=0.8
# ENABLE_GOOGLE_SEARCH=false
# ENABLE_URL_CONTEXT=false

# ===================================================================
# SECURITY NOTES
# ===================================================================
# 
# 1. Never commit the actual .env file to version control
# 2. Keep SUPABASE_SERVICE_ROLE_KEY secret - it has admin privileges
# 3. Rotate API keys regularly for production environments
# 4. Use environment-specific keys for development, test, and production
# 5. Consider using a secret management service for production
# 
# For more details, see: README.md and CLAUDE.md
# ===================================================================