# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ChromoForge is a medical OCR pipeline application designed for processing Thai medical documents with PII detection and obfuscation. It uses Google Gemini API for OCR processing and Supabase for secure data storage.

The project has been consolidated to use a single entry point (`src/main.py`) with feature flags for enhanced functionality and database integration.

> **🐳 Docker-First Development**: ChromoForge uses Docker Desktop for containerized development. No Python virtual environment setup is required. All development commands should be run inside the Docker container using `./docker-run.sh shell`.

## Commands

### Setup and Environment
```bash
# Docker-based development (preferred)
./docker-run.sh build
./docker-run.sh start

# Test setup
./docker-run.sh test
```

### Run Tests
```bash
# Access container shell for testing
./docker-run.sh shell

# Inside container:
# Run all tests (17 comprehensive test modules)
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test markers
pytest -m integration  # Real integration tests
pytest -m performance  # Performance tests
pytest -m security     # Security tests
pytest -m slow         # Slow tests

# Run specific test file
pytest tests/test_end_to_end_real.py -v

# Run tests for specific modules
pytest tests/test_core_config_real.py -v
pytest tests/test_pii_detection_real.py -v
pytest tests/test_batch_processor_real.py -v
```

### Linting and Code Quality
```bash
# Access container shell for code quality tools
./docker-run.sh shell

# Inside container:
# Format code with black
black src/ tests/

# Sort imports
isort src/ tests/

# Run linting
flake8 src/ tests/

# Security scanning
bandit -r src/

# Type checking
mypy src/
```

### Running the OCR Pipeline

#### Basic Usage
```bash
# Process single file
./docker-run.sh process-file "original-pdf-examples/file.pdf"

# Process directory
./docker-run.sh process-batch

# For advanced options, access container shell:
./docker-run.sh shell
# Inside container:
python -m src.main --input file.pdf --output ./processed --no-obfuscation
```

#### Enhanced Features
```bash
# Access container shell for enhanced features
./docker-run.sh shell

# Inside container:
# Enable enhanced medical extraction with structured fields
python -m src.main --input file.pdf --output ./processed --enhanced

# Enable database integration (requires Supabase setup)
python -m src.main --input file.pdf --output ./processed --database

# Combine enhanced features with database
python -m src.main --input file.pdf --output ./processed --enhanced --database
```

#### Advanced Configuration
```bash
# Access container shell for advanced configuration
./docker-run.sh shell

# Inside container:
# Full featured processing
python -m src.main --input ./documents --output ./processed \
  --enhanced \
  --database \
  --confidence-threshold 0.8 \
  --obfuscation-method blur \
  --max-concurrent 5 \
  --user-id "user-123" \
  --session-id "session-456" \
  --verbose

# Directory processing with custom pattern
python -m src.main --input ./documents --output ./processed \
  --pattern "medical_*.pdf" \
  --no-recursive \
  --enhanced

# Disable enhanced OCR processing for speed
python -m src.main --input file.pdf --output ./processed \
  --disable-ultra-think \
  --max-concurrent 15
```

## Architecture

### Project Structure (Consolidated)
```
src/
├── main.py         # Single consolidated entry point
├── core/           # Core domain models and configuration
│   ├── config.py   # Pydantic settings management
│   ├── models.py   # Domain models (OCRResult, PIIMatch, etc.)
│   ├── constants.py # Application constants
│   └── exceptions.py # Custom exceptions
├── processing/     # Business logic layer
│   ├── ocr_processor.py    # Gemini OCR with ULTRA THINK
│   ├── pii_detector.py     # Thai PII detection patterns
│   ├── pdf_obfuscator.py   # PDF obfuscation methods
│   └── batch_processor.py  # Batch and concurrent processing
├── security/       # Security and compliance
│   ├── pii_encryption.py   # PII encryption utilities
│   └── audit_logger.py     # HIPAA-compliant audit logging
└── utils/          # Utilities and helpers
    ├── logging_config.py   # Structured logging setup
    └── utils.py            # Common utilities

tests/              # Comprehensive test coverage (17 modules)
├── conftest.py     # Test configuration
├── test_core_*.py  # Core module tests
├── test_*_real.py  # Integration tests with real APIs
├── test_batch_processor_real.py
├── test_end_to_end_real.py
├── test_gemini_integration_real.py
├── test_ocr_processor_real.py
├── test_pdf_obfuscation_real.py
├── test_pii_detection_real.py
├── test_security_*.py
├── test_supabase_integration_real.py
└── test_utils_*.py
```

### Key Changes from Previous Versions

1. **Consolidated Entry Point**: All functionality now accessible through `src/main.py` with feature flags
2. **Enhanced Features**: `--enhanced` flag enables advanced medical field extraction
3. **Database Integration**: `--database` flag enables Supabase integration
4. **Comprehensive Testing**: 17 test modules covering all functionality
5. **Clean Project Structure**: Removed duplicate entry points and consolidated functionality

### Key Components

1. **OCR Processing**: 
   - Google Gemini 2.0 Flash with enhanced thinking capabilities
   - Optional ULTRA THINK mode (can be disabled with `--disable-ultra-think`)
   - Enhanced medical extraction with structured field parsing

2. **Enhanced Medical Extraction** (--enhanced flag):
   - Patient codes (TT prefix patterns)
   - Sample codes (6-character alphanumeric)
   - Investigation types (K-TRACK, SPOT-MAS, K4CARE, etc.)
   - Patient names (Thai and English)
   - Date of birth (Gregorian YYYY-MM-DD and Buddhist Era DD/MM/YYYY)
   - Contact information and physician details
   - Medical facility information

3. **PII Detection**: Comprehensive Thai PII patterns including:
   - Thai National ID (13-digit with checksum validation)
   - Hospital Numbers (HN patterns)
   - Lab Numbers (VN patterns)
   - Thai Names (with contextual detection)
   - Phone Numbers (Thai mobile formats)
   - Medical Record Numbers

4. **PDF Obfuscation**: Multiple methods:
   - BLACK_BOX: Solid black rectangles
   - BLUR: Gaussian blur
   - REDACT: Text replacement
   - WHITE_BOX: White rectangles
   - HASH_PATTERN: Pattern-based hashing

5. **Database Integration** (--database flag): Supabase architecture with:
   - Encrypted PII storage using pgcrypto
   - Row-level security (RLS) policies
   - Audit logging for compliance
   - Soft deletes for data recovery
   - Medical extractions table for structured data
   - Document tracking and status management

### Environment Configuration

Single `.env` file approach (consolidated from multiple env files):

Required environment variables:
```env
# Required for OCR
GOOGLE_API_KEY=your_gemini_api_key_here

# Optional - for database features (--database flag)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key  
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_key

# Optional - application settings
ENABLE_ULTRA_THINK=true
CONFIDENCE_THRESHOLD=0.7
OBFUSCATION_METHOD=black_box
MAX_CONCURRENT_REQUESTS=10
```

Key configuration options:
- `ENABLE_ULTRA_THINK`: Enable enhanced OCR thinking (default: true, override with `--disable-ultra-think`)
- `CONFIDENCE_THRESHOLD`: PII detection confidence (0.0-1.0, default: 0.7, override with `--confidence-threshold`)
- `OBFUSCATION_METHOD`: Default obfuscation method (default: black_box, override with `--obfuscation-method`)
- `MAX_CONCURRENT_REQUESTS`: Concurrent processing limit (default: 10, override with `--max-concurrent`)

### Database Schema

The application uses a comprehensive Supabase schema with:
- **organizations**: Multi-tenant support
- **user_profiles**: Role-based access (admin, editor, viewer, analyst)
- **documents**: PDF document tracking with status and metadata
- **medical_extractions**: Structured medical data with encrypted PII fields
- **audit_logs**: Immutable audit trail
- **encryption_keys**: Organization-specific encryption

All tables have RLS policies enforcing organization-based isolation and role-based access control.

### Security Considerations

1. **PII Protection**: All PII is encrypted at rest using pgcrypto with organization-specific keys
2. **Audit Logging**: All data access and modifications are logged for HIPAA compliance
3. **Soft Deletes**: Data is marked as deleted but retained for recovery
4. **Role-Based Access**: Granular permissions based on user roles
5. **File Validation**: Strict file size and format validation
6. **Circuit Breakers**: Automatic failure detection and recovery
7. **Retry Logic**: Exponential backoff for failed API calls

### Testing Strategy (Comprehensive Coverage)

The project now has comprehensive test coverage with 17 test modules:

- **Unit Tests**: Core business logic with mocked dependencies
  - `test_core_config_real.py`: Configuration management
  - `test_core_constants_real.py`: Application constants
  - `test_core_exceptions_real.py`: Exception handling
  - `test_utils_*.py`: Utility functions

- **Integration Tests**: Real API calls marked with `@pytest.mark.integration`
  - `test_gemini_integration_real.py`: Google Gemini API integration
  - `test_supabase_integration_real.py`: Database operations
  - `test_end_to_end_real.py`: Full pipeline testing

- **Processing Tests**: Core business logic
  - `test_ocr_processor_real.py`: OCR processing with real API
  - `test_pii_detection_real.py`: PII pattern matching
  - `test_pdf_obfuscation_real.py`: PDF manipulation
  - `test_batch_processor_real.py`: Concurrent processing

- **Security Tests**: PII detection and encryption validation
  - `test_security_pii_encryption_real.py`: Encryption utilities
  - `test_security_audit_logger_real.py`: Audit logging

- **Performance Tests**: Benchmark tests for OCR processing
  - All `*_real.py` tests include performance measurements

### CLI Reference

#### Required Arguments
- `--input, -i`: Input PDF file or directory path
- `--output, -o`: Output directory for processed files

#### Feature Flags (New)
- `--enhanced`: Enable enhanced medical extraction with structured field extraction
- `--database, --save-to-db`: Enable database integration to save results to Supabase

#### Processing Options
- `--no-obfuscation`: Disable PDF obfuscation (only perform OCR and PII detection)
- `--pattern`: File pattern for directory processing (default: *.pdf)
- `--no-recursive`: Disable recursive directory scanning
- `--confidence-threshold`: PII confidence threshold (0.0-1.0, default: 0.7)
- `--obfuscation-method`: Obfuscation method (black_box, blur, redact, white_box, hash_pattern)
- `--max-concurrent`: Maximum concurrent processing (default: 10)

#### Advanced Options (New)
- `--disable-ultra-think`: Disable ULTRA THINK mode for faster processing
- `--organization-id`: Organization ID for database transaction recording
- `--user-id`: User ID for audit logging
- `--session-id`: Session ID for tracking
- `--verbose, -v`: Enable verbose logging
- `--version`: Show version information

## Development Workflow

### Project Status
The project has been successfully consolidated and cleaned up:
- ✅ Single entry point (`src/main.py`)
- ✅ Comprehensive test coverage (17 test modules)
- ✅ Fixed import issues and reorganized structure
- ✅ Enhanced features available via CLI flags
- ✅ Database integration consolidated
- ✅ Clean environment configuration

### Adding New Features

1. **Update models** in `src/core/models.py`
2. **Implement logic** in appropriate `src/processing/` module
3. **Add tests** in `tests/` with appropriate naming (`test_[module]_real.py`)
4. **Update CLI flags** in `src/main.py` if needed
5. **Update documentation**

### Modifying PII Patterns

1. Edit regex patterns in `src/processing/pii_detector.py`
2. Update corresponding tests in `tests/test_pii_detection_real.py`
3. Consider performance impact of complex patterns
4. Test with real documents using integration tests

### Database Changes

1. Create migration SQL in `migrations/` directory
2. Test with both upgrade and rollback scenarios in `tests/test_supabase_integration_real.py`
3. Update RLS policies as needed
4. Test with `--database` flag

### API Integration

1. Check retry logic and circuit breakers in `src/utils/utils.py`
2. Implement proper error handling in processing modules
3. Add appropriate logging without exposing PII
4. Test with integration tests (`test_*_real.py`)

### Running the Application

The application now has a single, clean entry point with all features accessible via flags:

```bash
# Basic OCR processing
python -m src.main --input document.pdf --output ./results

# Enhanced processing with database
python -m src.main --input document.pdf --output ./results --enhanced --database

# Batch processing with custom settings
python -m src.main --input ./documents --output ./results \
  --enhanced --database --confidence-threshold 0.8 --verbose
```

All legacy entry points have been consolidated, and the project structure is now clean and maintainable.