#!/bin/bash

# ChromoForge Docker Cleanup Executor
# Orchestrated cleanup with production service protection
# Version: 1.0
# Orchestrator: project-orchestrator

set -euo pipefail

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CRITICAL_CONTAINER="chromoforge-app"
CRITICAL_IMAGE="chromoforge:1.0.0"
CRITICAL_NETWORK="chromoforge-network"
BACKUP_DIR="backup/cleanup-$(date +%Y%m%d-%H%M%S)"
LOG_FILE="$BACKUP_DIR/cleanup.log"

# Initialize
mkdir -p "$BACKUP_DIR"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

# Functions
log_info() {
    echo -e "${BLUE}[INFO $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

check_critical_service() {
    if docker ps | grep -q "$CRITICAL_CONTAINER"; then
        log_success "Critical service $CRITICAL_CONTAINER is running"
        return 0
    else
        log_error "CRITICAL: Service $CRITICAL_CONTAINER is NOT running!"
        return 1
    fi
}

create_checkpoint() {
    local checkpoint_name=$1
    log_info "Creating checkpoint: $checkpoint_name"
    
    docker system df > "$BACKUP_DIR/checkpoint-$checkpoint_name.txt"
    docker ps -a >> "$BACKUP_DIR/checkpoint-$checkpoint_name.txt"
    
    if check_critical_service; then
        log_success "Checkpoint $checkpoint_name passed"
        return 0
    else
        log_error "Checkpoint $checkpoint_name FAILED"
        return 1
    fi
}

confirm_action() {
    local action=$1
    echo -e "${YELLOW}About to: $action${NC}"
    echo -n "Continue? (y/n): "
    read -r response
    if [[ "$response" != "y" ]]; then
        log_warning "Action cancelled by user"
        return 1
    fi
    return 0
}

calculate_space_saved() {
    local before=$1
    local after=$2
    local saved=$((before - after))
    local saved_gb=$(echo "scale=2; $saved / **********" | bc)
    echo "$saved_gb"
}

# Main Execution
main() {
    echo "=========================================="
    echo "ChromoForge Docker Cleanup Orchestration"
    echo "=========================================="
    echo ""
    
    # Phase 2: Validation
    log_info "PHASE 2: Strategy Validation & Approval"
    
    # Check prerequisites
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed!"
        exit 1
    fi
    
    if ! check_critical_service; then
        log_error "Critical service not running. Aborting cleanup."
        exit 1
    fi
    
    # Save initial state
    log_info "Saving initial system state..."
    docker system df -v > "$BACKUP_DIR/system-state-before.txt"
    docker ps -a > "$BACKUP_DIR/containers-before.txt"
    docker images -a > "$BACKUP_DIR/images-before.txt"
    docker volume ls > "$BACKUP_DIR/volumes-before.txt"
    docker network ls > "$BACKUP_DIR/networks-before.txt"
    
    # Get initial space usage
    SPACE_BEFORE=$(docker system df | grep "Images" | awk '{print $4}' | sed 's/GB//' | awk '{sum+=$1} END {print sum***********}')
    
    log_success "Initial state saved to $BACKUP_DIR"
    
    # Phase 3A: Backup
    log_info "PHASE 3A: Resource Backup & Safety Preparation"
    
    if confirm_action "Create service configuration backup"; then
        docker inspect "$CRITICAL_CONTAINER" > "$BACKUP_DIR/container-config.json"
        log_success "Service configuration backed up"
    fi
    
    # Create rollback script
    cat > "$BACKUP_DIR/rollback.sh" << 'ROLLBACK'
#!/bin/bash
echo "Starting emergency rollback..."

# Check if critical service is running
if ! docker ps | grep -q "chromoforge-app"; then
    echo "Attempting to restart critical service..."
    
    # Try to start existing container
    docker start chromoforge-app 2>/dev/null || {
        echo "Container not found, recreating..."
        # Recreate from image
        docker run -d \
            --name chromoforge-app \
            --network chromoforge-network \
            chromoforge:1.0.0
    }
fi

# Verify service is running
if docker ps | grep -q "chromoforge-app"; then
    echo "✓ Service restored successfully"
else
    echo "✗ Failed to restore service - manual intervention required!"
    exit 1
fi
ROLLBACK
    chmod +x "$BACKUP_DIR/rollback.sh"
    log_success "Rollback script created"
    
    # Checkpoint CP1
    create_checkpoint "CP1-pre-cleanup" || exit 1
    
    # Phase 3B: Cleanup Execution
    log_info "PHASE 3B: Coordinated Cleanup Execution"
    
    # Stage 1: Container Cleanup
    log_info "Stage 1: Removing stopped containers..."
    if confirm_action "Remove stopped containers"; then
        # Get stopped container IDs (excluding critical)
        STOPPED_CONTAINERS=$(docker ps -a -q -f "status=exited" | grep -v "$CRITICAL_CONTAINER" || true)
        
        if [ -n "$STOPPED_CONTAINERS" ]; then
            echo "$STOPPED_CONTAINERS" | xargs docker rm -f
            log_success "Removed stopped containers"
        else
            log_info "No stopped containers to remove"
        fi
    fi
    
    # Checkpoint CP2
    create_checkpoint "CP2-post-containers" || exit 1
    
    # Stage 2: Dangling Images
    log_info "Stage 2: Removing dangling images..."
    if confirm_action "Remove dangling images"; then
        docker image prune -f
        log_success "Removed dangling images"
    fi
    
    # Stage 3: Unused Images
    log_info "Stage 3: Removing unused dashboard images..."
    if confirm_action "Remove unused dashboard images"; then
        # Remove dashboard images (but not the critical one)
        for image in "chromoforge-dashboard:dev" "chromoforge-dashboard:test" "chromoforge-dashboard:fixed" "chromoforge-dashboard:1.0.0"; do
            if docker images | grep -q "$image"; then
                docker rmi "$image" 2>/dev/null && log_success "Removed $image" || log_warning "Could not remove $image"
            fi
        done
    fi
    
    # Checkpoint CP3
    create_checkpoint "CP3-post-images" || exit 1
    
    # Stage 4: Build Cache
    log_info "Stage 4: Clearing build cache..."
    if confirm_action "Clear Docker build cache (15.18GB)"; then
        docker builder prune -f --all
        log_success "Cleared build cache"
    fi
    
    # Checkpoint CP4
    create_checkpoint "CP4-post-cache" || exit 1
    
    # Stage 5: Volumes and Networks
    log_info "Stage 5: Cleaning unused volumes and networks..."
    if confirm_action "Clean unused volumes and networks"; then
        docker volume prune -f
        docker network prune -f
        log_success "Cleaned unused volumes and networks"
    fi
    
    # Verify critical network still exists
    if docker network ls | grep -q "$CRITICAL_NETWORK"; then
        log_success "Critical network $CRITICAL_NETWORK preserved"
    else
        log_warning "Critical network missing - recreating..."
        docker network create "$CRITICAL_NETWORK"
    fi
    
    # Stage 6: Final Cleanup
    log_info "Stage 6: Final system cleanup..."
    if confirm_action "Run final system prune"; then
        docker system prune -f
        log_success "Final cleanup complete"
    fi
    
    # Checkpoint CP5
    create_checkpoint "CP5-final" || exit 1
    
    # Phase 3C: Verification
    log_info "PHASE 3C: Integrity Verification & Maintenance"
    
    # Service health check
    log_info "Performing service health check..."
    if docker exec "$CRITICAL_CONTAINER" echo "Health check" &>/dev/null; then
        log_success "Service health check passed"
    else
        log_error "Service health check failed!"
        "$BACKUP_DIR/rollback.sh"
    fi
    
    # Calculate space saved
    SPACE_AFTER=$(docker system df | grep "Images" | awk '{print $4}' | sed 's/GB//' | awk '{sum+=$1} END {print sum***********}')
    SPACE_SAVED=$(calculate_space_saved "$SPACE_BEFORE" "$SPACE_AFTER")
    
    # Generate report
    cat > "$BACKUP_DIR/cleanup-report.md" << EOF
# Docker Cleanup Report

**Date**: $(date)
**Executor**: docker-cleanup-executor.sh
**Orchestrator**: project-orchestrator

## Results Summary

### Space Recovery
- **Target**: 32.86 GB
- **Achieved**: ${SPACE_SAVED} GB
- **Success**: $([ $(echo "$SPACE_SAVED > 30" | bc) -eq 1 ] && echo "✓" || echo "✗")

### Service Status
- **Container**: $CRITICAL_CONTAINER
- **Status**: $(docker ps --format "{{.Status}}" -f name="$CRITICAL_CONTAINER")
- **Health**: $(docker exec "$CRITICAL_CONTAINER" echo "OK" &>/dev/null && echo "✓ Healthy" || echo "✗ Unhealthy")

### Cleanup Actions
$(grep -c "SUCCESS" "$LOG_FILE") successful operations
$(grep -c "WARNING" "$LOG_FILE") warnings
$(grep -c "ERROR" "$LOG_FILE") errors

### System State
\`\`\`
$(docker system df)
\`\`\`

## Recommendations
1. Schedule regular cleanup (monthly)
2. Monitor build cache growth
3. Implement image tagging strategy
4. Consider multi-stage builds for smaller images

## Next Maintenance
- **Date**: $(date -d "+30 days" +%Y-%m-%d)
- **Type**: Regular cleanup
- **Expected Recovery**: 5-10 GB
EOF
    
    log_success "Report generated: $BACKUP_DIR/cleanup-report.md"
    
    # Final summary
    echo ""
    echo "=========================================="
    echo "CLEANUP ORCHESTRATION COMPLETE"
    echo "=========================================="
    echo "Space Recovered: ${SPACE_SAVED} GB"
    echo "Service Status: $(check_critical_service && echo "✓ Running" || echo "✗ Down")"
    echo "Report: $BACKUP_DIR/cleanup-report.md"
    echo "Rollback: $BACKUP_DIR/rollback.sh"
    echo "=========================================="
}

# Trap errors
trap 'log_error "Error occurred! Running rollback..."; $BACKUP_DIR/rollback.sh' ERR

# Run main function
main "$@"