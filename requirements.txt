# Core dependencies
google-genai>=0.3.0
pypdf>=4.0.0
pydantic>=2.5.0
pydantic-settings>=2.1.0
python-dotenv>=1.0.0
supabase>=2.3.0
pillow>=10.0.0

# PDF processing and image handling
pymupdf>=1.23.0
pdf2image>=1.16.3
reportlab>=4.0.0

# OCR and computer vision for image-based PDF processing
pytesseract>=0.3.10
opencv-python>=4.8.0

# Text processing and regex
regex>=2023.12.25
python-Levenshtein>=0.25.0

# Security and encryption
cryptography>=41.0.0

# HTTP client and async support
httpx>=0.27.0
aiofiles>=23.2.0
asyncio-throttle>=1.0.2

# Logging and monitoring
structlog>=23.2.0
python-json-logger>=2.0.7

# Development and testing dependencies
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0
pytest-mock>=3.12.0
black>=23.12.0
isort>=5.13.0
mypy>=1.8.0
flake8>=7.0.0
bandit>=1.7.5

# Supabase integration
supabase>=2.3.0

# Additional test utilities
faker>=22.0.0
factory-boy>=3.3.0

# Type stubs
types-Pillow>=10.0.0
types-requests>=2.31.0