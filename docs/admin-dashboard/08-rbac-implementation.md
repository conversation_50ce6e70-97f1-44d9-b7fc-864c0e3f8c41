# ChromoForge Admin Dashboard - Role-Based Permission Implementation Strategy

## Executive Summary

This document defines the comprehensive Role-Based Access Control (RBAC) implementation strategy for the ChromoForge Admin Dashboard, ensuring granular control over medical data access while maintaining usability and performance.

---

## 1. RBAC Architecture Overview

### 1.1 Permission Model

```
┌─────────────────────────────────────────────────────────┐
│                    Users                                 │
│              (Authenticated Entities)                    │
└──────────────────────┬──────────────────────────────────┘
                       │ has
┌──────────────────────▼──────────────────────────────────┐
│                    Roles                                 │
│         (Admin, Editor, Viewer, Analyst)                 │
└──────────────────────┬──────────────────────────────────┘
                       │ contains
┌──────────────────────▼──────────────────────────────────┐
│                 Permissions                              │
│         (Resource + Action Combinations)                 │
└──────────────────────┬──────────────────────────────────┘
                       │ applies to
┌──────────────────────▼──────────────────────────────────┐
│                  Resources                               │
│    (Medical Records, Users, Reports, System)            │
└─────────────────────────────────────────────────────────┘
```

### 1.2 Core Design Principles

1. **Principle of Least Privilege**: Users have minimum permissions required
2. **Separation of Duties**: Critical actions require multiple roles
3. **Defense in Depth**: Multiple layers of permission checks
4. **Auditability**: All permission checks are logged
5. **Performance**: Permission checks must be fast (<10ms)
6. **Flexibility**: Easy to add new roles and permissions

---

## 2. Role Definitions

### 2.1 System Roles

#### Administrator Role
```typescript
interface AdminRole {
  id: 'admin'
  name: 'Administrator'
  description: 'Full system access with administrative privileges'
  permissions: [
    // Medical Records
    'records:create',
    'records:read',
    'records:update',
    'records:soft_delete',
    'records:hard_delete',
    'records:export',
    'records:bulk_operations',
    
    // User Management
    'users:create',
    'users:read',
    'users:update',
    'users:delete',
    'users:assign_roles',
    'users:reset_password',
    
    // System
    'system:configure',
    'system:backup',
    'system:restore',
    'system:view_logs',
    'system:manage_integrations',
    
    // Audit
    'audit:read',
    'audit:export',
    'audit:configure',
    
    // Analytics
    'analytics:view_all',
    'analytics:create_reports',
    'analytics:export_reports'
  ]
  restrictions: {
    requireTwoFactor: true
    requireApproval: ['system:restore', 'records:hard_delete']
    ipWhitelist: boolean
    timeRestrictions: boolean
  }
}
```

#### Editor Role
```typescript
interface EditorRole {
  id: 'editor'
  name: 'Medical Records Editor'
  description: 'Create and modify medical records'
  permissions: [
    // Medical Records
    'records:create',
    'records:read',
    'records:update',
    'records:soft_delete',
    'records:export',
    
    // Limited User Access
    'users:read_own',
    'users:update_own',
    
    // Audit
    'audit:read_own',
    
    // Analytics
    'analytics:view_own'
  ]
  restrictions: {
    dataScope: 'organization' // Can only access own organization's data
    exportLimit: 1000 // Records per export
    rateLimit: {
      updates: 100, // per hour
      exports: 10   // per day
    }
  }
}
```

#### Viewer Role
```typescript
interface ViewerRole {
  id: 'viewer'
  name: 'Medical Records Viewer'
  description: 'Read-only access to medical records'
  permissions: [
    // Medical Records (Read Only)
    'records:read',
    'records:search',
    
    // User
    'users:read_own',
    
    // Analytics
    'analytics:view_basic'
  ]
  restrictions: {
    dataScope: 'organization'
    exportLimit: 0 // No export allowed
    piiMasking: true // PII fields are masked
    timeAccess: 'business_hours' // 8 AM - 6 PM
  }
}
```

#### Analyst Role
```typescript
interface AnalystRole {
  id: 'analyst'
  name: 'Data Analyst'
  description: 'Analyze and report on medical data'
  permissions: [
    // Medical Records
    'records:read',
    'records:search',
    'records:export',
    
    // Analytics
    'analytics:view_all',
    'analytics:create_reports',
    'analytics:export_reports',
    'analytics:schedule_reports',
    
    // Limited User Access
    'users:read_own',
    
    // Audit
    'audit:read_analytics'
  ]
  restrictions: {
    dataScope: 'organization'
    exportLimit: 10000
    anonymizedOnly: true // Only see anonymized data
    aggregationRequired: true // Can't see individual records
  }
}
```

### 2.2 Custom Role Support

```typescript
interface CustomRole {
  id: string
  name: string
  description: string
  baseRole: 'viewer' | 'editor' | 'analyst'
  additionalPermissions: Permission[]
  removedPermissions: Permission[]
  restrictions: RoleRestrictions
  createdBy: string
  approvedBy: string
  active: boolean
}

// Example: Medical Coder Role
const medicalCoderRole: CustomRole = {
  id: 'medical_coder',
  name: 'Medical Coder',
  baseRole: 'editor',
  additionalPermissions: [
    'records:code_diagnosis',
    'records:code_procedures'
  ],
  removedPermissions: [
    'records:soft_delete',
    'records:export'
  ],
  restrictions: {
    allowedFields: ['diagnosis', 'procedures', 'icd_codes'],
    dataScope: 'assigned_records'
  }
}
```

---

## 3. Permission Implementation

### 3.1 Permission Structure

```typescript
// Permission type definition
type Permission = `${Resource}:${Action}`

type Resource = 
  | 'records'
  | 'users'
  | 'audit'
  | 'analytics'
  | 'system'
  | 'reports'
  | 'exports'

type Action =
  | 'create'
  | 'read'
  | 'update'
  | 'delete'
  | 'soft_delete'
  | 'hard_delete'
  | 'export'
  | 'import'
  | 'search'
  | 'bulk_operations'

// Permission metadata
interface PermissionMetadata {
  permission: Permission
  resource: Resource
  action: Action
  description: string
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  requiresApproval: boolean
  requiresTwoFactor: boolean
  auditLevel: 'basic' | 'detailed' | 'full'
}
```

### 3.2 Permission Checking Implementation

```typescript
// Fast permission checking with caching
export class PermissionService {
  private cache = new Map<string, Set<Permission>>()
  
  async hasPermission(
    userId: string,
    permission: Permission,
    context?: PermissionContext
  ): Promise<boolean> {
    // Check cache first
    const cacheKey = `${userId}:${permission}`
    const cached = this.cache.get(cacheKey)
    
    if (cached !== undefined) {
      return this.evaluateWithContext(cached.has(permission), context)
    }
    
    // Load user permissions
    const userPermissions = await this.loadUserPermissions(userId)
    this.cache.set(userId, userPermissions)
    
    // Evaluate permission
    const hasBase = userPermissions.has(permission)
    return this.evaluateWithContext(hasBase, context)
  }
  
  async hasAnyPermission(
    userId: string,
    permissions: Permission[]
  ): Promise<boolean> {
    for (const permission of permissions) {
      if (await this.hasPermission(userId, permission)) {
        return true
      }
    }
    return false
  }
  
  async hasAllPermissions(
    userId: string,
    permissions: Permission[]
  ): Promise<boolean> {
    for (const permission of permissions) {
      if (!await this.hasPermission(userId, permission)) {
        return false
      }
    }
    return true
  }
  
  private async evaluateWithContext(
    hasPermission: boolean,
    context?: PermissionContext
  ): Promise<boolean> {
    if (!hasPermission) return false
    if (!context) return true
    
    // Time-based restrictions
    if (context.timeRestriction) {
      const now = new Date()
      const hour = now.getHours()
      if (hour < 8 || hour > 18) return false
    }
    
    // IP-based restrictions
    if (context.ipWhitelist) {
      const allowed = await this.checkIPWhitelist(context.ipAddress)
      if (!allowed) return false
    }
    
    // Organization scope
    if (context.organizationId) {
      const userOrg = await this.getUserOrganization(context.userId)
      if (userOrg !== context.organizationId) return false
    }
    
    return true
  }
}
```

### 3.3 Database Schema

```sql
-- Role definitions
CREATE TABLE roles (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_system BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Permissions catalog
CREATE TABLE permissions (
    id VARCHAR(100) PRIMARY KEY,
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT,
    risk_level VARCHAR(20) DEFAULT 'low',
    requires_approval BOOLEAN DEFAULT false,
    requires_two_factor BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Role-Permission mapping
CREATE TABLE role_permissions (
    role_id VARCHAR(50) REFERENCES roles(id),
    permission_id VARCHAR(100) REFERENCES permissions(id),
    granted_at TIMESTAMPTZ DEFAULT NOW(),
    granted_by UUID REFERENCES user_profiles(id),
    PRIMARY KEY (role_id, permission_id)
);

-- User-Role assignments
CREATE TABLE user_roles (
    user_id UUID REFERENCES user_profiles(id),
    role_id VARCHAR(50) REFERENCES roles(id),
    organization_id UUID REFERENCES organizations(id),
    assigned_at TIMESTAMPTZ DEFAULT NOW(),
    assigned_by UUID REFERENCES user_profiles(id),
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    PRIMARY KEY (user_id, role_id, organization_id)
);

-- Permission overrides (for exceptions)
CREATE TABLE user_permission_overrides (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id),
    permission_id VARCHAR(100) REFERENCES permissions(id),
    organization_id UUID REFERENCES organizations(id),
    action ENUM('grant', 'revoke') NOT NULL,
    reason TEXT NOT NULL,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES user_profiles(id),
    approved_by UUID REFERENCES user_profiles(id),
    approved_at TIMESTAMPTZ
);

-- Delegated permissions (temporary)
CREATE TABLE delegated_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_user_id UUID REFERENCES user_profiles(id),
    to_user_id UUID REFERENCES user_profiles(id),
    permissions TEXT[] NOT NULL, -- Array of permission IDs
    valid_from TIMESTAMPTZ NOT NULL,
    valid_until TIMESTAMPTZ NOT NULL,
    reason TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    revoked_at TIMESTAMPTZ,
    revoked_by UUID REFERENCES user_profiles(id)
);
```

---

## 4. Frontend Implementation

### 4.1 Permission-Based UI Components

```tsx
// Permission-aware component wrapper
export function WithPermission({
  permission,
  fallback = null,
  children
}: {
  permission: Permission | Permission[]
  fallback?: ReactNode
  children: ReactNode
}) {
  const { hasPermission } = usePermissions()
  const [allowed, setAllowed] = useState(false)
  
  useEffect(() => {
    const check = async () => {
      const permissions = Array.isArray(permission) ? permission : [permission]
      const result = await hasPermission(permissions)
      setAllowed(result)
    }
    check()
  }, [permission])
  
  if (!allowed) return <>{fallback}</>
  
  return <>{children}</>
}

// Usage example
<WithPermission permission="records:update">
  <Button onClick={handleEdit}>Edit Record</Button>
</WithPermission>

// Multiple permissions (ANY)
<WithPermission permission={["records:update", "records:create"]}>
  <Button onClick={handleSave}>Save</Button>
</WithPermission>
```

### 4.2 Route Protection

```tsx
// Protected route wrapper
export function ProtectedRoute({
  permissions,
  requireAll = false,
  children
}: {
  permissions: Permission[]
  requireAll?: boolean
  children: ReactNode
}) {
  const router = useRouter()
  const { hasAllPermissions, hasAnyPermission } = usePermissions()
  const [authorized, setAuthorized] = useState<boolean | null>(null)
  
  useEffect(() => {
    const checkAuth = async () => {
      const allowed = requireAll
        ? await hasAllPermissions(permissions)
        : await hasAnyPermission(permissions)
        
      if (!allowed) {
        router.push('/unauthorized')
      } else {
        setAuthorized(true)
      }
    }
    
    checkAuth()
  }, [permissions, requireAll])
  
  if (authorized === null) return <LoadingSpinner />
  if (!authorized) return null
  
  return <>{children}</>
}

// Usage in pages
export default function AdminPage() {
  return (
    <ProtectedRoute permissions={['system:configure']} requireAll>
      <AdminDashboard />
    </ProtectedRoute>
  )
}
```

### 4.3 Dynamic Menu Generation

```tsx
// Role-based navigation menu
export function NavigationMenu() {
  const { user, permissions } = useAuth()
  
  const menuItems = useMemo(() => {
    return [
      {
        label: 'Dashboard',
        href: '/dashboard',
        icon: Home,
        permission: null // Available to all
      },
      {
        label: 'Medical Records',
        href: '/records',
        icon: FileText,
        permission: 'records:read',
        children: [
          {
            label: 'All Records',
            href: '/records',
            permission: 'records:read'
          },
          {
            label: 'Add New',
            href: '/records/new',
            permission: 'records:create'
          },
          {
            label: 'Deleted',
            href: '/records/deleted',
            permission: 'records:hard_delete'
          }
        ]
      },
      {
        label: 'Analytics',
        href: '/analytics',
        icon: BarChart,
        permission: 'analytics:view_basic'
      },
      {
        label: 'Users',
        href: '/users',
        icon: Users,
        permission: 'users:read'
      },
      {
        label: 'Admin',
        href: '/admin',
        icon: Settings,
        permission: 'system:configure'
      }
    ].filter(item => {
      if (!item.permission) return true
      return permissions.includes(item.permission)
    })
  }, [permissions])
  
  return <Navigation items={menuItems} />
}
```

---

## 5. API Implementation

### 5.1 Middleware Protection

```typescript
// Permission middleware for API routes
export function requirePermission(...permissions: Permission[]) {
  return async (
    req: NextApiRequest,
    res: NextApiResponse,
    next: NextFunction
  ) => {
    const session = await getSession(req)
    
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
    
    const permissionService = new PermissionService()
    const hasPermission = await permissionService.hasAnyPermission(
      session.user.id,
      permissions
    )
    
    if (!hasPermission) {
      // Log permission denial
      await auditLog({
        userId: session.user.id,
        action: 'PERMISSION_DENIED',
        permissions: permissions,
        resource: req.url,
        method: req.method
      })
      
      return res.status(403).json({ 
        error: 'Forbidden',
        required: permissions
      })
    }
    
    // Add user permissions to request
    req.permissions = await permissionService.getUserPermissions(session.user.id)
    
    next()
  }
}

// Usage in API routes
export default compose(
  withAuth,
  requirePermission('records:update'),
  withValidation(updateRecordSchema)
)(async function handler(req, res) {
  // Handler has access to validated data and verified permissions
  const result = await updateRecord(req.body)
  res.json(result)
})
```

### 5.2 Resource-Level Permissions

```typescript
// Fine-grained resource permissions
export class ResourcePermissionService {
  async canAccessRecord(
    userId: string,
    recordId: string,
    action: Action
  ): Promise<boolean> {
    // Basic permission check
    const hasPermission = await this.hasPermission(
      userId,
      `records:${action}`
    )
    
    if (!hasPermission) return false
    
    // Resource-level checks
    const record = await this.getRecord(recordId)
    const user = await this.getUser(userId)
    
    // Organization scope
    if (record.organizationId !== user.organizationId) {
      return false
    }
    
    // Department scope
    if (user.role === 'editor' && user.departmentId) {
      if (record.departmentId !== user.departmentId) {
        return false
      }
    }
    
    // Assigned records only
    if (user.restrictions?.assignedOnly) {
      const assigned = await this.isAssignedToUser(recordId, userId)
      if (!assigned) return false
    }
    
    // Sensitive data restrictions
    if (record.sensitivityLevel === 'high' && !user.sensitiveDataAccess) {
      return false
    }
    
    return true
  }
  
  async filterAccessibleRecords(
    userId: string,
    records: MedicalRecord[]
  ): Promise<MedicalRecord[]> {
    const accessible = await Promise.all(
      records.map(async (record) => {
        const canAccess = await this.canAccessRecord(
          userId,
          record.id,
          'read'
        )
        return canAccess ? record : null
      })
    )
    
    return accessible.filter(Boolean) as MedicalRecord[]
  }
}
```

---

## 6. Advanced Permission Features

### 6.1 Delegation System

```typescript
// Permission delegation for temporary access
export class PermissionDelegation {
  async delegatePermissions(
    fromUserId: string,
    toUserId: string,
    permissions: Permission[],
    duration: Duration,
    reason: string
  ): Promise<DelegationTicket> {
    // Verify delegator has permissions
    const hasAll = await this.permissionService.hasAllPermissions(
      fromUserId,
      permissions
    )
    
    if (!hasAll) {
      throw new Error('Cannot delegate permissions you do not have')
    }
    
    // Check delegation limits
    const canDelegate = await this.checkDelegationLimits(fromUserId)
    if (!canDelegate) {
      throw new Error('Delegation limit exceeded')
    }
    
    // Create delegation record
    const delegation = await this.createDelegation({
      fromUserId,
      toUserId,
      permissions,
      validFrom: new Date(),
      validUntil: addDuration(new Date(), duration),
      reason
    })
    
    // Notify both parties
    await this.notifyDelegation(delegation)
    
    // Audit log
    await this.auditDelegation(delegation)
    
    return delegation
  }
  
  async revokeDelegation(
    delegationId: string,
    revokedBy: string
  ): Promise<void> {
    const delegation = await this.getDelegation(delegationId)
    
    // Verify revoker is authorized
    if (delegation.fromUserId !== revokedBy && !await this.isAdmin(revokedBy)) {
      throw new Error('Not authorized to revoke delegation')
    }
    
    // Revoke
    await this.updateDelegation(delegationId, {
      revokedAt: new Date(),
      revokedBy
    })
    
    // Clear permission cache
    await this.clearPermissionCache(delegation.toUserId)
    
    // Notify
    await this.notifyRevocation(delegation)
  }
}
```

### 6.2 Approval Workflows

```typescript
// Permission request and approval system
export class PermissionApproval {
  async requestPermission(
    userId: string,
    permissions: Permission[],
    justification: string,
    duration?: Duration
  ): Promise<PermissionRequest> {
    // Find approvers
    const approvers = await this.findApprovers(permissions)
    
    // Create request
    const request = await this.createRequest({
      userId,
      permissions,
      justification,
      duration,
      approvers,
      status: 'pending'
    })
    
    // Notify approvers
    await this.notifyApprovers(request)
    
    return request
  }
  
  async approveRequest(
    requestId: string,
    approverId: string,
    comments?: string
  ): Promise<void> {
    const request = await this.getRequest(requestId)
    
    // Verify approver
    if (!request.approvers.includes(approverId)) {
      throw new Error('Not authorized to approve')
    }
    
    // Record approval
    await this.recordApproval(requestId, approverId, comments)
    
    // Check if all required approvals received
    const approvals = await this.getApprovals(requestId)
    if (approvals.length >= request.requiredApprovals) {
      // Grant permissions
      await this.grantPermissions(request)
      
      // Update status
      await this.updateRequestStatus(requestId, 'approved')
      
      // Notify requester
      await this.notifyRequester(request, 'approved')
    }
  }
}
```

### 6.3 Context-Aware Permissions

```typescript
// Dynamic permission evaluation based on context
export class ContextualPermissions {
  async evaluatePermission(
    userId: string,
    permission: Permission,
    context: PermissionContext
  ): Promise<PermissionResult> {
    const baseResult = await this.checkBasePermission(userId, permission)
    
    if (!baseResult.allowed) {
      return baseResult
    }
    
    // Apply contextual rules
    const rules = await this.getContextualRules(permission)
    
    for (const rule of rules) {
      const result = await this.evaluateRule(rule, context)
      if (!result.allowed) {
        return {
          allowed: false,
          reason: result.reason,
          rule: rule.id
        }
      }
    }
    
    return { allowed: true }
  }
  
  private async evaluateRule(
    rule: ContextualRule,
    context: PermissionContext
  ): Promise<RuleResult> {
    switch (rule.type) {
      case 'TIME_WINDOW':
        return this.evaluateTimeWindow(rule, context)
        
      case 'LOCATION':
        return this.evaluateLocation(rule, context)
        
      case 'RISK_SCORE':
        return this.evaluateRiskScore(rule, context)
        
      case 'DATA_CLASSIFICATION':
        return this.evaluateDataClassification(rule, context)
        
      case 'WORKLOAD':
        return this.evaluateWorkload(rule, context)
        
      default:
        return { allowed: true }
    }
  }
}
```

---

## 7. Performance Optimization

### 7.1 Permission Caching Strategy

```typescript
// Multi-level permission cache
export class PermissionCache {
  private l1Cache = new Map<string, CachedPermissions>() // In-memory
  private l2Cache: Redis // Distributed cache
  
  async getCachedPermissions(
    userId: string
  ): Promise<Set<Permission> | null> {
    // Check L1 cache
    const l1 = this.l1Cache.get(userId)
    if (l1 && !this.isExpired(l1)) {
      return l1.permissions
    }
    
    // Check L2 cache
    const l2 = await this.l2Cache.get(`permissions:${userId}`)
    if (l2) {
      const parsed = JSON.parse(l2)
      this.l1Cache.set(userId, parsed)
      return new Set(parsed.permissions)
    }
    
    return null
  }
  
  async setCachedPermissions(
    userId: string,
    permissions: Set<Permission>
  ): Promise<void> {
    const cached: CachedPermissions = {
      permissions,
      timestamp: Date.now(),
      ttl: 300000 // 5 minutes
    }
    
    // Set L1
    this.l1Cache.set(userId, cached)
    
    // Set L2
    await this.l2Cache.setex(
      `permissions:${userId}`,
      300,
      JSON.stringify({
        permissions: Array.from(permissions),
        timestamp: cached.timestamp
      })
    )
  }
  
  async invalidateUser(userId: string): Promise<void> {
    this.l1Cache.delete(userId)
    await this.l2Cache.del(`permissions:${userId}`)
  }
  
  async invalidateRole(roleId: string): Promise<void> {
    // Get all users with this role
    const users = await this.getUsersByRole(roleId)
    
    // Invalidate all affected users
    await Promise.all(
      users.map(userId => this.invalidateUser(userId))
    )
  }
}
```

### 7.2 Batch Permission Checking

```typescript
// Efficient batch permission checking
export class BatchPermissionChecker {
  async checkBatch(
    requests: PermissionCheckRequest[]
  ): Promise<Map<string, boolean>> {
    // Group by user
    const byUser = this.groupByUser(requests)
    
    // Load permissions for all users
    const userPermissions = await this.loadMultipleUsers(
      Array.from(byUser.keys())
    )
    
    // Evaluate all requests
    const results = new Map<string, boolean>()
    
    for (const [userId, userRequests] of byUser) {
      const permissions = userPermissions.get(userId) || new Set()
      
      for (const request of userRequests) {
        const key = `${userId}:${request.permission}`
        results.set(key, permissions.has(request.permission))
      }
    }
    
    return results
  }
  
  async preloadPermissions(
    userIds: string[]
  ): Promise<void> {
    // Batch load from database
    const permissions = await this.db.query`
      SELECT 
        ur.user_id,
        array_agg(DISTINCT rp.permission_id) as permissions
      FROM user_roles ur
      JOIN role_permissions rp ON ur.role_id = rp.role_id
      WHERE ur.user_id = ANY(${userIds})
        AND ur.is_active = true
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
      GROUP BY ur.user_id
    `
    
    // Cache all results
    await Promise.all(
      permissions.map(({ user_id, permissions }) =>
        this.cache.setCachedPermissions(user_id, new Set(permissions))
      )
    )
  }
}
```

---

## 8. Monitoring & Analytics

### 8.1 Permission Usage Analytics

```typescript
// Track permission usage patterns
export class PermissionAnalytics {
  async trackPermissionCheck(
    userId: string,
    permission: Permission,
    result: boolean,
    context: PermissionContext
  ): Promise<void> {
    const event: PermissionEvent = {
      timestamp: new Date(),
      userId,
      permission,
      result,
      context,
      responseTime: context.responseTime
    }
    
    // Real-time streaming
    await this.streamEvent(event)
    
    // Batch for analytics
    await this.batchEvent(event)
    
    // Alert on anomalies
    if (await this.isAnomalous(event)) {
      await this.alertSecurityTeam(event)
    }
  }
  
  async generatePermissionReport(
    timeRange: TimeRange
  ): Promise<PermissionReport> {
    return {
      summary: {
        totalChecks: await this.getTotalChecks(timeRange),
        uniqueUsers: await this.getUniqueUsers(timeRange),
        denialRate: await this.getDenialRate(timeRange),
        avgResponseTime: await this.getAvgResponseTime(timeRange)
      },
      
      topPermissions: await this.getTopPermissions(timeRange),
      
      denialPatterns: await this.getDenialPatterns(timeRange),
      
      unusedPermissions: await this.getUnusedPermissions(timeRange),
      
      recommendations: await this.generateRecommendations(timeRange)
    }
  }
}
```

### 8.2 Compliance Reporting

```typescript
// RBAC compliance monitoring
export class RBACCompliance {
  async auditRoleAssignments(): Promise<ComplianceReport> {
    const issues: ComplianceIssue[] = []
    
    // Check for orphaned permissions
    const orphaned = await this.findOrphanedPermissions()
    if (orphaned.length > 0) {
      issues.push({
        type: 'ORPHANED_PERMISSIONS',
        severity: 'medium',
        items: orphaned
      })
    }
    
    // Check for excessive permissions
    const excessive = await this.findExcessivePermissions()
    if (excessive.length > 0) {
      issues.push({
        type: 'EXCESSIVE_PERMISSIONS',
        severity: 'high',
        items: excessive
      })
    }
    
    // Check for separation of duties violations
    const sodViolations = await this.checkSeparationOfDuties()
    if (sodViolations.length > 0) {
      issues.push({
        type: 'SOD_VIOLATION',
        severity: 'critical',
        items: sodViolations
      })
    }
    
    // Check for expired assignments
    const expired = await this.findExpiredAssignments()
    if (expired.length > 0) {
      issues.push({
        type: 'EXPIRED_ASSIGNMENTS',
        severity: 'medium',
        items: expired
      })
    }
    
    return {
      timestamp: new Date(),
      issues,
      recommendations: this.generateRecommendations(issues),
      complianceScore: this.calculateComplianceScore(issues)
    }
  }
}
```

---

## 9. Testing Strategy

### 9.1 Permission Testing Framework

```typescript
// Comprehensive permission testing
describe('RBAC System', () => {
  describe('Role Assignments', () => {
    it('should grant correct permissions for admin role', async () => {
      const userId = await createUser({ role: 'admin' })
      
      expect(await hasPermission(userId, 'records:read')).toBe(true)
      expect(await hasPermission(userId, 'records:hard_delete')).toBe(true)
      expect(await hasPermission(userId, 'system:configure')).toBe(true)
    })
    
    it('should restrict viewer role appropriately', async () => {
      const userId = await createUser({ role: 'viewer' })
      
      expect(await hasPermission(userId, 'records:read')).toBe(true)
      expect(await hasPermission(userId, 'records:update')).toBe(false)
      expect(await hasPermission(userId, 'records:delete')).toBe(false)
    })
  })
  
  describe('Context-Aware Permissions', () => {
    it('should respect time-based restrictions', async () => {
      const userId = await createUser({ 
        role: 'viewer',
        restrictions: { timeAccess: 'business_hours' }
      })
      
      // During business hours
      mockTime('2024-01-15 10:00:00')
      expect(await hasPermission(userId, 'records:read')).toBe(true)
      
      // After hours
      mockTime('2024-01-15 22:00:00')
      expect(await hasPermission(userId, 'records:read')).toBe(false)
    })
  })
  
  describe('Permission Delegation', () => {
    it('should allow temporary permission delegation', async () => {
      const manager = await createUser({ role: 'editor' })
      const assistant = await createUser({ role: 'viewer' })
      
      // Delegate update permission
      await delegatePermission(
        manager,
        assistant,
        ['records:update'],
        '1 day'
      )
      
      expect(await hasPermission(assistant, 'records:update')).toBe(true)
      
      // After expiration
      advanceTime('2 days')
      expect(await hasPermission(assistant, 'records:update')).toBe(false)
    })
  })
})
```

### 9.2 Security Testing

```typescript
// Permission security tests
describe('Permission Security', () => {
  it('should prevent privilege escalation', async () => {
    const user = await createUser({ role: 'editor' })
    
    // Try to assign admin role to self
    await expect(
      assignRole(user, user, 'admin')
    ).rejects.toThrow('Cannot assign role above your level')
  })
  
  it('should enforce separation of duties', async () => {
    const user = await createUser({ role: 'admin' })
    
    // Try to approve own permission request
    const request = await requestPermission(user, ['system:restore'])
    
    await expect(
      approveRequest(request.id, user)
    ).rejects.toThrow('Cannot approve own request')
  })
  
  it('should log all permission denials', async () => {
    const user = await createUser({ role: 'viewer' })
    
    // Attempt forbidden action
    await hasPermission(user, 'records:delete')
    
    // Check audit log
    const logs = await getAuditLogs({
      action: 'PERMISSION_DENIED',
      userId: user
    })
    
    expect(logs).toHaveLength(1)
    expect(logs[0].permission).toBe('records:delete')
  })
})
```

---

## 10. Migration Strategy

### 10.1 Migration from Existing System

```typescript
// RBAC migration plan
export class RBACMigration {
  async migrate(): Promise<MigrationResult> {
    const steps = [
      this.backupExistingPermissions,
      this.createRBACSchema,
      this.migrateRoles,
      this.migratePermissions,
      this.migrateUserAssignments,
      this.validateMigration,
      this.enableNewSystem,
      this.deprecateOldSystem
    ]
    
    const results = []
    
    for (const [index, step] of steps.entries()) {
      try {
        const result = await step()
        results.push({
          step: step.name,
          status: 'success',
          details: result
        })
        
        // Checkpoint after each step
        await this.createCheckpoint(index)
        
      } catch (error) {
        // Rollback on failure
        await this.rollback(index)
        
        results.push({
          step: step.name,
          status: 'failed',
          error: error.message
        })
        
        break
      }
    }
    
    return {
      success: results.every(r => r.status === 'success'),
      steps: results,
      timestamp: new Date()
    }
  }
}
```

---

## Conclusion

This comprehensive RBAC implementation provides:

- ✅ **Granular Control**: Fine-grained permissions at resource and field level
- ✅ **Performance**: Sub-10ms permission checks with intelligent caching
- ✅ **Flexibility**: Custom roles and contextual permissions
- ✅ **Security**: Multiple layers of protection and audit trails
- ✅ **Scalability**: Handles 10,000+ users with complex permission matrices
- ✅ **Compliance**: Full audit trail and compliance reporting
- ✅ **User Experience**: Seamless UI adaptation based on permissions
- ✅ **Maintainability**: Clear structure and comprehensive testing

The system ensures that medical data access is properly controlled while maintaining system performance and user productivity.