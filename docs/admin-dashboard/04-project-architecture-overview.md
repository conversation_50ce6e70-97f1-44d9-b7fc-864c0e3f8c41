# ChromoForge Admin Dashboard - Project Architecture Overview

## Executive Summary

The ChromoForge Admin Dashboard is a modern, secure, and scalable web application designed to manage OCR-extracted medical data from Thai healthcare documents. Built on Next.js 14 with Supabase backend, it provides enterprise-grade security, role-based access control, and seamless integration with the existing ChromoForge OCR pipeline.

---

## 1. System Architecture

### 1.1 Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────┐
│                              USER LAYER                                  │
├─────────────────────────────────────────────────────────────────────────┤
│  Healthcare Staff │ Administrators │ Data Analysts │ Compliance Officers │
└───────────────────┬─────────────────────────────────────────────────────┘
                    │ HTTPS/WSS
┌───────────────────▼─────────────────────────────────────────────────────┐
│                          PRESENTATION LAYER                              │
├─────────────────────────────────────────────────────────────────────────┤
│                    Next.js 14 App Router (Vercel Edge)                  │
│  ┌─────────────┬──────────────┬─────────────┬────────────────────┐    │
│  │   React     │  Server      │   Static    │    Real-time       │    │
│  │ Components  │ Components   │   Assets    │   Subscriptions    │    │
│  └─────────────┴──────────────┴─────────────┴────────────────────┘    │
└───────────────────┬─────────────────────────────────────────────────────┘
                    │ API Routes / tRPC
┌───────────────────▼─────────────────────────────────────────────────────┐
│                         APPLICATION LAYER                                │
├─────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────┬──────────────┬─────────────┬────────────────────┐    │
│  │  Business   │   Service     │   Domain    │   Security         │    │
│  │   Logic     │   Layer       │   Models    │   Middleware       │    │
│  └─────────────┴──────────────┴─────────────┴────────────────────┘    │
└───────────────────┬─────────────────────────────────────────────────────┘
                    │ Supabase Client SDK
┌───────────────────▼─────────────────────────────────────────────────────┐
│                            DATA LAYER                                    │
├─────────────────────────────────────────────────────────────────────────┤
│                        Supabase Platform                                 │
│  ┌─────────────┬──────────────┬─────────────┬────────────────────┐    │
│  │ PostgreSQL  │    Auth       │  Real-time  │    Storage         │    │
│  │   + RLS     │   Service     │   Engine    │    Service         │    │
│  └─────────────┴──────────────┴─────────────┴────────────────────┘    │
└───────────────────┬─────────────────────────────────────────────────────┘
                    │
┌───────────────────▼─────────────────────────────────────────────────────┐
│                      INTEGRATION LAYER                                   │
├─────────────────────────────────────────────────────────────────────────┤
│  ChromoForge OCR │ Google Gemini │ Monitoring │ Email/SMS │ Analytics   │
└─────────────────────────────────────────────────────────────────────────┘
```

### 1.2 Technology Stack Overview

| Layer | Technology | Purpose |
|-------|------------|---------|
| **Frontend** | Next.js 14, React 18, TypeScript | Modern web application framework |
| **UI Library** | Radix UI + Tailwind CSS | Accessible components with custom styling |
| **State Management** | Zustand + TanStack Query | Client and server state management |
| **Backend** | Supabase (PostgreSQL) | Database, auth, real-time, storage |
| **Authentication** | Supabase Auth | JWT-based auth with role management |
| **API Layer** | Next.js API Routes | RESTful endpoints with validation |
| **Deployment** | Vercel | Edge deployment with global CDN |
| **Monitoring** | Sentry + Vercel Analytics | Error tracking and performance |

## 2. Core Architecture Decisions

### 2.1 Next.js 14 with App Router

**Decision**: Use Next.js 14 with the new App Router architecture
**Rationale**:
- Server Components reduce client bundle size by 40-60%
- Built-in streaming SSR improves perceived performance
- Parallel routes enable better code organization
- Enhanced security with server-side data fetching
- Native TypeScript support with improved DX

**Trade-offs**:
- Learning curve for developers familiar with Pages Router
- Some third-party libraries need adaptation
- More complex mental model for data fetching

### 2.2 Supabase as Backend Platform

**Decision**: Leverage existing Supabase infrastructure
**Rationale**:
- Already implemented with ChromoForge OCR pipeline
- Built-in Row Level Security (RLS) for data isolation
- Real-time subscriptions for live updates
- Managed authentication with role support
- Auto-generated APIs with TypeScript types

**Benefits**:
- Reduced infrastructure complexity
- Consistent security model across applications
- Lower operational overhead
- Built-in compliance features

### 2.3 Radix UI + Tailwind CSS

**Decision**: Radix UI primitives with Tailwind CSS styling
**Rationale**:
- Radix provides unstyled, accessible components
- Complete control over visual design
- Tailwind enables rapid, consistent styling
- Perfect fit for ChromoForge Design System
- Smaller bundle size vs component libraries

**Implementation**:
```typescript
// Example: Custom button following ChromoForge Design System
export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'primary', size = 'md', ...props }, ref) => {
    return (
      <button
        className={cn(
          // Base styles
          'inline-flex items-center justify-center rounded-md font-medium',
          'transition-colors focus:outline-none focus:ring-2',
          // ChromoForge Design System colors
          variants[variant],
          sizes[size],
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
```

## 3. Domain-Driven Architecture

### 3.1 Domain Structure

```
src/domains/
├── medical-records/         # Core medical data management
│   ├── components/         # UI components
│   ├── hooks/             # Domain-specific hooks
│   ├── services/          # Business logic
│   ├── types/             # TypeScript types
│   └── validators/        # Zod schemas
├── user-management/        # User and role management
│   ├── components/
│   ├── services/
│   └── types/
├── audit-logs/            # Compliance and auditing
│   ├── components/
│   ├── services/
│   └── types/
└── analytics/             # Reporting and insights
    ├── components/
    ├── services/
    └── types/
```

### 3.2 Service Layer Pattern

Each domain implements a service layer that encapsulates business logic:

```typescript
// domains/medical-records/services/medical-records.service.ts
export class MedicalRecordsService {
  constructor(private supabase: SupabaseClient) {}

  async getRecords(params: GetRecordsParams): Promise<PaginatedResult<MedicalRecord>> {
    // Business logic, validation, transformation
  }

  async updateRecord(id: string, data: UpdateRecordData): Promise<MedicalRecord> {
    // Audit trail, validation, encryption
  }

  async softDelete(id: string, reason: string): Promise<void> {
    // Soft delete logic with audit
  }
}
```

## 4. Security Architecture

### 4.1 Multi-Layer Security Model

```
┌─────────────────────────────────────────────┐
│            Browser Security                  │
│  • CSP Headers                              │
│  • HTTPS Only                               │
│  • Secure Cookies                           │
└─────────────────┬───────────────────────────┘
                  │
┌─────────────────▼───────────────────────────┐
│          Application Security                │
│  • Input Validation (Zod)                   │
│  • Output Sanitization (DOMPurify)          │
│  • Rate Limiting                            │
│  • CORS Configuration                       │
└─────────────────┬───────────────────────────┘
                  │
┌─────────────────▼───────────────────────────┐
│            API Security                      │
│  • JWT Verification                         │
│  • Role-Based Access Control                │
│  • Request Validation                       │
│  • Audit Logging                            │
└─────────────────┬───────────────────────────┘
                  │
┌─────────────────▼───────────────────────────┐
│          Database Security                   │
│  • Row Level Security (RLS)                 │
│  • Encrypted PII (pgcrypto)                 │
│  • Audit Triggers                           │
│  • Soft Deletes                             │
└─────────────────────────────────────────────┘
```

### 4.2 Authentication & Authorization Flow

```mermaid
sequenceDiagram
    participant User
    participant NextJS
    participant Supabase Auth
    participant PostgreSQL
    
    User->>NextJS: Login Request
    NextJS->>Supabase Auth: Verify Credentials
    Supabase Auth->>PostgreSQL: Check user_profiles
    PostgreSQL-->>Supabase Auth: User + Role
    Supabase Auth-->>NextJS: JWT Token
    NextJS-->>User: Set Secure Cookie
    
    User->>NextJS: Access Protected Route
    NextJS->>NextJS: Verify JWT
    NextJS->>PostgreSQL: Query with RLS
    PostgreSQL-->>NextJS: Filtered Data
    NextJS-->>User: Authorized Response
```

## 5. Data Flow Architecture

### 5.1 Real-time Data Synchronization

```typescript
// Real-time subscription architecture
const subscription = supabase
  .channel('medical_records')
  .on('postgres_changes', 
    { 
      event: '*', 
      schema: 'public', 
      table: 'medical_extractions',
      filter: `organization_id=eq.${orgId}`
    },
    (payload) => {
      // Update local state
      queryClient.invalidateQueries(['medical-records'])
    }
  )
  .subscribe()
```

### 5.2 Optimistic Updates Pattern

```typescript
// Optimistic UI updates for better UX
const updateMutation = useMutation({
  mutationFn: updateRecord,
  onMutate: async (newData) => {
    // Cancel in-flight queries
    await queryClient.cancelQueries(['record', id])
    
    // Snapshot previous value
    const previous = queryClient.getQueryData(['record', id])
    
    // Optimistically update
    queryClient.setQueryData(['record', id], newData)
    
    return { previous }
  },
  onError: (err, newData, context) => {
    // Rollback on error
    queryClient.setQueryData(['record', id], context.previous)
  },
  onSettled: () => {
    // Always refetch after error or success
    queryClient.invalidateQueries(['record', id])
  }
})
```

## 6. Performance Architecture

### 6.1 Performance Optimization Strategy

| Optimization | Technique | Impact |
|-------------|-----------|---------|
| **Bundle Size** | Tree shaking, code splitting | -60% initial load |
| **Data Fetching** | Parallel queries, prefetching | -40% wait time |
| **Rendering** | Server components, streaming | -50% TTI |
| **Caching** | Query caching, CDN | -70% repeat loads |
| **Images** | Next/Image optimization | -80% image size |

### 6.2 Caching Architecture

```
┌─────────────────────────────────────────────┐
│           Browser Cache                      │
│  • Static assets (1 year)                   │
│  • API responses (5 min)                    │
└─────────────────┬───────────────────────────┘
                  │
┌─────────────────▼───────────────────────────┐
│            CDN Cache (Vercel)                │
│  • Static pages                             │
│  • Optimized images                         │
│  • API responses (configurable)             │
└─────────────────┬───────────────────────────┘
                  │
┌─────────────────▼───────────────────────────┐
│         Application Cache                    │
│  • TanStack Query (in-memory)               │
│  • Redis (session data)                     │
│  • Computed results                         │
└─────────────────┬───────────────────────────┘
                  │
┌─────────────────▼───────────────────────────┐
│          Database Cache                      │
│  • Query result sets                        │
│  • Materialized views                       │
│  • Connection pooling                       │
└─────────────────────────────────────────────┘
```

## 7. Integration Architecture

### 7.1 ChromoForge OCR Pipeline Integration

```typescript
// Integration with existing OCR pipeline
export class OCRIntegrationService {
  async triggerReprocessing(documentId: string): Promise<void> {
    // Create processing transaction
    const transaction = await this.createTransaction(documentId)
    
    // Trigger OCR pipeline
    await this.notifyOCRPipeline(transaction.id)
    
    // Subscribe to status updates
    this.subscribeToStatus(transaction.id)
  }
  
  private subscribeToStatus(transactionId: string) {
    return supabase
      .channel(`ocr_status:${transactionId}`)
      .on('postgres_changes', 
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'ocr_processing_transactions',
          filter: `id=eq.${transactionId}`
        },
        this.handleStatusUpdate
      )
      .subscribe()
  }
}
```

### 7.2 External System Integrations

| System | Integration Method | Purpose |
|--------|-------------------|---------|
| **Email Service** | SMTP/SendGrid API | Notifications |
| **SMS Gateway** | REST API | Alerts |
| **Monitoring** | Sentry SDK | Error tracking |
| **Analytics** | Google Analytics | Usage metrics |
| **Storage** | Supabase Storage | Document files |

## 8. Scalability Architecture

### 8.1 Horizontal Scaling Strategy

```
┌─────────────────────────────────────────────┐
│          Load Balancer (Vercel)              │
└──────┬──────────┬──────────┬────────────────┘
       │          │          │
┌──────▼────┐ ┌──▼────┐ ┌──▼────┐
│  Next.js  │ │Next.js│ │Next.js│  ← Auto-scaling
│ Instance 1│ │ Inst 2│ │ Inst N│
└──────┬────┘ └──┬────┘ └──┬────┘
       │          │          │
┌──────▼──────────▼──────────▼────────────────┐
│        Supabase (Managed Service)            │
│  • Connection pooling                        │
│  • Read replicas                             │
│  • Automatic scaling                         │
└─────────────────────────────────────────────┘
```

### 8.2 Performance Targets

- **Concurrent Users**: 1,000+ without degradation
- **Records**: 10M+ with sub-second queries
- **API Response**: <200ms p95
- **Page Load**: <2s on 3G
- **Uptime**: 99.9% SLA

## 9. Development Architecture

### 9.1 Development Workflow

```
Developer → Feature Branch → Local Testing → PR
                                  ↓
                            Code Review
                                  ↓
                         Automated Tests
                                  ↓
                          Staging Deploy
                                  ↓
                            QA Testing
                                  ↓
                         Production Deploy
```

### 9.2 Environment Architecture

| Environment | Purpose | Data | Access |
|------------|---------|------|---------|
| **Local** | Development | Mock data | Developers |
| **Preview** | PR testing | Test data | Team |
| **Staging** | Pre-production | Anonymized | Team + QA |
| **Production** | Live system | Real data | End users |

## 10. Monitoring & Observability

### 10.1 Observability Stack

```
Application Metrics → Vercel Analytics
Error Tracking → Sentry
User Analytics → Google Analytics
Database Metrics → Supabase Dashboard
Uptime Monitoring → Better Uptime
Log Aggregation → Vercel Logs
```

### 10.2 Key Metrics

- **Business Metrics**: User adoption, data accuracy, processing time
- **Technical Metrics**: Response time, error rate, uptime
- **Security Metrics**: Failed logins, permission denials, audit events
- **Performance Metrics**: Core Web Vitals, API latency, DB query time

## 11. Disaster Recovery Architecture

### 11.1 Backup Strategy

- **Database**: Daily automated backups (30-day retention)
- **Files**: Versioned storage with soft deletes
- **Configuration**: Git-based version control
- **Secrets**: Encrypted vault with rotation

### 11.2 Recovery Procedures

| Scenario | RTO | RPO | Procedure |
|----------|-----|-----|-----------|
| **App Failure** | 5 min | 0 | Auto-failover to healthy instance |
| **Database Failure** | 30 min | 1 hour | Restore from latest backup |
| **Region Failure** | 2 hours | 4 hours | Deploy to alternate region |
| **Data Corruption** | 4 hours | 24 hours | Point-in-time recovery |

## 12. Future Architecture Considerations

### 12.1 Potential Enhancements

1. **Microservices Migration**: Extract OCR processing into separate service
2. **GraphQL API**: Replace REST with GraphQL for flexible queries
3. **Mobile Applications**: React Native apps for iOS/Android
4. **AI/ML Integration**: Automated data quality improvements
5. **Blockchain Audit Trail**: Immutable audit logs for compliance

### 12.2 Scaling Considerations

- **Database Sharding**: When exceeding 100M records
- **Multi-Region Deployment**: For <100ms global latency
- **Event-Driven Architecture**: For complex workflows
- **Data Lake Integration**: For advanced analytics

---

## Conclusion

The ChromoForge Admin Dashboard architecture provides a solid foundation for managing medical data with security, scalability, and user experience as primary concerns. The use of modern technologies like Next.js 14, Supabase, and the ChromoForge Design System ensures the application can evolve with changing requirements while maintaining high performance and security standards.

The architecture supports:
- ✅ 1,000+ concurrent users
- ✅ 10M+ medical records
- ✅ Real-time updates
- ✅ PDPA compliance
- ✅ 99.9% uptime
- ✅ Sub-second response times

This architecture positions ChromoForge as a leader in medical data management solutions for the Thai healthcare market.