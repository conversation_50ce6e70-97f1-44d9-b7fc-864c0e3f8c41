# ChromoForge Admin Dashboard - Standards Document

## Version 1.0 | Agent OS Standards Phase
**Last Updated**: 2025-01-05
**Document Status**: Official Standards Document
**Authors**: System Architect, Technical Lead

---

## 1. Executive Summary

This document establishes the technical standards, coding conventions, and architectural patterns for the ChromoForge Admin Dashboard. It ensures consistency, maintainability, and alignment with the existing ChromoForge OCR pipeline infrastructure while introducing modern web application standards for medical data management.

## 2. Technology Stack

### 2.1 Frontend Framework
- **Primary**: Next.js 14+ (App Router)
  - Rationale: Server-side rendering for performance, built-in security features, excellent TypeScript support
  - Features: React Server Components, Streaming SSR, Parallel Routes, Error Boundaries
  
### 2.2 UI Component Library
- **Primary**: Radix UI + Tailwind CSS
  - Rationale: Accessible primitives, unopinionated styling, perfect for custom design system
  - Styling: Tailwind CSS with ChromoForge Design System tokens
  
### 2.3 State Management
- **Client State**: Zustand
  - Rationale: Lightweight, TypeScript-first, minimal boilerplate
- **Server State**: TanStack Query (React Query) v5
  - Rationale: Powerful caching, optimistic updates, real-time synchronization
  
### 2.4 Database & Authentication
- **Database**: Supabase (existing infrastructure)
  - PostgreSQL with Row Level Security
  - Real-time subscriptions
  - Built-in authentication
- **ORM**: Prisma with Supabase adapter
  - Type-safe database queries
  - Migration management
  
### 2.5 Development Tools
- **Language**: TypeScript 5.3+
- **Package Manager**: pnpm (performance and disk space efficiency)
- **Linting**: ESLint with custom medical data rules
- **Formatting**: Prettier with ChromoForge config
- **Testing**: Vitest + React Testing Library + Playwright

## 3. Coding Standards

### 3.1 TypeScript Configuration
```typescript
// tsconfig.json standards
{
  "compilerOptions": {
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitOverride": true,
    "forceConsistentCasingInFileNames": true,
    "allowJs": false,
    "checkJs": false,
    "incremental": true,
    "target": "ES2022",
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "moduleResolution": "bundler"
  }
}
```

### 3.2 File Naming Conventions
```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Auth group routes
│   ├── (dashboard)/       # Dashboard group routes
│   └── api/               # API routes
├── components/            # React components
│   ├── ui/               # Base UI components
│   ├── features/         # Feature-specific components
│   └── layouts/          # Layout components
├── lib/                  # Utility functions
│   ├── supabase/        # Supabase client and helpers
│   ├── validators/      # Zod schemas
│   └── utils/           # General utilities
├── hooks/               # Custom React hooks
├── stores/              # Zustand stores
├── types/               # TypeScript type definitions
└── styles/              # Global styles and tokens
```

### 3.3 Component Standards
```typescript
// Component file structure
// ComponentName.tsx

import { type FC, type ReactNode } from 'react'
import { cn } from '@/lib/utils'

export interface ComponentNameProps {
  children?: ReactNode
  className?: string
  // Other props with JSDoc comments
}

/**
 * ComponentName - Brief description
 * @example
 * <ComponentName className="custom-class">Content</ComponentName>
 */
export const ComponentName: FC<ComponentNameProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <div className={cn('base-classes', className)} {...props}>
      {children}
    </div>
  )
}

ComponentName.displayName = 'ComponentName'
```

### 3.4 API Route Standards
```typescript
// app/api/resource/route.ts
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import { z } from 'zod'

// Request validation schema
const requestSchema = z.object({
  // Define request shape
})

export async function GET(request: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Business logic here
    
    return NextResponse.json({ data: result })
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}
```

## 4. Architecture Patterns

### 4.1 Domain-Driven Design
```
src/
├── domains/
│   ├── medical-records/
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── services/
│   │   ├── types/
│   │   └── validators/
│   ├── user-management/
│   ├── audit-logs/
│   └── organization/
```

### 4.2 Service Layer Pattern
```typescript
// domains/medical-records/services/medical-records.service.ts
export class MedicalRecordsService {
  constructor(private supabase: SupabaseClient) {}
  
  async getRecords(filters: RecordFilters): Promise<MedicalRecord[]> {
    // Implementation with RLS
  }
  
  async updateRecord(id: string, data: UpdateRecordData): Promise<MedicalRecord> {
    // Implementation with audit logging
  }
  
  async softDeleteRecord(id: string): Promise<void> {
    // Implementation with soft delete
  }
}
```

### 4.3 Repository Pattern for Data Access
```typescript
// lib/repositories/base.repository.ts
export abstract class BaseRepository<T> {
  constructor(
    protected supabase: SupabaseClient,
    protected tableName: string
  ) {}
  
  async findById(id: string): Promise<T | null> {
    const { data, error } = await this.supabase
      .from(this.tableName)
      .select('*')
      .eq('id', id)
      .single()
      
    if (error) throw error
    return data
  }
  
  // Other CRUD operations
}
```

## 5. Security Standards

### 5.1 Authentication & Authorization
- All routes must verify authentication using Supabase Auth
- Role-based access control (RBAC) enforced at both API and database levels
- Session management with secure, httpOnly cookies
- JWT tokens validated on every request

### 5.2 Data Protection
```typescript
// PII Handling Standards
interface PIIHandlingRules {
  // Never log PII data
  logging: 'masked' | 'excluded'
  
  // Always encrypt PII in transit and at rest
  encryption: 'required'
  
  // Audit all PII access
  audit: 'mandatory'
  
  // Display PII only to authorized users
  display: 'role-based'
}
```

### 5.3 Input Validation
```typescript
// All user inputs must be validated
import { z } from 'zod'

const patientSchema = z.object({
  patientCode: z.string().regex(/^TT\d+$/),
  patientNameTh: z.string().min(1).max(200),
  dateOfBirth: z.string().datetime(),
  // ... other fields
})

// Sanitization for display
export function sanitizeForDisplay(text: string): string {
  return DOMPurify.sanitize(text, { 
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  })
}
```

## 6. Performance Standards

### 6.1 Core Web Vitals Targets
- **LCP** (Largest Contentful Paint): < 2.5s
- **FID** (First Input Delay): < 100ms
- **CLS** (Cumulative Layout Shift): < 0.1
- **INP** (Interaction to Next Paint): < 200ms

### 6.2 Bundle Size Limits
```javascript
// next.config.js
module.exports = {
  experimental: {
    bundleSizeLimit: {
      javascript: 300, // 300KB
      css: 100, // 100KB
    }
  }
}
```

### 6.3 Data Fetching Patterns
```typescript
// Parallel data fetching for dashboard
export async function DashboardLayout() {
  // Parallel promises for better performance
  const [stats, recentRecords, userActivity] = await Promise.all([
    getStatistics(),
    getRecentRecords(),
    getUserActivity()
  ])
  
  return <DashboardView data={{ stats, recentRecords, userActivity }} />
}
```

## 7. Testing Standards

### 7.1 Test Coverage Requirements
- **Unit Tests**: Minimum 80% coverage for business logic
- **Integration Tests**: All API endpoints must have tests
- **E2E Tests**: Critical user journeys (login, CRUD operations, role-based access)

### 7.2 Test File Structure
```typescript
// __tests__/components/MedicalRecordTable.test.tsx
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { MedicalRecordTable } from '@/components/features/MedicalRecordTable'

describe('MedicalRecordTable', () => {
  it('should render records with proper role-based visibility', async () => {
    // Test implementation
  })
  
  it('should handle soft delete for editors', async () => {
    // Test implementation
  })
})
```

## 8. Accessibility Standards

### 8.1 WCAG 2.1 AA Compliance
- All interactive elements must be keyboard accessible
- Color contrast ratios: 4.5:1 for normal text, 3:1 for large text
- ARIA labels for all form inputs and interactive elements
- Focus indicators visible and high contrast

### 8.2 Thai Language Support
```typescript
// Bilingual support patterns
interface BilingualField {
  th: string  // Thai version
  en: string  // English version
}

// Date formatting for Buddhist Era
export function formatBuddhistEra(date: Date): string {
  const year = date.getFullYear() + 543
  return `${date.getDate()}/${date.getMonth() + 1}/${year}`
}
```

## 9. Design System Integration

### 9.1 ChromoForge Design Tokens
```typescript
// styles/tokens.ts
export const tokens = {
  colors: {
    deepMedicalTeal: '#0F766E',
    chromaForgeTeal: '#14B8A6',
    mintGradient: '#5EEAD4',
    // ... other colors
  },
  spacing: {
    base: 4,
    xs: 8,
    sm: 12,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  typography: {
    fontFamily: {
      sans: ['Noto Sans Thai', 'sans-serif'],
      logo: ['Lora', 'serif'],
    }
  }
} as const
```

### 9.2 Component Styling Convention
```typescript
// Using Tailwind with design tokens
export const Button = ({ variant = 'primary', ...props }) => {
  const variants = {
    primary: 'bg-chromaforge-teal hover:bg-deep-medical-teal',
    secondary: 'bg-gray-200 hover:bg-gray-300',
    danger: 'bg-red-600 hover:bg-red-700',
  }
  
  return (
    <button
      className={cn(
        'px-4 py-2 rounded-md font-medium transition-colors',
        'focus:outline-none focus:ring-2 focus:ring-offset-2',
        variants[variant]
      )}
      {...props}
    />
  )
}
```

## 10. Development Workflow

### 10.1 Git Workflow
- **Branch Naming**: `feature/CHRM-XXX-description`, `fix/CHRM-XXX-description`
- **Commit Convention**: Conventional Commits (feat:, fix:, docs:, etc.)
- **PR Requirements**: 
  - Minimum 2 approvals
  - All tests passing
  - No decrease in coverage
  - Lighthouse CI checks passing

### 10.2 Code Review Checklist
- [ ] TypeScript types properly defined (no `any`)
- [ ] Error handling implemented
- [ ] Loading and error states handled
- [ ] Accessibility requirements met
- [ ] Security considerations addressed
- [ ] Performance optimizations applied
- [ ] Tests written and passing
- [ ] Documentation updated

## 11. Monitoring & Observability

### 11.1 Error Tracking
```typescript
// lib/monitoring/sentry.ts
import * as Sentry from '@sentry/nextjs'

export function initSentry() {
  Sentry.init({
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
    environment: process.env.NODE_ENV,
    tracesSampleRate: 0.1,
    beforeSend(event) {
      // Remove PII from error reports
      return sanitizeEvent(event)
    }
  })
}
```

### 11.2 Performance Monitoring
- Real User Monitoring (RUM) with Web Vitals
- API response time tracking
- Database query performance monitoring
- Bundle size tracking in CI/CD

## 12. Compliance & Regulatory Standards

### 12.1 PDPA Compliance (Thai Personal Data Protection Act)
- Explicit consent for data processing
- Data minimization principles
- Right to erasure implementation
- Data portability features

### 12.2 Medical Data Standards
- HL7 FHIR compatibility where applicable
- ICD-10 coding support
- Thai medical terminology support
- Audit trail for all data modifications

---

## Appendix A: Technology Decision Records

### ADR-001: Next.js over Traditional SPA
**Status**: Accepted
**Context**: Need for SEO, performance, and security
**Decision**: Use Next.js with App Router for server-side rendering
**Consequences**: Better performance, built-in security features, more complex deployment

### ADR-002: Radix UI over Material-UI
**Status**: Accepted
**Context**: Need for custom design system implementation
**Decision**: Use Radix UI primitives with Tailwind CSS
**Consequences**: More control over styling, better accessibility, requires more initial setup

### ADR-003: Zustand over Redux
**Status**: Accepted
**Context**: Need for simple, performant state management
**Decision**: Use Zustand for client-state management
**Consequences**: Less boilerplate, easier to learn, sufficient for our needs

---

## Document Control

- **Version**: 1.0
- **Approval Date**: 2025-01-05
- **Review Cycle**: Quarterly
- **Next Review**: 2025-04-05
- **Approved By**: System Architect, Technical Lead, Security Officer