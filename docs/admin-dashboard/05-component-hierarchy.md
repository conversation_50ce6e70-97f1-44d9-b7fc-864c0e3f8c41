# ChromoForge Admin Dashboard - Component Hierarchy & Design System

## ChromoForge Design System Implementation

This document defines the complete component hierarchy following the ChromoForge Design System specifications for a professional medical-grade interface.

---

## 1. Design System Foundation

### 1.1 Color Palette

```typescript
// styles/design-tokens.ts
export const colors = {
  // Primary - Teal Gradient System
  primary: {
    900: '#0F766E', // Deep Medical Teal (Primary CTA)
    700: '#14B8A6', // ChromaForge Teal (Brand)
    500: '#2DD4BF', // Medium Teal
    300: '#5EEAD4', // Mint Gradient (Hover)
    100: '#99F6E4', // Light Mint
    50:  '#F0FDFA', // <PERSON> Tint (Background)
  },
  
  // Neutral - Medical Grays
  neutral: {
    900: '#0F172A', // Text Primary
    800: '#1E293B', // Text Secondary
    700: '#334155', // Borders Dark
    600: '#475569', // Disabled Text
    500: '#64748B', // Placeholders
    400: '#94A3B8', // Borders Light
    300: '#CBD5E1', // Dividers
    200: '#E2E8F0', // Backgrounds
    100: '#F1F5F9', // Light Background
    50:  '#F8FAFC', // White Alternative
  },
  
  // Semantic Colors
  success: {
    600: '#059669', // Success Dark
    500: '#10B981', // Success Default
    100: '#D1FAE5', // Success Light
  },
  warning: {
    600: '#D97706', // Warning Dark
    500: '#F59E0B', // Warning Default
    100: '#FEF3C7', // Warning Light
  },
  error: {
    600: '#DC2626', // Error Dark
    500: '#EF4444', // Error Default
    100: '#FEE2E2', // Error Light
  },
  info: {
    600: '#2563EB', // Info Dark
    500: '#3B82F6', // Info Default
    100: '#DBEAFE', // Info Light
  }
} as const
```

### 1.2 Typography System

```typescript
// styles/typography.ts
export const typography = {
  fontFamily: {
    sans: ['Noto Sans Thai', '-apple-system', 'BlinkMacSystemFont', 'sans-serif'],
    logo: ['Lora', 'serif'], // Logo only - normal weight
  },
  
  fontSize: {
    xs:   { size: '0.75rem', lineHeight: '1rem' },     // 12px
    sm:   { size: '0.875rem', lineHeight: '1.25rem' }, // 14px
    base: { size: '1rem', lineHeight: '1.5rem' },      // 16px
    lg:   { size: '1.125rem', lineHeight: '1.75rem' }, // 18px
    xl:   { size: '1.25rem', lineHeight: '1.75rem' },  // 20px
    '2xl': { size: '1.5rem', lineHeight: '2rem' },     // 24px
    '3xl': { size: '1.875rem', lineHeight: '2.25rem' },// 30px
    '4xl': { size: '2.25rem', lineHeight: '2.5rem' },  // 36px
  },
  
  fontWeight: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  }
}
```

### 1.3 Spacing System

```typescript
// styles/spacing.ts
export const spacing = {
  base: 4,  // Base unit
  scale: {
    0:   0,    // 0px
    px:  1,    // 1px
    0.5: 2,    // 2px
    1:   4,    // 4px (base)
    2:   8,    // 8px (XS)
    3:   12,   // 12px (SM)
    4:   16,   // 16px (MD)
    5:   20,   // 20px
    6:   24,   // 24px (LG)
    8:   32,   // 32px (XL)
    10:  40,   // 40px
    12:  48,   // 48px (XXL)
    16:  64,   // 64px
    20:  80,   // 80px
    24:  96,   // 96px
  }
}
```

## 2. Component Hierarchy

### 2.1 Base Components (Atomic)

```
components/ui/
├── Button/
│   ├── Button.tsx
│   ├── Button.styles.ts
│   ├── Button.types.ts
│   └── index.ts
├── Input/
│   ├── Input.tsx
│   ├── Input.styles.ts
│   └── index.ts
├── Card/
│   ├── Card.tsx
│   └── index.ts
├── Badge/
│   ├── Badge.tsx
│   └── index.ts
├── Avatar/
│   ├── Avatar.tsx
│   └── index.ts
├── Spinner/
│   ├── Spinner.tsx
│   └── index.ts
└── Typography/
    ├── Heading.tsx
    ├── Text.tsx
    └── index.ts
```

#### Button Component

```typescript
// components/ui/Button/Button.tsx
import { forwardRef } from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

const buttonVariants = cva(
  // Base styles
  'inline-flex items-center justify-center rounded-md font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none',
  {
    variants: {
      variant: {
        primary: [
          'bg-primary-700 text-white',
          'hover:bg-primary-900 hover:shadow-lg',
          'focus:ring-primary-500',
          'active:scale-[0.98]'
        ],
        secondary: [
          'bg-white text-primary-700 border border-primary-700',
          'hover:bg-primary-50',
          'focus:ring-primary-500'
        ],
        danger: [
          'bg-error-500 text-white',
          'hover:bg-error-600',
          'focus:ring-error-500'
        ],
        ghost: [
          'text-neutral-700',
          'hover:bg-neutral-100',
          'focus:ring-neutral-400'
        ]
      },
      size: {
        sm: 'h-8 px-3 text-sm',
        md: 'h-10 px-4 text-base',
        lg: 'h-12 px-6 text-lg',
        icon: 'h-10 w-10'
      }
    },
    defaultVariants: {
      variant: 'primary',
      size: 'md'
    }
  }
)

interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  loading?: boolean
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, loading, children, disabled, ...props }, ref) => {
    return (
      <button
        ref={ref}
        className={cn(buttonVariants({ variant, size, className }))}
        disabled={disabled || loading}
        {...props}
      >
        {loading && <Spinner className="mr-2 h-4 w-4" />}
        {children}
      </button>
    )
  }
)

Button.displayName = 'Button'
```

### 2.2 Composite Components

```
components/features/
├── DataTable/
│   ├── DataTable.tsx
│   ├── DataTablePagination.tsx
│   ├── DataTableToolbar.tsx
│   ├── DataTableColumnHeader.tsx
│   └── index.ts
├── SearchBar/
│   ├── SearchBar.tsx
│   ├── SearchFilters.tsx
│   └── index.ts
├── MedicalRecordCard/
│   ├── MedicalRecordCard.tsx
│   ├── ConfidenceIndicator.tsx
│   └── index.ts
├── RoleSelector/
│   ├── RoleSelector.tsx
│   └── index.ts
└── StatusBadge/
    ├── StatusBadge.tsx
    └── index.ts
```

#### Medical Record Card Component

```typescript
// components/features/MedicalRecordCard/MedicalRecordCard.tsx
interface MedicalRecordCardProps {
  record: MedicalRecord
  onEdit?: () => void
  onDelete?: () => void
  compact?: boolean
}

export function MedicalRecordCard({
  record,
  onEdit,
  onDelete,
  compact = false
}: MedicalRecordCardProps) {
  const { user } = useAuth()
  const canEdit = hasPermission(user, 'records:edit')
  
  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <h3 className="text-lg font-semibold text-neutral-900">
              {record.patientCode}
            </h3>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" size="sm">
                {record.investigation}
              </Badge>
              <ConfidenceIndicator score={record.overallConfidence} />
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <StatusBadge status={record.status} />
            {canEdit && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={onEdit}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={onDelete}
                    className="text-error-600"
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </CardHeader>
      
      {!compact && (
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-neutral-600">Patient Name:</span>
              <p className="font-medium">{record.patientNameEn}</p>
              <p className="text-neutral-700">{record.patientNameTh}</p>
            </div>
            <div>
              <span className="text-neutral-600">Sample Code:</span>
              <p className="font-medium">{record.sampleCode}</p>
            </div>
          </div>
          
          <div className="border-t pt-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-neutral-600">
                Processed: {formatDate(record.createdAt)}
              </span>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigateToDetails(record.id)}
              >
                View Details
                <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  )
}
```

### 2.3 Layout Components

```
components/layouts/
├── DashboardLayout/
│   ├── DashboardLayout.tsx
│   ├── Sidebar.tsx
│   ├── Header.tsx
│   └── index.ts
├── PageLayout/
│   ├── PageLayout.tsx
│   ├── PageHeader.tsx
│   └── index.ts
└── AuthLayout/
    ├── AuthLayout.tsx
    └── index.ts
```

#### Dashboard Layout with ChromoForge Branding

```typescript
// components/layouts/DashboardLayout/Header.tsx
export function Header() {
  const { user } = useAuth()
  const { language, setLanguage } = useUIStore()
  
  return (
    <header className="h-16 border-b border-neutral-200 bg-white">
      <div className="flex h-full items-center justify-between px-6">
        {/* Logo with ChromoForge branding */}
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-primary-700 to-primary-300">
            <span className="font-logo text-xl font-normal text-white">C</span>
          </div>
          <span className="text-xl font-semibold text-neutral-900">
            ChromoForge
          </span>
        </div>
        
        {/* Right side actions */}
        <div className="flex items-center gap-4">
          {/* Language Selector */}
          <Select value={language} onValueChange={setLanguage}>
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="th">ไทย</SelectItem>
              <SelectItem value="en">EN</SelectItem>
            </SelectContent>
          </Select>
          
          {/* Notifications */}
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-5 w-5" />
            <span className="absolute right-1 top-1 h-2 w-2 rounded-full bg-error-500" />
          </Button>
          
          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user?.avatar} />
                  <AvatarFallback>
                    {user?.name?.charAt(0) || 'U'}
                  </AvatarFallback>
                </Avatar>
                <span className="hidden md:inline-block">
                  {user?.name}
                </span>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <User className="mr-2 h-4 w-4" />
                Profile
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-error-600">
                <LogOut className="mr-2 h-4 w-4" />
                Logout
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}
```

### 2.4 Domain-Specific Components

```
domains/medical-records/components/
├── MedicalRecordForm/
│   ├── MedicalRecordForm.tsx
│   ├── FormSections/
│   │   ├── PatientInfoSection.tsx
│   │   ├── InvestigationSection.tsx
│   │   └── PhysicianSection.tsx
│   └── index.ts
├── MedicalRecordsTable/
│   ├── MedicalRecordsTable.tsx
│   ├── columns.tsx
│   └── index.ts
├── RecordDetailsModal/
│   ├── RecordDetailsModal.tsx
│   └── index.ts
└── ConfidenceScoreDisplay/
    ├── ConfidenceScoreDisplay.tsx
    └── index.ts
```

#### Medical Records Table with Thai Support

```typescript
// domains/medical-records/components/MedicalRecordsTable/columns.tsx
export const columns: ColumnDef<MedicalRecord>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'patientCode',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Patient Code" />
    ),
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue('patientCode')}</div>
    ),
  },
  {
    accessorKey: 'patientNameTh',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="ชื่อผู้ป่วย" />
    ),
    cell: ({ row }) => (
      <div className="space-y-1">
        <div className="font-medium">{row.original.patientNameTh}</div>
        <div className="text-sm text-neutral-600">
          {row.original.patientNameEn}
        </div>
      </div>
    ),
  },
  {
    accessorKey: 'investigation',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Investigation" />
    ),
    cell: ({ row }) => (
      <Badge variant="secondary">
        {row.getValue('investigation')}
      </Badge>
    ),
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: 'overallConfidence',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Confidence" />
    ),
    cell: ({ row }) => (
      <ConfidenceScoreDisplay score={row.getValue('overallConfidence')} />
    ),
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => <StatusBadge status={row.getValue('status')} />,
  },
  {
    id: 'actions',
    cell: ({ row }) => <RowActions row={row} />,
  },
]
```

### 2.5 Form Components

```
components/forms/
├── FormField/
│   ├── FormField.tsx
│   └── index.ts
├── DatePicker/
│   ├── DatePicker.tsx
│   ├── BuddhistEraDatePicker.tsx
│   └── index.ts
├── MultiSelect/
│   ├── MultiSelect.tsx
│   └── index.ts
└── FileUpload/
    ├── FileUpload.tsx
    ├── DropZone.tsx
    └── index.ts
```

#### Buddhist Era Date Picker

```typescript
// components/forms/DatePicker/BuddhistEraDatePicker.tsx
interface BuddhistEraDatePickerProps {
  value?: Date
  onChange: (date: Date | undefined) => void
  placeholder?: string
}

export function BuddhistEraDatePicker({
  value,
  onChange,
  placeholder = 'เลือกวันที่'
}: BuddhistEraDatePickerProps) {
  const { language } = useUIStore()
  
  const formatBuddhistDate = (date: Date) => {
    const buddhistYear = date.getFullYear() + 543
    const day = date.getDate().toString().padStart(2, '0')
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    return `${day}/${month}/${buddhistYear}`
  }
  
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          className={cn(
            'w-full justify-start text-left font-normal',
            !value && 'text-neutral-500'
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {value ? formatBuddhistDate(value) : placeholder}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={value}
          onSelect={onChange}
          initialFocus
          locale={language === 'th' ? th : enUS}
          formatters={{
            formatYear: (date) => (date.getFullYear() + 543).toString()
          }}
        />
      </PopoverContent>
    </Popover>
  )
}
```

## 3. Component Styling Guidelines

### 3.1 Gradient Usage

```typescript
// Gradient backgrounds for premium feel
const gradients = {
  primary: 'bg-gradient-to-br from-primary-700 to-primary-500',
  success: 'bg-gradient-to-br from-success-600 to-success-500',
  premium: 'bg-gradient-to-br from-primary-900 via-primary-700 to-primary-300',
}

// Usage in components
<div className={cn(
  'rounded-lg p-6 text-white shadow-xl',
  gradients.premium
)}>
  <h2 className="text-2xl font-semibold">Premium Feature</h2>
</div>
```

### 3.2 Shadow System

```typescript
const shadows = {
  sm: 'shadow-sm',
  DEFAULT: 'shadow',
  md: 'shadow-md',
  lg: 'shadow-lg',
  xl: 'shadow-xl',
  '2xl': 'shadow-2xl',
  inner: 'shadow-inner',
  // Medical-grade elevation
  card: 'shadow-[0_2px_8px_rgba(0,0,0,0.08)]',
  modal: 'shadow-[0_8px_32px_rgba(0,0,0,0.12)]',
  dropdown: 'shadow-[0_4px_16px_rgba(0,0,0,0.10)]',
}
```

### 3.3 Animation Guidelines

```typescript
// Smooth medical-grade animations
const animations = {
  // Transitions
  transition: {
    fast: 'transition-all duration-150 ease-in-out',
    base: 'transition-all duration-200 ease-in-out',
    slow: 'transition-all duration-300 ease-in-out',
  },
  
  // Hover effects
  hover: {
    scale: 'hover:scale-[1.02]',
    lift: 'hover:-translate-y-0.5',
    glow: 'hover:shadow-lg hover:shadow-primary-200/50',
  },
  
  // Loading states
  pulse: 'animate-pulse',
  spin: 'animate-spin',
  bounce: 'animate-bounce',
}
```

## 4. Responsive Design Patterns

### 4.1 Breakpoint System

```typescript
const breakpoints = {
  sm: '640px',   // Mobile landscape
  md: '768px',   // Tablet
  lg: '1024px',  // Desktop
  xl: '1280px',  // Large desktop
  '2xl': '1536px' // Wide screen
}
```

### 4.2 Responsive Component Example

```typescript
// Responsive data table that becomes cards on mobile
export function ResponsiveMedicalRecords({ records }: Props) {
  return (
    <>
      {/* Desktop view */}
      <div className="hidden md:block">
        <MedicalRecordsTable data={records} />
      </div>
      
      {/* Mobile view */}
      <div className="block md:hidden space-y-4">
        {records.map((record) => (
          <MedicalRecordCard 
            key={record.id} 
            record={record} 
            compact 
          />
        ))}
      </div>
    </>
  )
}
```

## 5. Accessibility Components

### 5.1 Skip Navigation

```typescript
export function SkipNavigation() {
  return (
    <a
      href="#main-content"
      className="sr-only focus:not-sr-only focus:absolute focus:left-4 focus:top-4 focus:z-50 focus:rounded-md focus:bg-primary-700 focus:px-4 focus:py-2 focus:text-white"
    >
      Skip to main content
    </a>
  )
}
```

### 5.2 Screen Reader Announcements

```typescript
export function LiveRegion() {
  return (
    <>
      <div 
        role="status" 
        aria-live="polite" 
        aria-atomic="true" 
        className="sr-only"
      >
        {/* Announcements for screen readers */}
      </div>
      <div 
        role="alert" 
        aria-live="assertive" 
        aria-atomic="true" 
        className="sr-only"
      >
        {/* Urgent announcements */}
      </div>
    </>
  )
}
```

## 6. Component Documentation Template

```typescript
/**
 * MedicalRecordForm - Comprehensive form for medical record data entry
 * 
 * @component
 * @example
 * ```tsx
 * <MedicalRecordForm
 *   mode="create"
 *   onSubmit={(data) => console.log(data)}
 *   defaultValues={{
 *     patientCode: 'TT00001'
 *   }}
 * />
 * ```
 * 
 * @param {MedicalRecordFormProps} props - Component props
 * @param {'create' | 'edit'} props.mode - Form mode
 * @param {(data: MedicalRecord) => void} props.onSubmit - Submit handler
 * @param {Partial<MedicalRecord>} props.defaultValues - Default form values
 * 
 * @returns {JSX.Element} Medical record form component
 * 
 * @since 1.0.0
 * @see {@link https://chromoforge.docs/components/medical-record-form}
 */
```

---

## Summary

This component hierarchy provides a complete UI system following the ChromoForge Design System with:

- ✅ Professional medical-grade interface
- ✅ Teal gradient color system
- ✅ Thai language support with Buddhist Era dates
- ✅ Accessible components (WCAG 2.1 AA)
- ✅ Responsive design patterns
- ✅ Consistent spacing and typography
- ✅ Performance-optimized components
- ✅ Type-safe with TypeScript

The component architecture ensures consistency, reusability, and maintainability across the entire ChromoForge Admin Dashboard application.