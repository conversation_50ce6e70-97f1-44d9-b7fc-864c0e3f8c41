# ChromoForge Admin Dashboard - Technical Specification

## Version 1.0 | Agent OS Technical Phase
**Last Updated**: 2025-01-05
**Document Status**: Official Technical Specification
**Technical Lead**: System Architect
**Engineering Team**: Frontend, Backend, Database, Security

---

## 1. System Architecture Overview

### 1.1 High-Level Architecture
```
┌─────────────────────────────────────────────────────────────────┐
│                        Client Layer                              │
├─────────────────────────────────────────────────────────────────┤
│  Next.js 14 App Router │ React Server Components │ Tailwind CSS │
│  TanStack Query        │ Zustand               │ Radix UI      │
└────────────────────┬───────────────────────────┴────────────────┘
                     │ HTTPS
┌────────────────────▼───────────────────────────────────────────┐
│                    Application Layer                            │
├─────────────────────────────────────────────────────────────────┤
│  API Routes          │ Middleware            │ Auth Guards     │
│  Business Logic      │ Validation            │ Rate Limiting   │
└────────────────────┬───────────────────────────┴────────────────┘
                     │ Supabase Client
┌────────────────────▼───────────────────────────────────────────┐
│                     Data Layer                                  │
├─────────────────────────────────────────────────────────────────┤
│  Supabase PostgreSQL │ Row Level Security    │ pgcrypto       │
│  Real-time Updates   │ Edge Functions        │ Storage        │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 Component Architecture
```
src/
├── app/                          # Next.js App Router
│   ├── (auth)/                  # Authentication routes
│   │   ├── login/
│   │   ├── logout/
│   │   └── forgot-password/
│   ├── (dashboard)/             # Protected dashboard routes
│   │   ├── page.tsx            # Dashboard overview
│   │   ├── records/            # Medical records management
│   │   ├── analytics/          # Analytics views
│   │   └── admin/              # Admin functions
│   └── api/                     # API endpoints
│       ├── records/
│       ├── users/
│       └── audit/
├── components/                   # React components
│   ├── ui/                      # Base UI components
│   ├── features/                # Feature components
│   └── layouts/                 # Layout components
├── lib/                         # Core libraries
│   ├── supabase/               # Database client
│   ├── auth/                   # Authentication
│   └── api/                    # API utilities
└── domains/                     # Domain logic
    ├── medical-records/
    ├── users/
    └── audit/
```

## 2. Database Integration Strategy

### 2.1 Existing Schema Utilization

#### Core Tables Integration
```sql
-- Existing tables to integrate with:
-- 1. organizations (multi-tenant support)
-- 2. user_profiles (RBAC)
-- 3. documents (PDF tracking)
-- 4. medical_extractions (extracted data)
-- 5. audit_logs (compliance)

-- Additional tables for admin dashboard
CREATE TABLE dashboard_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id),
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE saved_searches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id),
    name TEXT NOT NULL,
    filters JSONB NOT NULL,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE export_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    user_id UUID NOT NULL REFERENCES user_profiles(id),
    status export_status NOT NULL DEFAULT 'pending',
    format TEXT NOT NULL CHECK (format IN ('csv', 'excel', 'pdf')),
    filters JSONB,
    file_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);
```

### 2.2 Database Access Patterns

#### Repository Pattern Implementation
```typescript
// lib/repositories/medical-records.repository.ts
export class MedicalRecordsRepository extends BaseRepository<MedicalRecord> {
  constructor(supabase: SupabaseClient) {
    super(supabase, 'medical_extractions')
  }

  async findByOrganization(
    orgId: string,
    options: QueryOptions
  ): Promise<PaginatedResult<MedicalRecord>> {
    const query = this.supabase
      .from('current_medical_extractions')
      .select('*', { count: 'exact' })
      .eq('organization_id', orgId)

    // Apply filters
    if (options.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        query.filter(key, 'eq', value)
      })
    }

    // Apply sorting
    if (options.orderBy) {
      query.order(options.orderBy.column, {
        ascending: options.orderBy.ascending
      })
    }

    // Apply pagination
    const { data, error, count } = await query
      .range(
        options.page * options.pageSize,
        (options.page + 1) * options.pageSize - 1
      )

    if (error) throw error

    return {
      data: data || [],
      total: count || 0,
      page: options.page,
      pageSize: options.pageSize
    }
  }

  async updateWithAudit(
    id: string,
    updates: Partial<MedicalRecord>,
    userId: string
  ): Promise<MedicalRecord> {
    // Start transaction
    const { data, error } = await this.supabase.rpc(
      'update_medical_record_with_audit',
      {
        record_id: id,
        updates: updates,
        user_id: userId
      }
    )

    if (error) throw error
    return data
  }
}
```

### 2.3 Real-time Subscriptions

```typescript
// hooks/useRealtimeRecords.ts
export function useRealtimeRecords(organizationId: string) {
  const queryClient = useQueryClient()
  
  useEffect(() => {
    const subscription = supabase
      .channel('medical_records_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'medical_extractions',
          filter: `organization_id=eq.${organizationId}`
        },
        (payload) => {
          // Invalidate and refetch queries
          queryClient.invalidateQueries({
            queryKey: ['medical-records', organizationId]
          })
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [organizationId, queryClient])
}
```

## 3. API Design

### 3.1 RESTful API Endpoints

#### Medical Records API
```typescript
// GET /api/records
// Query params: page, pageSize, sortBy, filters
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get('page') || '0')
  const pageSize = parseInt(searchParams.get('pageSize') || '20')
  
  const records = await recordsService.getRecords({
    page,
    pageSize,
    filters: parseFilters(searchParams),
    orderBy: parseOrderBy(searchParams)
  })
  
  return NextResponse.json(records)
}

// PUT /api/records/[id]
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  const body = await request.json()
  const validation = updateRecordSchema.safeParse(body)
  
  if (!validation.success) {
    return NextResponse.json(
      { error: validation.error },
      { status: 400 }
    )
  }
  
  const updated = await recordsService.updateRecord(
    params.id,
    validation.data
  )
  
  return NextResponse.json(updated)
}

// DELETE /api/records/[id]
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  const { searchParams } = new URL(request.url)
  const hardDelete = searchParams.get('hard') === 'true'
  
  if (hardDelete) {
    // Verify admin role and 2FA
    const is2FAVerified = await verify2FA(request)
    if (!is2FAVerified) {
      return NextResponse.json(
        { error: '2FA verification required' },
        { status: 403 }
      )
    }
  }
  
  await recordsService.deleteRecord(params.id, { hardDelete })
  return NextResponse.json({ success: true })
}
```

### 3.2 API Response Format

```typescript
// Standard API response format
interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: unknown
  }
  meta?: {
    page?: number
    pageSize?: number
    total?: number
    timestamp: string
  }
}

// Pagination response
interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: {
    page: number
    pageSize: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
    timestamp: string
  }
}
```

### 3.3 API Validation Schemas

```typescript
// lib/validators/medical-records.ts
import { z } from 'zod'

export const medicalRecordSchema = z.object({
  patientCode: z.string().regex(/^TT\d+$/),
  sampleCode: z.string().length(6),
  investigation: z.enum([
    'K-TRACK',
    'SPOT-MAS',
    'K4CARE',
    'K-TRACK MET'
  ]),
  patientNameTh: z.string().min(1).max(200),
  patientNameEn: z.string().min(1).max(200),
  gender: z.enum(['Male', 'Female', 'Other']),
  dobGregorian: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  dobBuddhistEra: z.string().regex(/^\d{2}\/\d{2}\/\d{4}$/),
  patientContactNo: z.string().regex(/^0\d{9}$/),
  placeOfTreatment: z.string().max(200),
  referringPhysicianTh: z.string().max(200),
  referringPhysicianEn: z.string().max(200),
  referringPhysicianMdCode: z.string().max(50),
  referringPhysicianEmail: z.string().email()
})

export const updateRecordSchema = medicalRecordSchema.partial()

export const recordFiltersSchema = z.object({
  status: z.enum(['active', 'deleted']).optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  search: z.string().optional(),
  investigation: z.string().optional()
})
```

## 4. Component Architecture

### 4.1 Core Components

#### DataTable Component
```typescript
// components/features/DataTable/DataTable.tsx
export interface DataTableProps<T> {
  data: T[]
  columns: ColumnDef<T>[]
  loading?: boolean
  pagination?: PaginationState
  onPaginationChange?: (pagination: PaginationState) => void
  sorting?: SortingState
  onSortingChange?: (sorting: SortingState) => void
  filtering?: FilteringState
  onFilteringChange?: (filtering: FilteringState) => void
  onRowClick?: (row: T) => void
  bulkActions?: BulkAction<T>[]
  emptyState?: ReactNode
}

export function DataTable<T>({
  data,
  columns,
  loading,
  pagination,
  onPaginationChange,
  // ... other props
}: DataTableProps<T>) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    // ... other table options
  })

  return (
    <div className="relative overflow-hidden rounded-lg border border-gray-200">
      {loading && <TableSkeleton />}
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHead key={header.id}>
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                onClick={() => onRowClick?.(row.original)}
                className="cursor-pointer hover:bg-gray-50"
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(
                      cell.column.columnDef.cell,
                      cell.getContext()
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell
                colSpan={columns.length}
                className="h-24 text-center"
              >
                {emptyState || 'No results found'}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      {pagination && (
        <TablePagination
          pagination={pagination}
          onPaginationChange={onPaginationChange}
        />
      )}
    </div>
  )
}
```

#### Role-Based Component
```typescript
// components/auth/RoleGuard.tsx
interface RoleGuardProps {
  allowedRoles: UserRole[]
  fallback?: ReactNode
  children: ReactNode
}

export function RoleGuard({
  allowedRoles,
  fallback = null,
  children
}: RoleGuardProps) {
  const { user } = useAuth()
  
  if (!user || !allowedRoles.includes(user.role)) {
    return <>{fallback}</>
  }
  
  return <>{children}</>
}

// Usage
<RoleGuard allowedRoles={['admin', 'editor']}>
  <EditButton onClick={handleEdit} />
</RoleGuard>
```

### 4.2 Domain Components

#### Medical Record Form
```typescript
// domains/medical-records/components/MedicalRecordForm.tsx
export function MedicalRecordForm({
  record,
  onSubmit,
  mode = 'edit'
}: MedicalRecordFormProps) {
  const form = useForm<MedicalRecord>({
    resolver: zodResolver(medicalRecordSchema),
    defaultValues: record || getDefaultValues()
  })

  const { mutate: saveRecord, isLoading } = useMutation({
    mutationFn: (data: MedicalRecord) => 
      mode === 'create' 
        ? recordsService.create(data)
        : recordsService.update(record!.id, data),
    onSuccess: () => {
      toast.success('Record saved successfully')
      onSubmit()
    },
    onError: (error) => {
      toast.error('Failed to save record')
      console.error(error)
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(saveRecord)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="patientCode"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Patient Code</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="TT04035"
                    pattern="^TT\d+$"
                  />
                </FormControl>
                <FormDescription>
                  Must start with TT followed by digits
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Other form fields... */}
          
          <FormField
            control={form.control}
            name="dobBuddhistEra"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Date of Birth (Buddhist Era)</FormLabel>
                <FormControl>
                  <DatePicker
                    {...field}
                    format="DD/MM/YYYY"
                    calendar="buddhist"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        <div className="flex justify-end gap-4 mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={() => form.reset()}
          >
            Reset
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Spinner className="mr-2" />}
            {mode === 'create' ? 'Create' : 'Update'}
          </Button>
        </div>
      </form>
    </Form>
  )
}
```

### 4.3 Layout Components

#### Dashboard Layout
```typescript
// components/layouts/DashboardLayout.tsx
export function DashboardLayout({ children }: { children: ReactNode }) {
  const { user } = useAuth()
  const router = useRouter()
  
  return (
    <div className="min-h-screen bg-gray-50">
      <NavigationHeader user={user} />
      
      <div className="flex h-[calc(100vh-64px)]">
        <Sidebar
          navigation={getNavigationByRole(user.role)}
          className="w-64 border-r border-gray-200"
        />
        
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto p-6">
            {children}
          </div>
        </main>
      </div>
      
      <Toaster position="bottom-right" />
    </div>
  )
}
```

## 5. State Management

### 5.1 Client State (Zustand)

```typescript
// stores/ui.store.ts
interface UIState {
  sidebarOpen: boolean
  theme: 'light' | 'dark'
  language: 'th' | 'en'
  toggleSidebar: () => void
  setTheme: (theme: 'light' | 'dark') => void
  setLanguage: (language: 'th' | 'en') => void
}

export const useUIStore = create<UIState>((set) => ({
  sidebarOpen: true,
  theme: 'light',
  language: 'th',
  toggleSidebar: () => set((state) => ({ 
    sidebarOpen: !state.sidebarOpen 
  })),
  setTheme: (theme) => set({ theme }),
  setLanguage: (language) => set({ language })
}))
```

### 5.2 Server State (TanStack Query)

```typescript
// hooks/queries/useMedicalRecords.ts
export function useMedicalRecords(options: QueryOptions) {
  const { organizationId } = useAuth()
  
  return useQuery({
    queryKey: ['medical-records', organizationId, options],
    queryFn: () => recordsService.getRecords(organizationId, options),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useMedicalRecord(id: string) {
  return useQuery({
    queryKey: ['medical-record', id],
    queryFn: () => recordsService.getRecord(id),
    enabled: !!id
  })
}

export function useUpdateMedicalRecord() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: UpdateRecordParams) => 
      recordsService.updateRecord(id, data),
    onSuccess: (data, variables) => {
      // Update cache
      queryClient.setQueryData(['medical-record', variables.id], data)
      // Invalidate list
      queryClient.invalidateQueries({ 
        queryKey: ['medical-records'] 
      })
    }
  })
}
```

## 6. Authentication & Authorization

### 6.1 Authentication Flow

```typescript
// lib/auth/auth.service.ts
export class AuthService {
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    if (error) throw error
    
    // Fetch user profile with role
    const profile = await this.getUserProfile(data.user.id)
    
    return { user: data.user, profile }
  }
  
  async verifyRole(userId: string, requiredRole: UserRole) {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('id', userId)
      .single()
    
    if (error) throw error
    
    return hasRequiredRole(data.role, requiredRole)
  }
  
  async verify2FA(userId: string, token: string) {
    // 2FA verification logic
    return await supabase.rpc('verify_2fa_token', {
      user_id: userId,
      token: token
    })
  }
}
```

### 6.2 Middleware Protection

```typescript
// middleware.ts
export async function middleware(request: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req: request, res })
  
  const {
    data: { session },
  } = await supabase.auth.getSession()
  
  // Protected routes
  if (request.nextUrl.pathname.startsWith('/dashboard')) {
    if (!session) {
      return NextResponse.redirect(new URL('/login', request.url))
    }
    
    // Admin routes
    if (request.nextUrl.pathname.startsWith('/dashboard/admin')) {
      const hasAdminRole = await verifyAdminRole(session.user.id)
      if (!hasAdminRole) {
        return NextResponse.redirect(new URL('/dashboard', request.url))
      }
    }
  }
  
  return res
}

export const config = {
  matcher: ['/dashboard/:path*', '/api/:path*']
}
```

## 7. Performance Optimization

### 7.1 Data Fetching Optimization

```typescript
// Parallel data loading for dashboard
export async function DashboardPage() {
  const statsPromise = getStats()
  const recentRecordsPromise = getRecentRecords()
  const activitiesPromise = getActivities()
  
  const [stats, recentRecords, activities] = await Promise.all([
    statsPromise,
    recentRecordsPromise,
    activitiesPromise
  ])
  
  return (
    <DashboardLayout>
      <Suspense fallback={<StatsSkeleton />}>
        <StatsCards stats={stats} />
      </Suspense>
      
      <Suspense fallback={<TableSkeleton />}>
        <RecentRecordsTable records={recentRecords} />
      </Suspense>
      
      <Suspense fallback={<ActivitySkeleton />}>
        <ActivityFeed activities={activities} />
      </Suspense>
    </DashboardLayout>
  )
}
```

### 7.2 Bundle Optimization

```javascript
// next.config.js
module.exports = {
  modularizeImports: {
    '@radix-ui/react-icons': {
      transform: '@radix-ui/react-icons/dist/{{member}}'
    },
    'lodash': {
      transform: 'lodash/{{member}}'
    }
  },
  
  experimental: {
    optimizeCss: true,
    optimizePackageImports: [
      '@radix-ui/react-*',
      'date-fns',
      'react-hook-form'
    ]
  }
}
```

### 7.3 Caching Strategy

```typescript
// lib/cache/cache.service.ts
export class CacheService {
  private redis: Redis
  
  async get<T>(key: string): Promise<T | null> {
    const cached = await this.redis.get(key)
    return cached ? JSON.parse(cached) : null
  }
  
  async set<T>(
    key: string, 
    value: T, 
    ttl: number = 300
  ): Promise<void> {
    await this.redis.setex(key, ttl, JSON.stringify(value))
  }
  
  async invalidate(pattern: string): Promise<void> {
    const keys = await this.redis.keys(pattern)
    if (keys.length > 0) {
      await this.redis.del(...keys)
    }
  }
}

// Usage in API
export async function GET(request: Request) {
  const cacheKey = `records:${organizationId}:${page}`
  
  // Check cache
  const cached = await cache.get(cacheKey)
  if (cached) {
    return NextResponse.json(cached)
  }
  
  // Fetch from database
  const records = await recordsService.getRecords(...)
  
  // Cache results
  await cache.set(cacheKey, records, 300) // 5 minutes
  
  return NextResponse.json(records)
}
```

## 8. Testing Strategy

### 8.1 Unit Testing

```typescript
// __tests__/services/medical-records.service.test.ts
describe('MedicalRecordsService', () => {
  let service: MedicalRecordsService
  let mockSupabase: MockSupabaseClient
  
  beforeEach(() => {
    mockSupabase = createMockSupabaseClient()
    service = new MedicalRecordsService(mockSupabase)
  })
  
  describe('updateRecord', () => {
    it('should update record with audit trail', async () => {
      const recordId = 'test-id'
      const updates = { patientNameEn: 'John Doe' }
      const userId = 'user-id'
      
      mockSupabase.rpc.mockResolvedValueOnce({
        data: { id: recordId, ...updates },
        error: null
      })
      
      const result = await service.updateRecord(
        recordId, 
        updates, 
        userId
      )
      
      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'update_medical_record_with_audit',
        {
          record_id: recordId,
          updates,
          user_id: userId
        }
      )
      expect(result).toMatchObject(updates)
    })
  })
})
```

### 8.2 Integration Testing

```typescript
// __tests__/api/records.test.ts
import { createMocks } from 'node-mocks-http'
import { GET, PUT } from '@/app/api/records/[id]/route'

describe('/api/records/[id]', () => {
  describe('PUT', () => {
    it('should validate input data', async () => {
      const { req, res } = createMocks({
        method: 'PUT',
        body: {
          patientCode: 'INVALID' // Should start with TT
        }
      })
      
      await PUT(req, { params: { id: 'test-id' } })
      
      expect(res._getStatusCode()).toBe(400)
      expect(JSON.parse(res._getData())).toMatchObject({
        error: expect.objectContaining({
          issues: expect.arrayContaining([
            expect.objectContaining({
              path: ['patientCode']
            })
          ])
        })
      })
    })
  })
})
```

### 8.3 E2E Testing

```typescript
// e2e/medical-records.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Medical Records Management', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login')
    await page.fill('[name="email"]', '<EMAIL>')
    await page.fill('[name="password"]', 'password')
    await page.click('button[type="submit"]')
    await page.waitForNavigation()
  })
  
  test('should edit medical record', async ({ page }) => {
    await page.goto('/dashboard/records')
    
    // Click first record
    await page.click('table tbody tr:first-child')
    
    // Edit patient name
    await page.fill('[name="patientNameEn"]', 'Updated Name')
    
    // Save
    await page.click('button:has-text("Update")')
    
    // Verify success message
    await expect(
      page.locator('text=Record saved successfully')
    ).toBeVisible()
  })
})
```

## 9. Deployment Architecture

### 9.1 Infrastructure

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - NEXT_PUBLIC_SUPABASE_URL=${SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    ports:
      - "3000:3000"
    depends_on:
      - redis
      
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
      
volumes:
  redis_data:
```

### 9.2 CI/CD Pipeline

```yaml
# .github/workflows/deploy.yml
name: Deploy

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: pnpm install
        
      - name: Run tests
        run: |
          pnpm test
          pnpm test:e2e
          
      - name: Build
        run: pnpm build
        
  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Vercel
        uses: vercel/action@v28
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
```

## 10. Monitoring & Observability

### 10.1 Application Monitoring

```typescript
// lib/monitoring/monitoring.ts
import * as Sentry from '@sentry/nextjs'

export function initMonitoring() {
  if (process.env.NODE_ENV === 'production') {
    Sentry.init({
      dsn: process.env.SENTRY_DSN,
      tracesSampleRate: 0.1,
      integrations: [
        new Sentry.BrowserTracing(),
        new Sentry.Replay()
      ],
      beforeSend(event, hint) {
        // Remove PII
        if (event.request?.cookies) {
          delete event.request.cookies
        }
        return event
      }
    })
  }
}

// Performance monitoring
export function measurePerformance(name: string) {
  const start = performance.now()
  
  return {
    end: () => {
      const duration = performance.now() - start
      
      // Send to analytics
      if (window.gtag) {
        window.gtag('event', 'timing_complete', {
          name,
          value: Math.round(duration)
        })
      }
      
      // Log to Sentry
      Sentry.addBreadcrumb({
        category: 'performance',
        message: `${name} took ${duration}ms`,
        level: 'info'
      })
    }
  }
}
```

## 11. Security Implementation

### 11.1 Content Security Policy

```typescript
// middleware.ts
export function middleware(request: NextRequest) {
  const response = NextResponse.next()
  
  // Content Security Policy
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-eval' 'unsafe-inline' *.supabase.co; " +
    "style-src 'self' 'unsafe-inline'; " +
    "img-src 'self' data: blob: *.supabase.co; " +
    "font-src 'self' data:; " +
    "connect-src 'self' *.supabase.co wss://*.supabase.co; " +
    "frame-ancestors 'none';"
  )
  
  // Other security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=()'
  )
  
  return response
}
```

### 11.2 Input Sanitization

```typescript
// lib/security/sanitize.ts
import DOMPurify from 'isomorphic-dompurify'

export function sanitizeInput(input: string): string {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  })
}

export function sanitizeHTML(html: string): string {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
    ALLOWED_ATTR: []
  })
}

// Usage in API
export async function POST(request: Request) {
  const body = await request.json()
  
  const sanitized = {
    ...body,
    patientNameTh: sanitizeInput(body.patientNameTh),
    patientNameEn: sanitizeInput(body.patientNameEn),
    placeOfTreatment: sanitizeInput(body.placeOfTreatment)
  }
  
  // Continue with sanitized data
}
```

---

## Document Control

- **Version**: 1.0
- **Status**: Approved
- **Last Technical Review**: 2025-01-05
- **Next Review**: 2025-03-05
- **Technical Approvers**: System Architect, Lead Developer, Security Engineer