# ChromoForge Base Dependencies
# Core system packages that change infrequently
# Used in multi-stage Dockerfile optimization

# Core Python packages
setuptools>=65.0.0
wheel>=0.37.0
pip>=22.0.0

# HTTP and networking
requests>=2.28.0
urllib3>=1.26.0
certifi>=2022.9.0

# JSON and data handling
simplejson>=3.18.0
orjson>=3.8.0

# Logging and monitoring
structlog>=22.1.0
python-json-logger>=2.0.0

# Configuration management
python-dotenv>=0.20.0
configparser>=5.3.0

# Security foundations
cryptography>=38.0.0
bcrypt>=4.0.0

# Performance monitoring
psutil>=5.9.0