version: '3.8'

# ChromoForge AgentOS-Compliant Docker Compose Configuration
# Optimized for production readiness and security

services:
  chromoforge:
    build:
      context: .
      dockerfile: Dockerfile.optimized
      target: production
      cache_from:
        - chromoforge:${CHROMOFORGE_VERSION:-1.0.0}
      args:
        BUILDKIT_INLINE_CACHE: 1
        CHROMOFORGE_VERSION: ${CHROMOFORGE_VERSION:-1.0.0}
      labels:
        - "org.opencontainers.image.title=ChromoForge OCR"
        - "org.opencontainers.image.version=${CHROMOFORGE_VERSION:-1.0.0}"
    
    image: "chromoforge:${CHROMOFORGE_VERSION:-1.0.0}"
    container_name: chromoforge-app
    
    # AgentOS Resource Constraints (Required)
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.25'
      restart_policy:
        condition: unless-stopped
        delay: 5s
        max_attempts: 3
        window: 120s
    
    # Environment configuration (Security-focused)
    env_file:
      - .env
    environment:
      # Core application settings
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - MAX_CONCURRENT_REQUESTS=${MAX_CONCURRENT_REQUESTS:-10}
      - CONFIDENCE_THRESHOLD=${CONFIDENCE_THRESHOLD:-0.7}
      - OBFUSCATION_METHOD=${OBFUSCATION_METHOD:-black_box}
      
      # Security settings
      - CHROME_FORGE_ENV=production
      - PYTHONHASHSEED=random
      - PYTHONIOENCODING=utf-8
      
      # Performance tuning
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
    
    # Secure volume mounts (Minimal for security)
    volumes:
      # Output directories only (no source code in production)
      - type: bind
        source: ./processed
        target: /app/processed
        bind:
          create_host_path: true
      - type: bind
        source: ./temp
        target: /app/temp
        bind:
          create_host_path: true
      
      # Development volume (conditional)
      - type: bind
        source: ./src
        target: /app/src
        read_only: true
        bind:
          create_host_path: false
    
    working_dir: /app
    restart: unless-stopped
    
    # Production health check (AgentOS requirement)
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || python -c 'import sys; import src.main; sys.exit(0)'"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    networks:
      - chromoforge
    
    # Security hardening (AgentOS compliance)
    security_opt:
      - no-new-privileges:true
    read_only: false  # Application needs write access to /app/temp
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    cap_drop:
      - ALL
    cap_add:
      - SETUID
      - SETGID
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Port mapping (conditional for development)
    ports:
      - "${CHROMOFORGE_PORT:-8000}:8000"

  chromoforge-dashboard:
    build:
      context: ./dashboard
      dockerfile: Dockerfile
      target: ${DASHBOARD_TARGET:-development}
      cache_from:
        - chromoforge-dashboard:${CHROMOFORGE_VERSION:-1.0.0}
      args:
        BUILDKIT_INLINE_CACHE: 1
        CHROMOFORGE_VERSION: ${CHROMOFORGE_VERSION:-1.0.0}
    
    image: "chromoforge-dashboard:${CHROMOFORGE_VERSION:-1.0.0}"
    container_name: chromoforge-dashboard
    
    # Resource constraints for dashboard
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.1'
      restart_policy:
        condition: unless-stopped
        delay: 5s
        max_attempts: 3
        window: 120s
    
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - VITE_API_URL=http://chromoforge:8000
      - VITE_APP_VERSION=${CHROMOFORGE_VERSION:-1.0.0}
      - VITE_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - VITE_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
    
    ports:
      - "3001:3001"
    
    # Conditional development volumes
    volumes:
      - type: bind
        source: ./dashboard/src
        target: /app/src
        read_only: true
        bind:
          create_host_path: false
      # Named volume for node_modules performance
      - chromoforge-dashboard-modules:/app/node_modules
    
    networks:
      - chromoforge
    
    # Service dependency with health check
    depends_on:
      chromoforge:
        condition: service_healthy
    
    restart: unless-stopped
    
    # Dashboard health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Security hardening
    security_opt:
      - no-new-privileges:true
    read_only: false  # Node.js needs write access for temp files
    tmpfs:
      - /tmp:noexec,nosuid,size=50m
    cap_drop:
      - ALL
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

  # Optional: Monitoring service for production
  chromoforge-monitor:
    image: prom/node-exporter:latest
    container_name: chromoforge-monitor
    profiles: ["monitoring"]
    
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    
    ports:
      - "9100:9100"
    
    networks:
      - chromoforge
    
    restart: unless-stopped
    
    # Resource limits for monitoring
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'

# Networks configuration (AgentOS compliant)
networks:
  chromoforge:
    driver: bridge
    name: chromoforge-network
    labels:
      - "org.chromoforge.network=main"
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: chromoforge-br0

# Named volumes (AgentOS compliant)
volumes:
  chromoforge-dashboard-modules:
    name: chromoforge-dashboard-modules
    labels:
      - "org.chromoforge.volume=node-modules"
      - "org.chromoforge.version=${CHROMOFORGE_VERSION:-1.0.0}"

# Secrets configuration (Production deployment)
secrets:
  google_api_key:
    file: ./secrets/google_api_key.txt
  supabase_url:
    file: ./secrets/supabase_url.txt
  supabase_anon_key:
    file: ./secrets/supabase_anon_key.txt
  supabase_service_key:
    file: ./secrets/supabase_service_key.txt

# Configuration profiles for different environments
x-development-overrides: &development-overrides
  volumes:
    - ./src:/app/src:ro
  environment:
    - LOG_LEVEL=DEBUG
    - CHROME_FORGE_ENV=development

x-production-overrides: &production-overrides
  read_only: true
  security_opt:
    - no-new-privileges:true
  cap_drop:
    - ALL
  environment:
    - LOG_LEVEL=INFO
    - CHROME_FORGE_ENV=production

# Extension for different deployment scenarios
profiles:
  development:
    extends:
      service: chromoforge
    <<: *development-overrides
    
  production:
    extends:
      service: chromoforge
    <<: *production-overrides