# Docker Cleanup Agent Coordination Protocol

## Agent Handoff Sequence

This document defines the coordination protocol for the multi-agent orchestration of ChromoForge Docker cleanup operations.

## Agent Roles & Responsibilities

### 1. semantic-refactoring-expert (Phase 1 - Complete)
**Status**: ✅ COMPLETE
**Responsibilities**:
- Initial resource analysis
- Baseline metrics collection
- Problem space definition
- Initial safety assessment

**Deliverables**:
- Baseline analysis: 32.86GB reclaimable
- Risk assessment: 1 critical service identified
- Cleanup strategy: Multi-phase approach defined

### 2. project-orchestrator (Phase 2 - Current)
**Status**: 🔄 ACTIVE
**Timeline**: T+0 to T+15 minutes
**Responsibilities**:
- Overall coordination and scheduling
- Agent task allocation
- Timeline management
- Risk mitigation planning
- Communication protocols

**Current Deliverables**:
- ✅ Orchestration plan created
- ✅ Executor scripts generated
- ✅ Monitoring tools prepared
- ✅ Pre-flight validation ready
- 🔄 Coordinating handoff to system-architect

### 3. system-architect (Phase 3A)
**Status**: ⏳ PENDING
**Timeline**: T+15 to T+25 minutes
**Responsibilities**:
- System state validation
- Backup strategy implementation
- Architecture preservation
- Rollback procedure design

**Expected Deliverables**:
- Complete system backup
- Architecture documentation
- Rollback procedures tested
- Safety protocols verified

### 4. semantic-dev-specialist (Phase 3B)
**Status**: ⏳ PENDING
**Timeline**: T+25 to T+55 minutes
**Responsibilities**:
- Cleanup execution
- Real-time monitoring
- Service continuity
- Progressive cleanup stages

**Expected Deliverables**:
- 32.86GB space recovered
- Zero service disruption
- Staged cleanup complete
- Audit trail maintained

### 5. semantic-qa-specialist (Phase 3C)
**Status**: ⏳ PENDING
**Timeline**: T+55 to T+75 minutes
**Responsibilities**:
- Service verification
- Performance validation
- Space recovery confirmation
- Quality assurance

**Expected Deliverables**:
- Health check reports
- Performance metrics
- Recovery validation
- Maintenance schedule

## Coordination Matrix

| Phase | Time | Lead Agent | Support Agent | Critical Decision Points |
|-------|------|------------|---------------|--------------------------|
| 2 | T+0-15 | project-orchestrator | system-architect | Approval to proceed |
| 3A | T+15-25 | system-architect | semantic-dev-specialist | Backup verification |
| 3B | T+25-55 | semantic-dev-specialist | project-orchestrator | Service monitoring |
| 3C | T+55-75 | semantic-qa-specialist | project-orchestrator | Final validation |

## Communication Protocols

### Inter-Agent Messages

#### Handoff Message Format
```yaml
handoff:
  from: project-orchestrator
  to: system-architect
  timestamp: T+15
  status: ready
  deliverables:
    - orchestration_plan.md
    - executor_scripts/
    - monitoring_tools/
  prerequisites_met:
    - critical_service_identified: true
    - backup_strategy_defined: true
    - rollback_procedures_ready: true
  next_actions:
    - validate_system_state
    - create_backups
    - prepare_safety_nets
```

### Status Update Protocol
```yaml
status_update:
  agent: semantic-dev-specialist
  phase: 3B
  timestamp: T+35
  progress: 40%
  current_stage: "Dashboard image cleanup"
  space_recovered: "6.2GB"
  service_status: "operational"
  issues: []
  eta_completion: T+55
```

### Alert Escalation
```yaml
alert:
  severity: [INFO|WARN|ERROR|CRITICAL]
  agent: reporting_agent
  timestamp: current_time
  issue: description
  impact: service_impact
  action_required: true|false
  escalate_to: [project-orchestrator|human]
```

## Checkpoint Validation Protocol

### Checkpoint Structure
```yaml
checkpoint:
  id: CP1
  name: "Pre-cleanup validation"
  time: T+15
  validating_agent: system-architect
  criteria:
    - service_running: true
    - backup_complete: true
    - rollback_ready: true
  status: PASS|FAIL
  evidence:
    - logs/checkpoint-CP1.txt
    - screenshots/service-status.png
```

### Validation Requirements

#### CP1: Pre-cleanup (T+15)
- Validator: system-architect
- Must Pass: All criteria
- Failure Action: Abort cleanup

#### CP2: Post-containers (T+30)
- Validator: semantic-dev-specialist
- Must Pass: Service running
- Failure Action: Rollback

#### CP3: Post-images (T+40)
- Validator: semantic-dev-specialist
- Must Pass: Critical image exists
- Failure Action: Restore image

#### CP4: Post-cache (T+50)
- Validator: semantic-dev-specialist
- Must Pass: Service operational
- Failure Action: Service restart

#### CP5: Final (T+60)
- Validator: semantic-qa-specialist
- Must Pass: All quality checks
- Failure Action: Investigation

## Parallel Task Coordination

### Task Distribution
```yaml
parallel_tasks:
  T+25-30:
    agents:
      - semantic-dev-specialist: "Container cleanup"
      - project-orchestrator: "Monitor service"
    synchronization: T+30
    
  T+35-40:
    agents:
      - semantic-dev-specialist: "Image cleanup"
      - system-architect: "Verify backups"
    synchronization: T+40
```

### Resource Locking
```yaml
resource_locks:
  chromoforge-app:
    locked_by: system
    lock_type: read-only
    duration: entire_operation
    
  chromoforge:1.0.0:
    locked_by: system
    lock_type: protected
    duration: entire_operation
```

## Success Metrics

### Phase 2 (Orchestration)
- ✅ Plan created and approved
- ✅ All scripts executable
- ✅ Monitoring active
- ✅ Pre-flight passed

### Phase 3A (Preparation)
- [ ] System backup complete
- [ ] Rollback tested
- [ ] Safety nets in place
- [ ] Ready for execution

### Phase 3B (Execution)
- [ ] 30GB+ space recovered
- [ ] Zero service interruptions
- [ ] All stages complete
- [ ] Audit trail maintained

### Phase 3C (Validation)
- [ ] Service healthy
- [ ] Performance validated
- [ ] Report generated
- [ ] Schedule created

## Agent Performance Tracking

### Metrics Collection
```yaml
agent_metrics:
  project-orchestrator:
    tasks_completed: 8
    time_spent: "15 minutes"
    decisions_made: 12
    success_rate: "100%"
    
  system-architect:
    tasks_completed: 0
    time_spent: "0 minutes"
    decisions_made: 0
    success_rate: "pending"
```

### Knowledge Transfer Log
```yaml
knowledge_transfer:
  - from: semantic-refactoring-expert
    to: project-orchestrator
    timestamp: T-15
    knowledge:
      - baseline_metrics
      - risk_assessment
      - cleanup_opportunity
      
  - from: project-orchestrator
    to: system-architect
    timestamp: T+15
    knowledge:
      - orchestration_plan
      - protected_resources
      - execution_scripts
```

## Emergency Coordination

### Crisis Response Team
1. **Primary**: project-orchestrator
2. **Secondary**: system-architect
3. **Escalation**: Human operator

### Emergency Actions
```bash
# Stop all cleanup operations
docker-cleanup-executor.sh --emergency-stop

# Restore service
./backup/cleanup-*/rollback.sh

# Alert all agents
echo "EMERGENCY: Service disruption detected" | broadcast-to-agents
```

## Post-Operation Review

### Agent Debrief Protocol
1. Each agent submits performance report
2. project-orchestrator compiles lessons learned
3. Update orchestration protocols
4. Archive coordination logs

### Improvement Areas
- [ ] Reduce handoff time between agents
- [ ] Improve parallel task coordination
- [ ] Enhance monitoring granularity
- [ ] Automate more decision points

## Coordination Commands

### Start Orchestration
```bash
# Initialize all agents
./docker-cleanup-preflight.sh && \
./docker-cleanup-monitor.sh & \
./docker-cleanup-executor.sh
```

### Monitor Progress
```bash
# Real-time coordination status
watch -n 2 'cat agent-coordination-status.json | jq .'
```

### Emergency Stop
```bash
# Halt all operations
pkill -f docker-cleanup
docker start chromoforge-app
```

---

**Protocol Version**: 1.0
**Last Updated**: Current Session
**Orchestrator**: project-orchestrator
**Status**: READY FOR PHASE 3A HANDOFF