[tool:pytest]
minversion = 6.0
addopts =
    -ra
    --strict-markers
    --tb=short
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    integration: Real integration tests with external services
    performance: Performance validation tests  
    security: Security validation tests
    slow: Slow tests that may take several minutes
asyncio_mode = auto
filterwarnings =
    ignore::pytest.PytestUnraisableExceptionWarning
    ignore::DeprecationWarning
    ignore:builtin type SwigPyPacked has no __module__ attribute:DeprecationWarning:*
    ignore:builtin type SwigPyObject has no __module__ attribute:DeprecationWarning:*
    ignore:builtin type swigvarlink has no __module__ attribute:DeprecationWarning:*
    ignore:.*SwigPy.*:DeprecationWarning
    ignore:.*swigvarlink.*:DeprecationWarning