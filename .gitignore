# ===================================================================
# CHROMOFORGE MEDICAL DOCUMENT PROCESSING - GITIGNORE v1.0.0
# ===================================================================
# Comprehensive exclusion patterns for medical document processing
# application with strict security and compliance requirements.

# ===================================================================
# ENVIRONMENT & CONFIGURATION FILES
# ===================================================================

# Environment Variables (CRITICAL - Never commit API keys/secrets)
.env
.env.local
.env.production
.env.staging
.env.development
.env.test
.env.*.local
.env.backup
.env.bak

# Configuration backups
config.backup
config.bak
*.config.backup

# ===================================================================
# PYTHON DEVELOPMENT
# ===================================================================

# Bytecode
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Virtual Environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/
.env/
.virtualenv/
pyvenv.cfg

# Pipenv
Pipfile.lock

# Poetry
poetry.lock

# ===================================================================
# IDE & EDITOR FILES
# ===================================================================

# Visual Studio Code
.vscode/
.vscode-test/
*.code-workspace

# IntelliJ IDEA / PyCharm
.idea/
*.iml
*.ipr
*.iws
.idea_modules/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Atom
.atom/

# ===================================================================
# LOGGING & MONITORING
# ===================================================================

# Application logs
*.log
logs/
*.log.*
log/
*.jsonl
*.ndjson

# System logs
syslog
*.out
*.err

# Debug files
debug.log
debug/
*.debug

# ===================================================================
# TEMPORARY & CACHE FILES
# ===================================================================

# Temporary files
temp/
tmp/
*.tmp
*.temp
*.bak
*.backup
*.swp
*.cache

# Cache directories
.cache/
cache/
.pytest_cache/
.mypy_cache/
.ruff_cache/

# ===================================================================
# TEST COVERAGE & REPORTS
# ===================================================================

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.tox/
.nox/

# Test reports
test-reports/
test-output/
.pytest_cache/
junit.xml
coverage.json

# ===================================================================
# JUPYTER NOTEBOOK
# ===================================================================

.ipynb_checkpoints
*.ipynb_checkpoints/
.jupyter/

# ===================================================================
# OPERATING SYSTEM FILES
# ===================================================================

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
.fseventsd
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
ehthumbs.db
Thumbs.db
Thumbs.db:encryptable
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.directory
.Trash-*

# ===================================================================
# MEDICAL DATA & PROCESSING RESULTS (CRITICAL SECURITY)
# ===================================================================

# Medical document directories
processed/
batch-results/
test-results/
results-v2/
diagnostic-results/
test-obfuscation/
test-obfuscation-v2/

# Sample medical documents (NEVER commit real patient data)
original-pdf-examples/
sample-documents/
patient-data/
medical-records/

# Medical document file types
*.pdf
*.PDF
*.jpg
*.jpeg
*.JPG
*.JPEG
*.png
*.PNG
*.tiff
*.tif
*.TIFF
*.TIF
*.bmp
*.BMP
*.gif
*.GIF
*.webp
*.WEBP

# OCR processing results
*_ocr_results.json
*_extraction_results.json
*_pii_results.json
*_coordinates.json
*_diagnostic.json
*obfuscated_*.pdf

# Medical data exports
*.csv
*.xlsx
*.xls
*.json
*.xml
*.hl7
*.dicom
*.dcm

# ===================================================================
# SECRETS & SECURITY (CRITICAL)
# ===================================================================

# API Keys and secrets directories
secrets/
keys/
certificates/
certs/
ssl/
private/
.secrets/

# Certificate files
*.pem
*.key
*.crt
*.cer
*.p12
*.pfx
*.jks
*.keystore

# SSH keys
id_rsa
id_dsa
id_ecdsa
id_ed25519
*.pub

# GPG keys
*.gpg
*.asc

# Authentication tokens
*.token
auth.json
credentials.json

# ===================================================================
# DATABASE FILES
# ===================================================================

# Local databases
*.db
*.sqlite
*.sqlite3
*.db-journal
*.db-wal
*.db-shm

# Database dumps
*.sql.gz
*.dump
*.backup
*.bak

# Migration backups
migrations/backup/
*.migration.backup

# ===================================================================
# DOCKER & CONTAINERIZATION
# ===================================================================

# Docker
.dockerignore
docker-compose.override.yml
docker-compose.local.yml
.docker/

# Container logs
docker-logs/

# ===================================================================
# NODE.JS & FRONTEND (if applicable)
# ===================================================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
.tmp/
.temp/

# ===================================================================
# CHROMOFORGE SPECIFIC EXCLUSIONS
# ===================================================================

# Version control backups
VERSION.backup
VERSION.bak

# Processing artifacts
*.processing
*.in-progress
*.failed

# Audit trails (sensitive)
audit-logs/
compliance-reports/
security-scans/

# Performance benchmarks
benchmarks/
performance-results/

# Development artifacts
dev-notes/
scratch/
playground/

# Backup directories
backups/
*.backup/

# ===================================================================
# COMPLIANCE & REGULATORY
# ===================================================================

# Compliance reports (may contain sensitive data)
compliance/
audit/
regulatory/
hipaa/
gdpr/

# Security scan results
security-reports/
vulnerability-scans/
penetration-tests/

# ===================================================================
# END OF GITIGNORE
# ===================================================================