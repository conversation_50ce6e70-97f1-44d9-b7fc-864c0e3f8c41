#!/bin/bash

# ChromoForge Docker Cleanup Pre-flight Validation
# Comprehensive checks before cleanup orchestration
# Version: 1.0

set -euo pipefail

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
CRITICAL_CONTAINER="chromoforge-app"
CRITICAL_IMAGE="chromoforge:1.0.0"
CRITICAL_NETWORK="chromoforge-network"
MIN_DISK_SPACE_GB=5
VALIDATION_RESULTS=()
WARNINGS=()
ERRORS=()

# Functions
check_pass() {
    local check_name=$1
    local message=$2
    echo -e "${GREEN}✓${NC} $check_name: $message"
    VALIDATION_RESULTS+=("PASS: $check_name - $message")
}

check_warn() {
    local check_name=$1
    local message=$2
    echo -e "${YELLOW}⚠${NC} $check_name: $message"
    WARNINGS+=("$check_name: $message")
    VALIDATION_RESULTS+=("WARN: $check_name - $message")
}

check_fail() {
    local check_name=$1
    local message=$2
    echo -e "${RED}✗${NC} $check_name: $message"
    ERRORS+=("$check_name: $message")
    VALIDATION_RESULTS+=("FAIL: $check_name - $message")
}

section_header() {
    echo ""
    echo -e "${CYAN}═══ $1 ═══${NC}"
}

# Pre-flight checks
main() {
    echo -e "${BLUE}╔════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║     ChromoForge Docker Cleanup Pre-flight Validation       ║${NC}"
    echo -e "${BLUE}╚════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    echo "Starting validation at $(date '+%Y-%m-%d %H:%M:%S')"
    
    # 1. System Requirements
    section_header "SYSTEM REQUIREMENTS"
    
    # Check Docker
    if command -v docker &> /dev/null; then
        local docker_version=$(docker --version | awk '{print $3}' | sed 's/,//')
        check_pass "Docker" "Installed (version $docker_version)"
    else
        check_fail "Docker" "Not installed or not in PATH"
    fi
    
    # Check Docker daemon
    if docker info &> /dev/null; then
        check_pass "Docker Daemon" "Running and accessible"
    else
        check_fail "Docker Daemon" "Not running or not accessible"
    fi
    
    # Check disk space
    local available_space=$(df -BG / | awk 'NR==2 {print $4}' | sed 's/G//')
    if [ "$available_space" -ge "$MIN_DISK_SPACE_GB" ]; then
        check_pass "Disk Space" "${available_space}GB available (minimum ${MIN_DISK_SPACE_GB}GB)"
    else
        check_fail "Disk Space" "Only ${available_space}GB available (need ${MIN_DISK_SPACE_GB}GB)"
    fi
    
    # Check permissions
    if [ -w "." ]; then
        check_pass "Permissions" "Write access to current directory"
    else
        check_fail "Permissions" "No write access to current directory"
    fi
    
    # 2. Critical Service Status
    section_header "CRITICAL SERVICE STATUS"
    
    # Check critical container
    if docker ps | grep -q "$CRITICAL_CONTAINER"; then
        local container_id=$(docker ps -q -f name="$CRITICAL_CONTAINER")
        local uptime=$(docker ps --format "{{.Status}}" -f name="$CRITICAL_CONTAINER")
        check_pass "Critical Container" "$CRITICAL_CONTAINER is running ($uptime)"
        
        # Check container health
        if docker exec "$CRITICAL_CONTAINER" echo "test" &> /dev/null; then
            check_pass "Container Health" "Container responds to commands"
        else
            check_warn "Container Health" "Container may not be fully healthy"
        fi
    else
        check_fail "Critical Container" "$CRITICAL_CONTAINER is NOT running"
    fi
    
    # Check critical image
    if docker images | grep -q "$CRITICAL_IMAGE"; then
        local image_id=$(docker images -q "$CRITICAL_IMAGE" | head -1)
        check_pass "Critical Image" "$CRITICAL_IMAGE exists (ID: ${image_id:0:12})"
    else
        check_fail "Critical Image" "$CRITICAL_IMAGE not found"
    fi
    
    # Check critical network
    if docker network ls | grep -q "$CRITICAL_NETWORK"; then
        check_pass "Critical Network" "$CRITICAL_NETWORK exists"
    else
        check_warn "Critical Network" "$CRITICAL_NETWORK not found (will be recreated)"
    fi
    
    # 3. Resource Analysis
    section_header "RESOURCE ANALYSIS"
    
    # Count resources
    local running_containers=$(docker ps -q | wc -l)
    local stopped_containers=$(docker ps -aq -f status=exited | wc -l)
    local total_images=$(docker images -q | wc -l)
    local dangling_images=$(docker images -f "dangling=true" -q | wc -l)
    local volumes=$(docker volume ls -q | wc -l)
    local networks=$(docker network ls -q | wc -l)
    
    echo "Running Containers: $running_containers"
    echo "Stopped Containers: $stopped_containers"
    echo "Total Images: $total_images"
    echo "Dangling Images: $dangling_images"
    echo "Volumes: $volumes"
    echo "Networks: $networks"
    
    # Check for reclaimable space
    local reclaimable=$(docker system df | grep "RECLAIMABLE" -A 3 | tail -3 | awk '{print $4}' | paste -sd+ | bc 2>/dev/null || echo "0")
    if [ -n "$reclaimable" ] && [ "$reclaimable" != "0" ]; then
        check_pass "Reclaimable Space" "Space available for cleanup"
    else
        check_warn "Reclaimable Space" "Unable to calculate reclaimable space"
    fi
    
    # 4. Backup Readiness
    section_header "BACKUP READINESS"
    
    # Check backup directory
    if mkdir -p backup/test 2>/dev/null && rmdir backup/test 2>/dev/null; then
        check_pass "Backup Directory" "Can create backup directory"
    else
        check_fail "Backup Directory" "Cannot create backup directory"
    fi
    
    # Check for existing backups
    if [ -d "backup" ] && [ "$(ls -A backup 2>/dev/null)" ]; then
        local backup_count=$(ls -1 backup | wc -l)
        check_warn "Existing Backups" "$backup_count existing backup(s) found"
    else
        check_pass "Existing Backups" "No conflicting backups"
    fi
    
    # 5. Script Dependencies
    section_header "SCRIPT DEPENDENCIES"
    
    # Check for required commands
    for cmd in bc grep awk sed tee date; do
        if command -v $cmd &> /dev/null; then
            check_pass "Command: $cmd" "Available"
        else
            check_fail "Command: $cmd" "Not found"
        fi
    done
    
    # Check for executor script
    if [ -f "docker-cleanup-executor.sh" ]; then
        if [ -x "docker-cleanup-executor.sh" ]; then
            check_pass "Executor Script" "Found and executable"
        else
            check_warn "Executor Script" "Found but not executable (run: chmod +x docker-cleanup-executor.sh)"
        fi
    else
        check_fail "Executor Script" "docker-cleanup-executor.sh not found"
    fi
    
    # 6. Safety Checks
    section_header "SAFETY CHECKS"
    
    # Check for running cleanup
    if pgrep -f "docker-cleanup-executor.sh" > /dev/null; then
        check_fail "Cleanup Status" "Another cleanup is already running"
    else
        check_pass "Cleanup Status" "No cleanup currently running"
    fi
    
    # Check Docker API responsiveness
    if timeout 5 docker version &> /dev/null; then
        check_pass "Docker API" "Responsive (< 5 seconds)"
    else
        check_fail "Docker API" "Not responsive or too slow"
    fi
    
    # 7. Protected Resources
    section_header "PROTECTED RESOURCES VERIFICATION"
    
    echo -e "${YELLOW}The following resources will be protected:${NC}"
    echo "  • Container: $CRITICAL_CONTAINER"
    echo "  • Image: $CRITICAL_IMAGE"
    echo "  • Network: $CRITICAL_NETWORK"
    
    # 8. Estimated Impact
    section_header "ESTIMATED CLEANUP IMPACT"
    
    # Parse docker system df output
    local df_output=$(docker system df)
    
    echo "Estimated Space Recovery:"
    echo "$df_output" | grep -E "Images|Containers|Build Cache" | while read line; do
        echo "  $line"
    done
    
    local total_reclaimable=$(echo "$df_output" | grep "RECLAIMABLE" -A 3 | tail -3 | awk '{print $4}' | sed 's/GB//' | paste -sd+ | bc 2>/dev/null || echo "0")
    echo ""
    echo -e "${BLUE}Total Reclaimable: ~${total_reclaimable}GB${NC}"
    
    # Summary
    echo ""
    echo -e "${CYAN}════════════════════════════════════════${NC}"
    echo -e "${CYAN}           VALIDATION SUMMARY            ${NC}"
    echo -e "${CYAN}════════════════════════════════════════${NC}"
    
    local total_checks=${#VALIDATION_RESULTS[@]}
    local error_count=${#ERRORS[@]}
    local warning_count=${#WARNINGS[@]}
    local pass_count=$((total_checks - error_count - warning_count))
    
    echo "Total Checks: $total_checks"
    echo -e "${GREEN}Passed: $pass_count${NC}"
    echo -e "${YELLOW}Warnings: $warning_count${NC}"
    echo -e "${RED}Errors: $error_count${NC}"
    
    # Show warnings if any
    if [ ${#WARNINGS[@]} -gt 0 ]; then
        echo ""
        echo -e "${YELLOW}Warnings:${NC}"
        for warning in "${WARNINGS[@]}"; do
            echo "  • $warning"
        done
    fi
    
    # Show errors if any
    if [ ${#ERRORS[@]} -gt 0 ]; then
        echo ""
        echo -e "${RED}Errors that must be resolved:${NC}"
        for error in "${ERRORS[@]}"; do
            echo "  • $error"
        done
    fi
    
    # Final decision
    echo ""
    if [ $error_count -eq 0 ]; then
        echo -e "${GREEN}╔════════════════════════════════════════╗${NC}"
        echo -e "${GREEN}║    ✓ SYSTEM READY FOR CLEANUP         ║${NC}"
        echo -e "${GREEN}╚════════════════════════════════════════╝${NC}"
        
        if [ $warning_count -gt 0 ]; then
            echo ""
            echo -e "${YELLOW}Note: There are $warning_count warning(s) to review.${NC}"
        fi
        
        echo ""
        echo "Next steps:"
        echo "1. Review any warnings above"
        echo "2. Start monitoring: ./docker-cleanup-monitor.sh"
        echo "3. Run cleanup: ./docker-cleanup-executor.sh"
        
        exit 0
    else
        echo -e "${RED}╔════════════════════════════════════════╗${NC}"
        echo -e "${RED}║    ✗ SYSTEM NOT READY FOR CLEANUP     ║${NC}"
        echo -e "${RED}╚════════════════════════════════════════╝${NC}"
        
        echo ""
        echo "Please resolve the $error_count error(s) before proceeding."
        
        exit 1
    fi
}

# Run validation
main "$@"