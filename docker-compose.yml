services:
  chromoforge:
    build:
      context: .
      dockerfile: Dockerfile
      tags:
        - "chromoforge:${CHROMOFORGE_VERSION:-1.0.0}"
        - "chromoforge:latest"
    image: "chromoforge:${CHROMOFORGE_VERSION:-1.0.0}"
    container_name: chromoforge-app
    environment:
      # Hot reload settings
      - WATCHPACK_POLLING=true
      - CHOKIDAR_USEPOLLING=true
      # Load environment variables from .env file
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - MAX_RETRIES=${MAX_RETRIES:-3}
      - RETRY_DELAY=${RETRY_DELAY:-1.0}
      - BATCH_SIZE=${BATCH_SIZE:-5}
      - MAX_CONCURRENT_REQUESTS=${MAX_CONCURRENT_REQUESTS:-10}
      - MAX_FILE_SIZE_MB=${MAX_FILE_SIZE_MB:-50}
      - SUPPORTED_FORMATS=${SUPPORTED_FORMATS:-pdf}
      - OUTPUT_DIR=${OUTPUT_DIR:-./processed}
      - TEMP_DIR=${TEMP_DIR:-./temp}
      - CONFIDENCE_THRESHOLD=${CONFIDENCE_THRESHOLD:-0.7}
      - ENABLE_COORDINATE_TRACKING=${ENABLE_COORDINATE_TRACKING:-true}
      - OBFUSCATION_METHOD=${OBFUSCATION_METHOD:-black_box}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FORMAT=${LOG_FORMAT:-json}
      - GEMINI_MODEL=${GEMINI_MODEL:-gemini-2.5-pro}
      - GEMINI_TEMPERATURE=${GEMINI_TEMPERATURE:-0.1}
      - GEMINI_MAX_TOKENS=${GEMINI_MAX_TOKENS:-8192}
      - GEMINI_THINKING_BUDGET=${GEMINI_THINKING_BUDGET:--1}
      - ENABLE_ULTRA_THINK=${ENABLE_ULTRA_THINK:-true}
      - ENABLE_GOOGLE_SEARCH=${ENABLE_GOOGLE_SEARCH:-true}
      - ENABLE_URL_CONTEXT=${ENABLE_URL_CONTEXT:-true}
      - ENABLE_DATABASE_RECORDING=${ENABLE_DATABASE_RECORDING:-true}
      - DEFAULT_ORGANIZATION_ID=${DEFAULT_ORGANIZATION_ID:-}
      - THAI_CROSS_REFERENCE=${THAI_CROSS_REFERENCE:-true}
      - CONTEXTUAL_NAME_MAPPING=${CONTEXTUAL_NAME_MAPPING:-true}
      - MEDICAL_FIELD_EXTRACTION=${MEDICAL_FIELD_EXTRACTION:-true}
    volumes:
      # Mount source code for development (hot reload)
      - ./src:/app/src:ro
      # Mount input and output directories
      - ./original-pdf-examples:/app/original-pdf-examples:ro
      - ./test-results:/app/test-results
      - ./batch-results:/app/batch-results
      - ./processed:/app/processed
      - ./temp:/app/temp
      # Mount environment file
      - ./.env:/app/.env:ro
    working_dir: /app
    stdin_open: true
    tty: true
    restart: unless-stopped
    networks:
      - chromoforge-network
    # Override default command for interactive use
    command: tail -f /dev/null

  # ChromoForge Dashboard Service
  chromoforge-dashboard:
    build:
      context: ./dashboard
      dockerfile: Dockerfile
      target: development
      tags:
        - "chromoforge-dashboard:${CHROMOFORGE_VERSION:-1.0.0}"
        - "chromoforge-dashboard:latest"
    image: "chromoforge-dashboard:${CHROMOFORGE_VERSION:-1.0.0}"
    container_name: chromoforge-dashboard
    environment:
      # Development environment
      - NODE_ENV=development
      - VITE_API_URL=http://chromoforge:8000
      # Hot reload settings
      - WATCHPACK_POLLING=true
      - CHOKIDAR_USEPOLLING=true
      - CHOKIDAR_INTERVAL=1000
      # Supabase integration (shared with main service)
      - VITE_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - VITE_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      # Dashboard specific settings
      - VITE_APP_NAME=ChromoForge Dashboard
      - VITE_APP_VERSION=${CHROMOFORGE_VERSION:-1.0.0}
      - VITE_ENABLE_THAI_SUPPORT=true
      - VITE_ENABLE_BUDDHIST_ERA=true
    ports:
      - "3001:3001"
    volumes:
      # Mount source code for hot reload development
      - ./dashboard/src:/app/src:ro
      - ./dashboard/public:/app/public:ro
      - ./dashboard/index.html:/app/index.html:ro
      - ./dashboard/tailwind.config.js:/app/tailwind.config.js:ro
      - ./dashboard/vite.config.ts:/app/vite.config.ts:ro
      - ./dashboard/tsconfig.json:/app/tsconfig.json:ro
      # Node modules volume for performance
      - chromoforge-dashboard-node-modules:/app/node_modules
    networks:
      - chromoforge-network
    depends_on:
      - chromoforge
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a service for running specific commands
  chromoforge-runner:
    extends: chromoforge
    container_name: chromoforge-runner
    profiles: ["runner"]
    command: python -m src.main --help

# Networks for service communication
networks:
  chromoforge-network:
    driver: bridge
    name: chromoforge-network

# Named volumes for persistent data
volumes:
  # Dashboard node_modules for better performance
  chromoforge-dashboard-node-modules:
    name: chromoforge-dashboard-node-modules
