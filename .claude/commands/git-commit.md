---
allowed-tools: <PERSON><PERSON>(git add:*), <PERSON><PERSON>(git status:*), <PERSON><PERSON>(git commit:*)
description: Create a git commit
---

## Context

- Current git status: !`git status`
- Current git diff (staged and unstaged changes): !`git diff HEAD`
- Current branch: !`git branch --show-current`
- Recent commits: !`git log --oneline -10`

## Your task

Based on the above changes, create a single git commit.  
Follow the formatting guidelines in `GIT_BEST_PRACTICES.md`