# ChromoForge Admin Dashboard - Database Schema Extensions

## Overview

This document describes the database schema extensions required for the ChromoForge Admin Dashboard. These extensions build upon the existing ChromoForge OCR pipeline schema to add dashboard-specific functionality including analytics, user preferences, notifications, and reporting capabilities.

## New Tables and Extensions

### 1. Dashboard Analytics Tables

#### dashboard_metrics
Stores aggregated metrics for dashboard analytics displays.

```sql
CREATE TABLE dashboard_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    metric_date DATE NOT NULL,
    metric_type TEXT NOT NULL, -- 'daily', 'weekly', 'monthly'
    
    -- Document metrics
    total_documents INTEGER DEFAULT 0,
    processed_documents INTEGER DEFAULT 0,
    failed_documents INTEGER DEFAULT 0,
    pending_documents INTEGER DEFAULT 0,
    
    -- OCR metrics
    total_pages_processed INTEGER DEFAULT 0,
    average_processing_time_ms DECIMAL(10,2),
    average_confidence_score DECIMAL(3,2),
    
    -- PII metrics
    total_pii_detected INTEGER DEFAULT 0,
    pii_by_type J<PERSON>NB DEFAULT '{}', -- {"thai_id": 150, "phone": 230, etc}
    
    -- Performance metrics
    api_calls_count INTEGER DEFAULT 0,
    api_cost_usd DECIMAL(10,4),
    storage_used_mb DECIMAL(10,2),
    
    -- User activity
    active_users_count INTEGER DEFAULT 0,
    login_count INTEGER DEFAULT 0,
    
    -- Calculated fields
    success_rate DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN processed_documents + failed_documents > 0 
            THEN (processed_documents::DECIMAL / (processed_documents + failed_documents) * 100)
            ELSE 0 
        END
    ) STORED,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT unique_org_date_type UNIQUE(organization_id, metric_date, metric_type),
    CONSTRAINT valid_metric_type CHECK (metric_type IN ('daily', 'weekly', 'monthly'))
);

CREATE INDEX idx_dashboard_metrics_org_date ON dashboard_metrics(organization_id, metric_date DESC);
CREATE INDEX idx_dashboard_metrics_type ON dashboard_metrics(metric_type);
```

#### real_time_stats
Stores real-time statistics for live dashboard updates.

```sql
CREATE TABLE real_time_stats (
    organization_id UUID PRIMARY KEY REFERENCES organizations(id),
    
    -- Live counters
    documents_processing INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    queue_size INTEGER DEFAULT 0,
    
    -- Rate metrics (last 5 minutes)
    processing_rate_per_min DECIMAL(10,2) DEFAULT 0,
    error_rate_per_min DECIMAL(10,2) DEFAULT 0,
    
    -- System health
    api_health_score DECIMAL(3,2) DEFAULT 1.0, -- 0-1 scale
    storage_health_score DECIMAL(3,2) DEFAULT 1.0,
    database_health_score DECIMAL(3,2) DEFAULT 1.0,
    
    -- Last update
    last_heartbeat TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 2. User Experience Tables

#### user_preferences
Stores user-specific dashboard preferences and settings.

```sql
CREATE TABLE user_preferences (
    user_id UUID PRIMARY KEY REFERENCES user_profiles(id),
    
    -- UI preferences
    theme TEXT DEFAULT 'light' CHECK (theme IN ('light', 'dark', 'system')),
    language TEXT DEFAULT 'th' CHECK (language IN ('th', 'en')),
    timezone TEXT DEFAULT 'Asia/Bangkok',
    date_format TEXT DEFAULT 'DD/MM/YYYY',
    
    -- Dashboard layout
    dashboard_layout JSONB DEFAULT '{}', -- Widget positions and sizes
    default_view TEXT DEFAULT 'overview',
    collapsed_sidebar BOOLEAN DEFAULT false,
    
    -- Notification preferences
    email_notifications BOOLEAN DEFAULT true,
    push_notifications BOOLEAN DEFAULT false,
    notification_frequency TEXT DEFAULT 'immediate', -- 'immediate', 'hourly', 'daily'
    notification_types JSONB DEFAULT '["errors", "completion", "system"]',
    
    -- Table preferences
    default_page_size INTEGER DEFAULT 25,
    table_density TEXT DEFAULT 'comfortable', -- 'compact', 'comfortable', 'spacious'
    show_thumbnails BOOLEAN DEFAULT true,
    
    -- Advanced settings
    auto_refresh_interval INTEGER DEFAULT 30, -- seconds, 0 = disabled
    enable_sounds BOOLEAN DEFAULT false,
    keyboard_shortcuts BOOLEAN DEFAULT true,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### saved_filters
Stores user-saved filter configurations for quick access.

```sql
CREATE TABLE saved_filters (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    
    name TEXT NOT NULL,
    description TEXT,
    filter_type TEXT NOT NULL, -- 'documents', 'extractions', 'users', 'analytics'
    
    -- Filter configuration
    filter_config JSONB NOT NULL, -- Complex filter object
    is_default BOOLEAN DEFAULT false,
    is_shared BOOLEAN DEFAULT false, -- Shared with organization
    
    -- Usage tracking
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT unique_default_filter UNIQUE(user_id, filter_type, is_default) WHERE is_default = true
);

CREATE INDEX idx_saved_filters_user ON saved_filters(user_id);
CREATE INDEX idx_saved_filters_org ON saved_filters(organization_id) WHERE is_shared = true;
```

### 3. Notification System Tables

#### notifications
Stores system and user notifications.

```sql
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    user_id UUID REFERENCES user_profiles(id), -- NULL for system-wide
    
    -- Notification details
    type TEXT NOT NULL, -- 'info', 'warning', 'error', 'success'
    category TEXT NOT NULL, -- 'system', 'document', 'user', 'billing'
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    
    -- Additional data
    action_url TEXT, -- Link to relevant page
    action_label TEXT, -- Button text
    metadata JSONB DEFAULT '{}',
    
    -- Status
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMPTZ,
    is_archived BOOLEAN DEFAULT false,
    archived_at TIMESTAMPTZ,
    
    -- Delivery
    channels TEXT[] DEFAULT ARRAY['in_app'], -- 'in_app', 'email', 'push'
    email_sent BOOLEAN DEFAULT false,
    email_sent_at TIMESTAMPTZ,
    
    -- Priority and expiry
    priority INTEGER DEFAULT 3 CHECK (priority BETWEEN 1 AND 5),
    expires_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_notifications_user_unread ON notifications(user_id, created_at DESC) 
    WHERE is_read = false AND is_archived = false;
CREATE INDEX idx_notifications_org ON notifications(organization_id, created_at DESC);
CREATE INDEX idx_notifications_expires ON notifications(expires_at) WHERE expires_at IS NOT NULL;
```

#### notification_queue
Queue for processing outbound notifications.

```sql
CREATE TABLE notification_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_id UUID NOT NULL REFERENCES notifications(id),
    channel TEXT NOT NULL, -- 'email', 'push', 'webhook'
    recipient TEXT NOT NULL, -- email address, device token, webhook URL
    
    -- Queue status
    status TEXT DEFAULT 'pending', -- 'pending', 'processing', 'sent', 'failed'
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    
    -- Scheduling
    scheduled_for TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    
    -- Error tracking
    last_error TEXT,
    error_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_notification_queue_pending ON notification_queue(scheduled_for) 
    WHERE status = 'pending' AND attempts < max_attempts;
```

### 4. Reporting Tables

#### report_templates
Stores customizable report templates.

```sql
CREATE TABLE report_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    created_by UUID NOT NULL REFERENCES user_profiles(id),
    
    -- Template details
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL, -- 'operational', 'compliance', 'analytics'
    
    -- Report configuration
    report_type TEXT NOT NULL, -- 'summary', 'detailed', 'audit'
    sections JSONB NOT NULL, -- Array of section configurations
    filters JSONB DEFAULT '{}',
    
    -- Scheduling
    is_scheduled BOOLEAN DEFAULT false,
    schedule_config JSONB, -- Cron expression and recipients
    
    -- Access control
    is_public BOOLEAN DEFAULT false, -- Available to all org users
    allowed_roles TEXT[] DEFAULT ARRAY['admin', 'analyst'],
    
    -- Template status
    is_active BOOLEAN DEFAULT true,
    last_generated_at TIMESTAMPTZ,
    generation_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_report_templates_org ON report_templates(organization_id);
CREATE INDEX idx_report_templates_scheduled ON report_templates(is_scheduled) WHERE is_scheduled = true;
```

#### generated_reports
Stores generated report instances.

```sql
CREATE TABLE generated_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    template_id UUID REFERENCES report_templates(id),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    generated_by UUID NOT NULL REFERENCES user_profiles(id),
    
    -- Report details
    title TEXT NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    
    -- Generation details
    generation_time_ms INTEGER,
    data_points_count INTEGER,
    file_size_bytes INTEGER,
    
    -- Storage
    file_path TEXT, -- Supabase storage path
    file_format TEXT DEFAULT 'pdf', -- 'pdf', 'xlsx', 'csv'
    
    -- Access tracking
    view_count INTEGER DEFAULT 0,
    download_count INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMPTZ,
    
    -- Retention
    expires_at TIMESTAMPTZ,
    is_archived BOOLEAN DEFAULT false,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_generated_reports_org_date ON generated_reports(organization_id, created_at DESC);
CREATE INDEX idx_generated_reports_template ON generated_reports(template_id);
```

### 5. Activity and Session Tables

#### user_sessions
Enhanced session tracking for analytics and security.

```sql
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    
    -- Session details
    session_token TEXT UNIQUE NOT NULL,
    refresh_token TEXT,
    
    -- Device information
    ip_address INET,
    user_agent TEXT,
    device_type TEXT, -- 'desktop', 'mobile', 'tablet'
    browser TEXT,
    os TEXT,
    
    -- Location (approximate)
    country_code TEXT,
    region TEXT,
    city TEXT,
    
    -- Session lifecycle
    started_at TIMESTAMPTZ DEFAULT NOW(),
    last_activity_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    ended_at TIMESTAMPTZ,
    end_reason TEXT, -- 'logout', 'timeout', 'forced', 'expired'
    
    -- Activity summary
    page_views_count INTEGER DEFAULT 0,
    api_calls_count INTEGER DEFAULT 0,
    documents_processed INTEGER DEFAULT 0
);

CREATE INDEX idx_user_sessions_user ON user_sessions(user_id, started_at DESC);
CREATE INDEX idx_user_sessions_active ON user_sessions(expires_at) WHERE ended_at IS NULL;
```

#### activity_log
Detailed user activity tracking for audit and analytics.

```sql
CREATE TABLE activity_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES user_sessions(id),
    user_id UUID NOT NULL REFERENCES user_profiles(id),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    
    -- Activity details
    action TEXT NOT NULL, -- 'view', 'create', 'update', 'delete', 'export', 'share'
    resource_type TEXT NOT NULL, -- 'document', 'report', 'user', 'settings'
    resource_id UUID,
    resource_name TEXT,
    
    -- Context
    page_path TEXT,
    referrer_path TEXT,
    
    -- Performance
    response_time_ms INTEGER,
    
    -- Additional data
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_activity_log_user_time ON activity_log(user_id, created_at DESC);
CREATE INDEX idx_activity_log_resource ON activity_log(resource_type, resource_id);
CREATE INDEX idx_activity_log_session ON activity_log(session_id);

-- Partition by month for better performance
-- Note: Requires PostgreSQL 11+
-- Implementation depends on specific PostgreSQL version and Supabase support
```

### 6. Search and Discovery Tables

#### search_history
Stores user search queries for analytics and quick access.

```sql
CREATE TABLE search_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    
    -- Search details
    query TEXT NOT NULL,
    search_type TEXT NOT NULL, -- 'documents', 'extractions', 'global'
    filters JSONB DEFAULT '{}',
    
    -- Results
    results_count INTEGER DEFAULT 0,
    clicked_results JSONB DEFAULT '[]', -- Array of clicked result IDs
    
    -- Performance
    search_time_ms INTEGER,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_search_history_user ON search_history(user_id, created_at DESC);
CREATE INDEX idx_search_history_query ON search_history USING gin(to_tsvector('simple', query));
```

### 7. Dashboard Widget Tables

#### dashboard_widgets
Stores available dashboard widgets and their configurations.

```sql
CREATE TABLE dashboard_widgets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Widget details
    widget_type TEXT UNIQUE NOT NULL, -- 'stats_card', 'chart', 'table', 'activity_feed'
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL, -- 'analytics', 'monitoring', 'reports', 'activity'
    
    -- Configuration schema
    config_schema JSONB NOT NULL, -- JSON Schema for widget configuration
    default_config JSONB NOT NULL,
    
    -- Size constraints
    min_width INTEGER DEFAULT 1,
    min_height INTEGER DEFAULT 1,
    max_width INTEGER DEFAULT 12,
    max_height INTEGER DEFAULT 8,
    
    -- Permissions
    required_roles TEXT[] DEFAULT ARRAY['viewer'],
    
    -- Widget metadata
    is_active BOOLEAN DEFAULT true,
    version TEXT DEFAULT '1.0.0',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### user_dashboards
Stores user-customized dashboard layouts.

```sql
CREATE TABLE user_dashboards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id),
    
    -- Dashboard details
    name TEXT NOT NULL,
    is_default BOOLEAN DEFAULT false,
    
    -- Layout configuration
    layout JSONB NOT NULL, -- Array of widget placements
    
    -- Sharing
    is_shared BOOLEAN DEFAULT false,
    shared_with_roles TEXT[],
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT unique_user_default UNIQUE(user_id, is_default) WHERE is_default = true
);

CREATE INDEX idx_user_dashboards_user ON user_dashboards(user_id);
```

## Updated RLS Policies

### Dashboard Metrics
```sql
-- Users can view metrics for their organization
CREATE POLICY "View organization metrics"
    ON dashboard_metrics FOR SELECT
    USING (organization_id = get_user_organization(auth.uid()));

-- Only system can insert/update metrics
CREATE POLICY "System manages metrics"
    ON dashboard_metrics FOR ALL
    USING (auth.uid() = get_system_user_id());
```

### User Preferences
```sql
-- Users can manage their own preferences
CREATE POLICY "Manage own preferences"
    ON user_preferences FOR ALL
    USING (user_id = auth.uid());
```

### Notifications
```sql
-- Users can view their notifications
CREATE POLICY "View own notifications"
    ON notifications FOR SELECT
    USING (
        user_id = auth.uid() 
        OR (user_id IS NULL AND organization_id = get_user_organization(auth.uid()))
    );

-- Users can update their notification status
CREATE POLICY "Update own notification status"
    ON notifications FOR UPDATE
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());
```

### Activity Logging
```sql
-- Users can view activity in their organization (based on role)
CREATE POLICY "View organization activity"
    ON activity_log FOR SELECT
    USING (
        organization_id = get_user_organization(auth.uid())
        AND (
            user_id = auth.uid() -- Own activity
            OR has_role(auth.uid(), 'admin') -- Admins see all
            OR has_role(auth.uid(), 'analyst') -- Analysts see all
        )
    );
```

## Database Functions

### Analytics Aggregation
```sql
CREATE OR REPLACE FUNCTION calculate_daily_metrics(p_date DATE DEFAULT CURRENT_DATE)
RETURNS VOID AS $$
BEGIN
    INSERT INTO dashboard_metrics (
        organization_id,
        metric_date,
        metric_type,
        total_documents,
        processed_documents,
        failed_documents,
        pending_documents,
        total_pages_processed,
        average_processing_time_ms,
        average_confidence_score,
        total_pii_detected,
        pii_by_type,
        api_calls_count,
        api_cost_usd,
        active_users_count,
        login_count
    )
    SELECT 
        o.id,
        p_date,
        'daily',
        COUNT(DISTINCT d.id),
        COUNT(DISTINCT CASE WHEN d.status = 'completed' THEN d.id END),
        COUNT(DISTINCT CASE WHEN d.status = 'failed' THEN d.id END),
        COUNT(DISTINCT CASE WHEN d.status IN ('pending', 'processing') THEN d.id END),
        SUM(d.page_count),
        AVG(t.processing_duration_ms),
        AVG(e.overall_confidence_score),
        COUNT(DISTINCT e.id),
        '{}'::jsonb, -- TODO: Aggregate PII types
        COUNT(t.id),
        SUM(t.api_cost_usd),
        COUNT(DISTINCT s.user_id),
        COUNT(DISTINCT s.id)
    FROM organizations o
    LEFT JOIN documents d ON o.id = d.organization_id 
        AND DATE(d.created_at) = p_date
    LEFT JOIN ocr_processing_transactions t ON d.id = t.document_id
    LEFT JOIN medical_records_extraction e ON d.id = e.document_id 
        AND e.is_current = true
    LEFT JOIN user_sessions s ON o.id = s.organization_id 
        AND DATE(s.started_at) = p_date
    GROUP BY o.id
    ON CONFLICT (organization_id, metric_date, metric_type) 
    DO UPDATE SET
        total_documents = EXCLUDED.total_documents,
        processed_documents = EXCLUDED.processed_documents,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Real-time Stats Update
```sql
CREATE OR REPLACE FUNCTION update_real_time_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Update real-time statistics based on document status changes
    IF TG_TABLE_NAME = 'documents' THEN
        UPDATE real_time_stats
        SET 
            documents_processing = (
                SELECT COUNT(*) FROM documents 
                WHERE organization_id = NEW.organization_id 
                AND status = 'processing'
            ),
            queue_size = (
                SELECT COUNT(*) FROM documents 
                WHERE organization_id = NEW.organization_id 
                AND status = 'pending'
            ),
            updated_at = NOW()
        WHERE organization_id = NEW.organization_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_stats_on_document_change
    AFTER INSERT OR UPDATE ON documents
    FOR EACH ROW
    EXECUTE FUNCTION update_real_time_stats();
```

## Migration Strategy

### Phase 1: Core Tables
1. Create analytics tables (dashboard_metrics, real_time_stats)
2. Create user experience tables (user_preferences, saved_filters)
3. Implement basic RLS policies

### Phase 2: Advanced Features
1. Create notification system tables
2. Create reporting tables
3. Implement activity tracking

### Phase 3: Optimization
1. Add partitioning for large tables
2. Create materialized views for complex queries
3. Implement advanced search capabilities

## Performance Considerations

### Indexes Strategy
- Cover all foreign keys
- Support common query patterns
- Enable full-text search for Thai language
- Use partial indexes where appropriate

### Partitioning
- Activity_log by month
- Notifications by created_at
- Dashboard_metrics by metric_date

### Materialized Views
```sql
-- Example: Organization summary view
CREATE MATERIALIZED VIEW organization_summary AS
SELECT 
    o.id,
    o.name,
    COUNT(DISTINCT d.id) as total_documents,
    COUNT(DISTINCT u.id) as total_users,
    MAX(d.created_at) as last_document_date,
    SUM(t.api_cost_usd) as total_cost
FROM organizations o
LEFT JOIN documents d ON o.id = d.organization_id
LEFT JOIN user_profiles u ON o.id = u.organization_id
LEFT JOIN ocr_processing_transactions t ON o.id = t.organization_id
GROUP BY o.id, o.name;

CREATE UNIQUE INDEX ON organization_summary(id);
REFRESH MATERIALIZED VIEW CONCURRENTLY organization_summary;
```