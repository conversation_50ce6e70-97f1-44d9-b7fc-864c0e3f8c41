# ChromoForge Admin Dashboard - System Architecture

## Executive Summary

The ChromoForge Admin Dashboard is a comprehensive web application designed to manage medical document OCR processing, monitor system health, and provide analytics for Thai medical document digitization. Built on a modern React/Next.js frontend with Supabase backend, it ensures medical-grade security, HIPAA compliance, and seamless integration with the existing ChromoForge OCR pipeline.

## Architecture Overview

```mermaid
graph TB
    subgraph "Client Layer"
        UI[React/Next.js UI]
        PWA[PWA Service Worker]
        Cache[Local Storage/IndexedDB]
    end
    
    subgraph "API Gateway"
        NextAPI[Next.js API Routes]
        Auth[Supabase Auth]
        RLS[Row Level Security]
    end
    
    subgraph "Backend Services"
        SB[Supabase PostgreSQL]
        RT[Realtime Subscriptions]
        EF[Edge Functions]
        Storage[Supabase Storage]
    end
    
    subgraph "External Services"
        OCR[ChromoForge OCR Pipeline]
        Gemini[Google Gemini API]
        Email[Email Service]
    end
    
    UI --> NextAPI
    UI --> Auth
    NextAPI --> RLS
    RLS --> SB
    UI --> RT
    RT --> SB
    NextAPI --> EF
    EF --> OCR
    OCR --> Gemini
    EF --> Email
    UI --> Cache
    PWA --> <PERSON><PERSON>
```

## Technical Stack

### Frontend Architecture

**Core Framework**
- **Next.js 14.2.x** with App Router for server-side rendering and optimal performance
- **React 18.3.x** with concurrent features for responsive UI
- **TypeScript 5.5.x** for type safety and developer experience

**UI Framework**
- **ChromoForge Design System**: Custom component library built on Tailwind CSS
- **Tailwind CSS 3.4.x** with custom medical theme configuration
- **Radix UI** for accessible, unstyled components
- **Framer Motion** for smooth animations and transitions

**State Management**
- **Zustand 4.x** for global application state
- **React Query (TanStack Query) 5.x** for server state management
- **React Hook Form 7.x** with Zod validation for forms

**Real-time Features**
- **Supabase Realtime Client** for live updates
- **Socket.io fallback** for legacy browser support
- **Optimistic UI updates** with rollback capability

**Development Tools**
- **Vite** for fast HMR in development
- **Vitest** for unit testing
- **Playwright** for E2E testing
- **Storybook** for component documentation

### Backend Architecture

**Primary Backend Service**
- **Supabase** (PostgreSQL 15) as the main database
- **PostgREST** for instant RESTful APIs
- **Supabase Auth** for authentication
- **Supabase Realtime** for WebSocket connections

**Database Features**
- **pgcrypto** for PII encryption
- **pg_trgm** for Thai language full-text search
- **pgvector** for AI embeddings (future)
- **TimescaleDB** extension for time-series analytics

**Edge Functions (Deno)**
- OCR pipeline orchestration
- Batch job management
- Email notifications
- Report generation
- Data export/import

**Storage**
- **Supabase Storage** for PDF documents
- **CDN integration** for static assets
- **Encryption at rest** for all files

### Security Architecture

**Authentication & Authorization**
- Supabase Auth with JWT tokens
- Multi-factor authentication (TOTP)
- OAuth providers (Google, Microsoft)
- Session management with refresh tokens

**Data Protection**
- End-to-end encryption for PII
- Organization-specific encryption keys
- Audit logging for all data access
- HIPAA-compliant data handling

**Network Security**
- HTTPS only with HSTS
- CSP headers for XSS protection
- Rate limiting on all endpoints
- DDoS protection via Cloudflare

## Deployment Architecture

### Production Environment

```yaml
Frontend:
  - Platform: Vercel Edge Network
  - Regions: Singapore (primary), Tokyo, Sydney
  - CDN: Vercel's global CDN
  - SSL: Automatic with Let's Encrypt

Backend:
  - Supabase: Singapore region (ap-southeast-1)
  - Database: 8 vCPU, 32GB RAM, 500GB SSD
  - Replicas: 2 read replicas for analytics
  - Backup: Daily automated backups, 30-day retention

Monitoring:
  - APM: Vercel Analytics + Custom Metrics
  - Error Tracking: Sentry
  - Logging: Supabase Logs + CloudWatch
  - Uptime: Pingdom + StatusPage
```

### Development Environment

```yaml
Local Development:
  - Docker Compose for services
  - Local Supabase instance
  - Hot Module Replacement
  - Mock data generators

Staging:
  - Vercel Preview Deployments
  - Supabase Preview Branches
  - Automated E2E testing
  - Performance profiling
```

## Scalability Considerations

### Horizontal Scaling
- **Frontend**: Automatic scaling via Vercel Edge
- **API**: Supabase handles up to 10K concurrent connections
- **Database**: Read replicas for analytics queries
- **Storage**: Unlimited with Supabase Storage

### Performance Targets
- **Page Load**: < 2s on 3G networks
- **API Response**: < 200ms p95
- **Real-time Updates**: < 100ms latency
- **Concurrent Users**: 1,000+ active sessions

### Caching Strategy
```typescript
// Multi-layer caching approach
const cacheStrategy = {
  browser: {
    staticAssets: '1 year',
    apiResponses: '5 minutes',
    userPreferences: 'session'
  },
  cdn: {
    images: '30 days',
    documents: '7 days',
    analytics: '1 hour'
  },
  database: {
    queryCache: '10 minutes',
    materializeViews: '1 hour',
    aggregations: '15 minutes'
  }
}
```

## Integration Architecture

### ChromoForge OCR Pipeline Integration

```typescript
interface OCRIntegration {
  // Direct database integration
  database: {
    tables: ['documents', 'medical_extractions', 'ocr_transactions'],
    triggers: ['on_document_upload', 'on_extraction_complete'],
    functions: ['process_document', 'get_extraction_status']
  },
  
  // Edge function orchestration
  edgeFunctions: {
    processDocument: '/functions/v1/process-document',
    batchProcess: '/functions/v1/batch-process',
    getStatus: '/functions/v1/get-status'
  },
  
  // Real-time updates
  subscriptions: {
    documentStatus: 'documents:status',
    extractionComplete: 'extractions:complete',
    batchProgress: 'batch:progress'
  }
}
```

### Third-Party Services

**Email Service (SendGrid)**
- Transactional emails for notifications
- Batch email for reports
- Template management
- Delivery tracking

**Analytics (Mixpanel)**
- User behavior tracking
- Feature usage analytics
- Conversion funnels
- Custom dashboards

**Monitoring (Datadog)**
- Infrastructure monitoring
- Custom metrics
- Log aggregation
- Alerting rules

## Data Flow Architecture

### Document Processing Flow

```mermaid
sequenceDiagram
    participant User
    participant Dashboard
    participant API
    participant Database
    participant OCR
    participant Storage
    
    User->>Dashboard: Upload PDF
    Dashboard->>Storage: Store PDF
    Storage-->>Dashboard: File URL
    Dashboard->>API: Create document record
    API->>Database: Insert document
    API->>OCR: Queue processing
    OCR->>Database: Update status
    Database-->>Dashboard: Real-time update
    OCR->>Database: Save extraction
    Database-->>Dashboard: Show results
```

### Real-time Data Synchronization

```typescript
// Real-time subscription architecture
const realtimeArchitecture = {
  channels: {
    'org:*': 'Organization-wide updates',
    'user:*': 'User-specific notifications',
    'document:*': 'Document status changes',
    'system:*': 'System-wide announcements'
  },
  
  events: {
    INSERT: 'New record created',
    UPDATE: 'Record modified',
    DELETE: 'Record removed',
    BROADCAST: 'Custom event'
  },
  
  optimization: {
    debounce: 100, // ms
    maxBatchSize: 50,
    compression: true
  }
}
```

## Disaster Recovery

### Backup Strategy
- **Database**: Daily automated backups with point-in-time recovery
- **Documents**: Cross-region replication to backup storage
- **Configuration**: Version controlled in Git
- **Encryption Keys**: Secure key management with rotation

### Recovery Targets
- **RTO (Recovery Time Objective)**: 4 hours
- **RPO (Recovery Point Objective)**: 1 hour
- **Failover**: Automatic with health checks
- **Data Integrity**: Checksums and validation

## Compliance & Standards

### Medical Data Compliance
- **HIPAA**: Full compliance with audit trails
- **PDPA (Thailand)**: Personal data protection
- **ISO 27001**: Information security management
- **SOC 2 Type II**: Security controls

### Development Standards
- **Code Quality**: ESLint, Prettier, TypeScript strict mode
- **Accessibility**: WCAG 2.1 AA compliance
- **Performance**: Core Web Vitals targets
- **Security**: OWASP Top 10 mitigation

## Future Architecture Considerations

### AI/ML Integration
- Vector embeddings for document similarity
- ML-based data quality scoring
- Automated anomaly detection
- Predictive analytics

### Microservices Migration Path
- Separate OCR processing service
- Analytics service extraction
- Notification service
- Report generation service

### Multi-Region Deployment
- Asia-Pacific primary region
- Europe secondary region
- Data residency compliance
- Global load balancing