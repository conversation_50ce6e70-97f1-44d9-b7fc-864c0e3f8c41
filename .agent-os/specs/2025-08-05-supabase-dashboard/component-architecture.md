# ChromoForge Admin Dashboard - Component Architecture

## Overview

This document defines the frontend component architecture for the ChromoForge Admin Dashboard, built with React 18, Next.js 14, and the ChromoForge Design System. The architecture emphasizes reusability, accessibility, and performance while maintaining consistency with Thai healthcare UX patterns.

## Component Hierarchy

```mermaid
graph TD
    App[App Layout]
    App --> Auth[Auth Provider]
    App --> Theme[Theme Provider]
    App --> I18n[i18n Provider]
    App --> Query[React Query Provider]
    
    Auth --> Router[Router]
    Router --> PublicLayout[Public Layout]
    Router --> DashboardLayout[Dashboard Layout]
    
    PublicLayout --> Login[Login Page]
    PublicLayout --> Register[Register Page]
    PublicLayout --> ForgotPassword[Forgot Password]
    
    DashboardLayout --> Sidebar[Sidebar Navigation]
    DashboardLayout --> Header[Header Bar]
    DashboardLayout --> MainContent[Main Content Area]
    
    MainContent --> Overview[Overview Dashboard]
    MainContent --> Documents[Documents Module]
    MainContent --> Analytics[Analytics Module]
    MainContent --> Users[Users Module]
    MainContent --> Settings[Settings Module]
```

## Core Layout Components

### App Shell

```typescript
// app/layout.tsx
interface AppShellProps {
  children: React.ReactNode;
}

export default function AppShell({ children }: AppShellProps) {
  return (
    <html lang="th" className={`${notoSansThai.variable} ${inter.variable}`}>
      <body>
        <Providers>
          <AuthGuard>
            <DashboardLayout>
              {children}
            </DashboardLayout>
          </AuthGuard>
        </Providers>
      </body>
    </html>
  );
}

// Providers component
function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider>
      <SupabaseProvider>
        <QueryClientProvider>
          <I18nProvider>
            <NotificationProvider>
              <TooltipProvider>
                {children}
              </TooltipProvider>
            </NotificationProvider>
          </I18nProvider>
        </QueryClientProvider>
      </SupabaseProvider>
    </ThemeProvider>
  );
}
```

### Dashboard Layout

```typescript
// components/layouts/DashboardLayout.tsx
interface DashboardLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const { isCollapsed } = useSidebar();
  
  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />
      <div className={cn(
        "flex-1 flex flex-col transition-all duration-300",
        isCollapsed ? "ml-16" : "ml-64"
      )}>
        <Header />
        <main className="flex-1 overflow-y-auto">
          <ErrorBoundary>
            <Suspense fallback={<PageLoader />}>
              {children}
            </Suspense>
          </ErrorBoundary>
        </main>
      </div>
      <NotificationPanel />
    </div>
  );
}
```

## Navigation Components

### Sidebar Navigation

```typescript
// components/navigation/Sidebar.tsx
interface SidebarProps {
  className?: string;
}

interface NavItem {
  id: string;
  label: string;
  icon: React.ComponentType;
  href: string;
  badge?: number;
  children?: NavItem[];
  permissions?: string[];
}

export function Sidebar({ className }: SidebarProps) {
  const { t } = useTranslation();
  const { user } = useAuth();
  const router = useRouter();
  const { isCollapsed, toggle } = useSidebar();
  
  const navItems: NavItem[] = [
    {
      id: 'overview',
      label: t('nav.overview'),
      icon: DashboardIcon,
      href: '/dashboard',
      permissions: ['view:dashboard']
    },
    {
      id: 'documents',
      label: t('nav.documents'),
      icon: DocumentIcon,
      href: '/documents',
      badge: 5,
      permissions: ['view:documents']
    },
    {
      id: 'analytics',
      label: t('nav.analytics'),
      icon: ChartIcon,
      href: '/analytics',
      children: [
        { id: 'reports', label: t('nav.reports'), href: '/analytics/reports' },
        { id: 'insights', label: t('nav.insights'), href: '/analytics/insights' }
      ],
      permissions: ['view:analytics']
    }
  ];
  
  return (
    <aside className={cn(
      "bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700",
      "transition-all duration-300",
      isCollapsed ? "w-16" : "w-64",
      className
    )}>
      <SidebarHeader onToggle={toggle} isCollapsed={isCollapsed} />
      <SidebarNav items={navItems} isCollapsed={isCollapsed} />
      <SidebarFooter isCollapsed={isCollapsed} />
    </aside>
  );
}
```

### Header Bar

```typescript
// components/navigation/Header.tsx
export function Header() {
  const { user } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const { language, setLanguage } = useI18n();
  
  return (
    <header className="h-16 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div className="h-full px-4 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <SearchCommand />
          <Breadcrumbs />
        </div>
        
        <div className="flex items-center space-x-4">
          <QuickActions />
          <NotificationBell />
          <LanguageSelector value={language} onChange={setLanguage} />
          <ThemeToggle value={theme} onChange={toggleTheme} />
          <UserMenu user={user} />
        </div>
      </div>
    </header>
  );
}
```

## Page Components

### Overview Dashboard

```typescript
// app/dashboard/page.tsx
export default function DashboardPage() {
  const { data: metrics } = useDashboardMetrics();
  const { data: recentDocuments } = useRecentDocuments();
  const { data: systemHealth } = useSystemHealth();
  
  return (
    <PageContainer>
      <PageHeader
        title="Dashboard Overview"
        description="Monitor your document processing and system health"
        actions={
          <Button variant="primary" leftIcon={<UploadIcon />}>
            Upload Document
          </Button>
        }
      />
      
      <div className="grid gap-6">
        {/* Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <MetricCard
            title="Total Documents"
            value={metrics?.totalDocuments || 0}
            change={12.5}
            trend="up"
            icon={<DocumentIcon />}
          />
          <MetricCard
            title="Processing Rate"
            value={`${metrics?.processingRate || 0}%`}
            change={-2.3}
            trend="down"
            icon={<ChartIcon />}
          />
          {/* More metric cards... */}
        </div>
        
        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader title="Processing Trends" />
            <CardContent>
              <ProcessingChart data={metrics?.trends} />
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader title="Document Types" />
            <CardContent>
              <DocumentTypeChart data={metrics?.documentTypes} />
            </CardContent>
          </Card>
        </div>
        
        {/* Recent Activity */}
        <Card>
          <CardHeader
            title="Recent Documents"
            action={
              <Link href="/documents" className="text-sm text-primary-600">
                View all
              </Link>
            }
          />
          <CardContent>
            <DocumentTable documents={recentDocuments} compact />
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
```

### Documents Module

```typescript
// app/documents/page.tsx
export default function DocumentsPage() {
  const [filters, setFilters] = useState<DocumentFilters>({
    status: 'all',
    dateRange: 'last30days',
    search: ''
  });
  
  const { data, isLoading } = useDocuments(filters);
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  
  return (
    <PageContainer>
      <PageHeader
        title="Documents"
        description="Manage and process medical documents"
        actions={
          <div className="flex space-x-3">
            <Button variant="outline" leftIcon={<FilterIcon />}>
              Filters
            </Button>
            <Button variant="primary" leftIcon={<UploadIcon />}>
              Upload
            </Button>
          </div>
        }
      />
      
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Filters Sidebar */}
        <aside className="lg:w-64">
          <DocumentFilters value={filters} onChange={setFilters} />
        </aside>
        
        {/* Main Content */}
        <div className="flex-1">
          {/* Bulk Actions Bar */}
          {selectedDocuments.length > 0 && (
            <BulkActionsBar
              selectedCount={selectedDocuments.length}
              onProcess={() => processBatch(selectedDocuments)}
              onDelete={() => deleteBatch(selectedDocuments)}
              onExport={() => exportBatch(selectedDocuments)}
            />
          )}
          
          {/* Documents Grid/List */}
          <DocumentList
            documents={data?.documents}
            isLoading={isLoading}
            viewMode="grid"
            selectedIds={selectedDocuments}
            onSelectionChange={setSelectedDocuments}
          />
          
          {/* Pagination */}
          <Pagination
            currentPage={data?.page || 1}
            totalPages={data?.totalPages || 1}
            onPageChange={(page) => setFilters({ ...filters, page })}
          />
        </div>
      </div>
    </PageContainer>
  );
}
```

## Component Library

### Design Tokens

```typescript
// design-system/tokens.ts
export const tokens = {
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      // ... Full palette
      900: '#1e3a8a',
    },
    gray: {
      // Neutral palette
    },
    success: {
      // Success states
    },
    warning: {
      // Warning states
    },
    error: {
      // Error states
    }
  },
  
  spacing: {
    0: '0',
    1: '0.25rem',
    2: '0.5rem',
    // ... up to 96
  },
  
  typography: {
    fonts: {
      sans: ['Noto Sans Thai', 'Inter', 'system-ui'],
      mono: ['JetBrains Mono', 'monospace']
    },
    sizes: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      // ... up to 6xl
    }
  },
  
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    DEFAULT: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    full: '9999px'
  },
  
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    DEFAULT: '0 1px 3px 0 rgb(0 0 0 / 0.1)',
    // ... Full shadow scale
  }
};
```

### Base Components

#### Button Component

```typescript
// components/ui/Button.tsx
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  isLoading?: boolean;
  isDisabled?: boolean;
  fullWidth?: boolean;
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    variant = 'primary',
    size = 'md',
    leftIcon,
    rightIcon,
    isLoading,
    isDisabled,
    fullWidth,
    className,
    children,
    ...props
  }, ref) => {
    return (
      <button
        ref={ref}
        disabled={isDisabled || isLoading}
        className={cn(
          'inline-flex items-center justify-center font-medium transition-colors',
          'focus:outline-none focus:ring-2 focus:ring-offset-2',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          buttonVariants[variant],
          buttonSizes[size],
          fullWidth && 'w-full',
          className
        )}
        {...props}
      >
        {isLoading ? (
          <Spinner className="mr-2" size={size} />
        ) : leftIcon ? (
          <span className="mr-2">{leftIcon}</span>
        ) : null}
        {children}
        {rightIcon && <span className="ml-2">{rightIcon}</span>}
      </button>
    );
  }
);
```

#### Card Component

```typescript
// components/ui/Card.tsx
interface CardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  variant?: 'elevated' | 'outlined' | 'filled';
}

export function Card({ 
  children, 
  className, 
  padding = 'md',
  variant = 'elevated' 
}: CardProps) {
  return (
    <div className={cn(
      'bg-white dark:bg-gray-800 rounded-lg',
      variant === 'elevated' && 'shadow-sm',
      variant === 'outlined' && 'border border-gray-200 dark:border-gray-700',
      variant === 'filled' && 'bg-gray-50 dark:bg-gray-900',
      paddingClasses[padding],
      className
    )}>
      {children}
    </div>
  );
}

export function CardHeader({ 
  title, 
  description, 
  action 
}: {
  title: string;
  description?: string;
  action?: React.ReactNode;
}) {
  return (
    <div className="flex items-center justify-between mb-4">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {title}
        </h3>
        {description && (
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {description}
          </p>
        )}
      </div>
      {action && <div>{action}</div>}
    </div>
  );
}
```

### Form Components

#### Input Field

```typescript
// components/form/Input.tsx
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  hint?: string;
  leftAddon?: React.ReactNode;
  rightAddon?: React.ReactNode;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ label, error, hint, leftAddon, rightAddon, className, ...props }, ref) => {
    const inputId = useId();
    
    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={inputId} 
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          {leftAddon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              {leftAddon}
            </div>
          )}
          
          <input
            ref={ref}
            id={inputId}
            className={cn(
              'block w-full rounded-md border-gray-300 dark:border-gray-600',
              'bg-white dark:bg-gray-800',
              'text-gray-900 dark:text-white',
              'focus:ring-primary-500 focus:border-primary-500',
              'disabled:bg-gray-50 dark:disabled:bg-gray-900',
              leftAddon && 'pl-10',
              rightAddon && 'pr-10',
              error && 'border-red-500 focus:ring-red-500 focus:border-red-500',
              className
            )}
            aria-invalid={!!error}
            aria-describedby={error ? `${inputId}-error` : hint ? `${inputId}-hint` : undefined}
            {...props}
          />
          
          {rightAddon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              {rightAddon}
            </div>
          )}
        </div>
        
        {error && (
          <p id={`${inputId}-error`} className="mt-1 text-sm text-red-600 dark:text-red-400">
            {error}
          </p>
        )}
        
        {hint && !error && (
          <p id={`${inputId}-hint`} className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {hint}
          </p>
        )}
      </div>
    );
  }
);
```

#### Select Component

```typescript
// components/form/Select.tsx
interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface SelectProps {
  label?: string;
  options: SelectOption[];
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  error?: string;
}

export function Select({ 
  label, 
  options, 
  value, 
  onChange, 
  placeholder = 'Select...',
  error 
}: SelectProps) {
  return (
    <Listbox value={value} onChange={onChange}>
      {({ open }) => (
        <>
          {label && (
            <Listbox.Label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {label}
            </Listbox.Label>
          )}
          
          <div className="relative">
            <Listbox.Button className={cn(
              'relative w-full cursor-default rounded-md',
              'bg-white dark:bg-gray-800 py-2 pl-3 pr-10',
              'border border-gray-300 dark:border-gray-600',
              'text-left focus:outline-none focus:ring-2',
              'focus:ring-primary-500 focus:border-primary-500',
              error && 'border-red-500'
            )}>
              <span className="block truncate">
                {value ? options.find(opt => opt.value === value)?.label : placeholder}
              </span>
              <span className="absolute inset-y-0 right-0 flex items-center pr-2">
                <ChevronUpDownIcon className="h-5 w-5 text-gray-400" />
              </span>
            </Listbox.Button>
            
            <Transition
              show={open}
              as={Fragment}
              leave="transition ease-in duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <Listbox.Options className={cn(
                'absolute z-10 mt-1 max-h-60 w-full overflow-auto',
                'rounded-md bg-white dark:bg-gray-800 py-1',
                'shadow-lg ring-1 ring-black ring-opacity-5',
                'focus:outline-none'
              )}>
                {options.map((option) => (
                  <Listbox.Option
                    key={option.value}
                    value={option.value}
                    disabled={option.disabled}
                    className={({ active }) => cn(
                      'relative cursor-default select-none py-2 pl-10 pr-4',
                      active ? 'bg-primary-100 dark:bg-primary-900' : '',
                      option.disabled && 'opacity-50 cursor-not-allowed'
                    )}
                  >
                    {({ selected, active }) => (
                      <>
                        <span className={cn(
                          'block truncate',
                          selected ? 'font-medium' : 'font-normal'
                        )}>
                          {option.label}
                        </span>
                        {selected && (
                          <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-primary-600">
                            <CheckIcon className="h-5 w-5" />
                          </span>
                        )}
                      </>
                    )}
                  </Listbox.Option>
                ))}
              </Listbox.Options>
            </Transition>
          </div>
          
          {error && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {error}
            </p>
          )}
        </>
      )}
    </Listbox>
  );
}
```

### Data Display Components

#### Data Table

```typescript
// components/data/DataTable.tsx
interface Column<T> {
  key: keyof T;
  header: string;
  sortable?: boolean;
  width?: string;
  render?: (value: any, row: T) => React.ReactNode;
}

interface DataTableProps<T> {
  columns: Column<T>[];
  data: T[];
  isLoading?: boolean;
  selectable?: boolean;
  selectedRows?: string[];
  onSelectionChange?: (selected: string[]) => void;
  onSort?: (column: keyof T, direction: 'asc' | 'desc') => void;
  emptyMessage?: string;
}

export function DataTable<T extends { id: string }>({
  columns,
  data,
  isLoading,
  selectable,
  selectedRows = [],
  onSelectionChange,
  onSort,
  emptyMessage = 'No data available'
}: DataTableProps<T>) {
  const [sortColumn, setSortColumn] = useState<keyof T | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  
  const handleSort = (column: keyof T) => {
    if (!columns.find(col => col.key === column)?.sortable) return;
    
    const newDirection = sortColumn === column && sortDirection === 'asc' ? 'desc' : 'asc';
    setSortColumn(column);
    setSortDirection(newDirection);
    onSort?.(column, newDirection);
  };
  
  const handleSelectAll = () => {
    if (selectedRows.length === data.length) {
      onSelectionChange?.([]);
    } else {
      onSelectionChange?.(data.map(row => row.id));
    }
  };
  
  if (isLoading) {
    return <TableSkeleton columns={columns.length} rows={5} />;
  }
  
  return (
    <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
      <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-900">
          <tr>
            {selectable && (
              <th scope="col" className="relative px-6 sm:w-12 sm:px-6">
                <input
                  type="checkbox"
                  className="absolute left-4 top-1/2 -mt-2 h-4 w-4"
                  checked={selectedRows.length === data.length && data.length > 0}
                  onChange={handleSelectAll}
                />
              </th>
            )}
            
            {columns.map((column) => (
              <th
                key={String(column.key)}
                scope="col"
                className={cn(
                  'px-3 py-3.5 text-left text-sm font-semibold',
                  'text-gray-900 dark:text-white',
                  column.sortable && 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800',
                  column.width
                )}
                onClick={() => column.sortable && handleSort(column.key)}
              >
                <div className="flex items-center space-x-1">
                  <span>{column.header}</span>
                  {column.sortable && (
                    <SortIcon
                      direction={sortColumn === column.key ? sortDirection : null}
                    />
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        
        <tbody className="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
          {data.length === 0 ? (
            <tr>
              <td
                colSpan={columns.length + (selectable ? 1 : 0)}
                className="px-3 py-8 text-center text-sm text-gray-500 dark:text-gray-400"
              >
                {emptyMessage}
              </td>
            </tr>
          ) : (
            data.map((row) => (
              <TableRow
                key={row.id}
                row={row}
                columns={columns}
                selectable={selectable}
                selected={selectedRows.includes(row.id)}
                onSelect={(selected) => {
                  if (selected) {
                    onSelectionChange?.([...selectedRows, row.id]);
                  } else {
                    onSelectionChange?.(selectedRows.filter(id => id !== row.id));
                  }
                }}
              />
            ))
          )}
        </tbody>
      </table>
    </div>
  );
}
```

### Feedback Components

#### Toast Notifications

```typescript
// components/feedback/Toast.tsx
interface ToastProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  duration?: number;
}

export function Toast({ id, type, title, message, action, duration = 5000 }: ToastProps) {
  const { removeToast } = useToast();
  
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        removeToast(id);
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [id, duration, removeToast]);
  
  const icons = {
    success: <CheckCircleIcon className="h-5 w-5 text-green-400" />,
    error: <XCircleIcon className="h-5 w-5 text-red-400" />,
    warning: <ExclamationIcon className="h-5 w-5 text-yellow-400" />,
    info: <InformationCircleIcon className="h-5 w-5 text-blue-400" />
  };
  
  return (
    <div className={cn(
      'max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg',
      'pointer-events-auto ring-1 ring-black ring-opacity-5'
    )}>
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">{icons[type]}</div>
          <div className="ml-3 w-0 flex-1">
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              {title}
            </p>
            {message && (
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {message}
              </p>
            )}
            {action && (
              <button
                onClick={action.onClick}
                className="mt-2 text-sm font-medium text-primary-600 hover:text-primary-500"
              >
                {action.label}
              </button>
            )}
          </div>
          <div className="ml-4 flex-shrink-0 flex">
            <button
              onClick={() => removeToast(id)}
              className="rounded-md inline-flex text-gray-400 hover:text-gray-500"
            >
              <XIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
```

### Medical-Specific Components

#### PII Display

```typescript
// components/medical/PIIDisplay.tsx
interface PIIDisplayProps {
  value: string;
  type: 'thai_id' | 'phone' | 'name' | 'email';
  showFull?: boolean;
  canReveal?: boolean;
}

export function PIIDisplay({ value, type, showFull = false, canReveal = false }: PIIDisplayProps) {
  const [isRevealed, setIsRevealed] = useState(showFull);
  const { hasPermission } = useAuth();
  
  const maskValue = (val: string): string => {
    switch (type) {
      case 'thai_id':
        return val.replace(/^(\d{1})-?(\d{4})-?(\d{5})-?(\d{2})-?(\d{1})$/, '$1-****-*****-**-*');
      case 'phone':
        return val.replace(/^(\d{2,3})-?(\d{3})-?(\d{4})$/, '$1-***-****');
      case 'name':
        return val.split(' ').map(part => part[0] + '*'.repeat(part.length - 1)).join(' ');
      case 'email':
        const [local, domain] = val.split('@');
        return local[0] + '*'.repeat(local.length - 1) + '@' + domain;
      default:
        return '*'.repeat(val.length);
    }
  };
  
  const displayValue = isRevealed ? value : maskValue(value);
  
  return (
    <div className="inline-flex items-center space-x-2">
      <span className="font-mono text-sm">{displayValue}</span>
      {canReveal && hasPermission('view:pii') && (
        <button
          onClick={() => setIsRevealed(!isRevealed)}
          className="text-gray-400 hover:text-gray-600"
          aria-label={isRevealed ? 'Hide' : 'Reveal'}
        >
          {isRevealed ? <EyeOffIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
        </button>
      )}
    </div>
  );
}
```

#### Medical Field Display

```typescript
// components/medical/MedicalFieldDisplay.tsx
interface MedicalFieldDisplayProps {
  field: ExtractedField;
  label: string;
  showConfidence?: boolean;
  showAlternatives?: boolean;
}

export function MedicalFieldDisplay({ 
  field, 
  label, 
  showConfidence = true,
  showAlternatives = false 
}: MedicalFieldDisplayProps) {
  const confidenceColors = {
    High: 'text-green-600 bg-green-100',
    Medium: 'text-yellow-600 bg-yellow-100',
    Low: 'text-red-600 bg-red-100'
  };
  
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </label>
        {showConfidence && (
          <span className={cn(
            'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium',
            confidenceColors[field.confidence]
          )}>
            {field.confidence}
          </span>
        )}
      </div>
      
      <div className="text-sm text-gray-900 dark:text-white">
        {field.value || <span className="text-gray-400">[Blank]</span>}
      </div>
      
      {field.reasoning && (
        <p className="text-xs text-gray-500 dark:text-gray-400 italic">
          {field.reasoning}
        </p>
      )}
      
      {showAlternatives && field.alternative_readings.length > 0 && (
        <div className="mt-1">
          <span className="text-xs text-gray-500">Alternatives: </span>
          {field.alternative_readings.map((alt, idx) => (
            <span key={idx} className="text-xs text-gray-600 dark:text-gray-400">
              {alt}{idx < field.alternative_readings.length - 1 && ', '}
            </span>
          ))}
        </div>
      )}
    </div>
  );
}
```

## State Management

### Global State with Zustand

```typescript
// stores/useAppStore.ts
interface AppState {
  // UI State
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark' | 'system';
  language: 'th' | 'en';
  
  // User State
  user: User | null;
  organization: Organization | null;
  permissions: string[];
  
  // App State
  activeDocument: Document | null;
  notifications: Notification[];
  
  // Actions
  toggleSidebar: () => void;
  setTheme: (theme: AppState['theme']) => void;
  setLanguage: (language: AppState['language']) => void;
  setUser: (user: User | null) => void;
  addNotification: (notification: Notification) => void;
  removeNotification: (id: string) => void;
}

export const useAppStore = create<AppState>((set) => ({
  // Initial state
  sidebarCollapsed: false,
  theme: 'system',
  language: 'th',
  user: null,
  organization: null,
  permissions: [],
  activeDocument: null,
  notifications: [],
  
  // Actions
  toggleSidebar: () => set((state) => ({ 
    sidebarCollapsed: !state.sidebarCollapsed 
  })),
  
  setTheme: (theme) => set({ theme }),
  
  setLanguage: (language) => set({ language }),
  
  setUser: (user) => set({ user }),
  
  addNotification: (notification) => set((state) => ({
    notifications: [...state.notifications, notification]
  })),
  
  removeNotification: (id) => set((state) => ({
    notifications: state.notifications.filter(n => n.id !== id)
  }))
}));
```

## Performance Optimization

### Code Splitting

```typescript
// Lazy load heavy components
const Analytics = lazy(() => import('./modules/Analytics'));
const Reports = lazy(() => import('./modules/Reports'));
const PDFViewer = lazy(() => import('./components/PDFViewer'));

// Route-based code splitting
const routes = [
  {
    path: '/analytics',
    element: (
      <Suspense fallback={<PageLoader />}>
        <Analytics />
      </Suspense>
    )
  }
];
```

### Memoization

```typescript
// Memoized expensive computations
const MemoizedDocumentList = memo(DocumentList, (prevProps, nextProps) => {
  return (
    prevProps.documents === nextProps.documents &&
    prevProps.selectedIds === nextProps.selectedIds
  );
});

// Memoized callbacks
const handleSearch = useCallback((query: string) => {
  setSearchQuery(query);
  debouncedSearch(query);
}, [debouncedSearch]);
```

### Virtual Scrolling

```typescript
// Virtual list for large datasets
import { VariableSizeList } from 'react-window';

export function VirtualDocumentList({ documents }: { documents: Document[] }) {
  const getItemSize = (index: number) => {
    // Variable heights based on content
    return documents[index].hasExtractions ? 120 : 80;
  };
  
  return (
    <VariableSizeList
      height={600}
      itemCount={documents.length}
      itemSize={getItemSize}
      width="100%"
    >
      {({ index, style }) => (
        <div style={style}>
          <DocumentRow document={documents[index]} />
        </div>
      )}
    </VariableSizeList>
  );
}
```

## Testing Strategy

### Component Testing

```typescript
// __tests__/components/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '../Button';

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });
  
  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  it('shows loading state', () => {
    render(<Button isLoading>Click me</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });
});
```

### Integration Testing

```typescript
// __tests__/integration/DocumentUpload.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { DocumentUpload } from '../DocumentUpload';
import { mockSupabase } from '../__mocks__/supabase';

describe('Document Upload Flow', () => {
  it('uploads document successfully', async () => {
    const user = userEvent.setup();
    render(<DocumentUpload />);
    
    const file = new File(['test'], 'test.pdf', { type: 'application/pdf' });
    const input = screen.getByLabelText('Upload document');
    
    await user.upload(input, file);
    
    await waitFor(() => {
      expect(mockSupabase.storage.upload).toHaveBeenCalledWith(
        expect.stringContaining('test.pdf'),
        file
      );
    });
    
    expect(screen.getByText('Upload successful')).toBeInTheDocument();
  });
});
```