# ChromoForge Admin Dashboard - API Specifications

## Overview

This document defines the RESTful API endpoints and real-time subscriptions for the ChromoForge Admin Dashboard. All APIs are built on Supabase's PostgREST and Realtime services, with additional Edge Functions for complex operations.

## API Architecture

### Base URLs
```
Production:  https://[project-ref].supabase.co
REST API:    https://[project-ref].supabase.co/rest/v1
Auth API:    https://[project-ref].supabase.co/auth/v1
Realtime:    wss://[project-ref].supabase.co/realtime/v1
Functions:   https://[project-ref].supabase.co/functions/v1
Storage:     https://[project-ref].supabase.co/storage/v1
```

### Authentication
All API requests require authentication via Supabase Auth JWT tokens.

```typescript
// Request headers
{
  "Authorization": "Bearer {jwt-token}",
  "apikey": "{anon-key}",
  "Content-Type": "application/json",
  "Prefer": "return=minimal" // or return=representation
}
```

## REST API Endpoints

### 1. Authentication & User Management

#### POST /auth/v1/signup
Create a new user account.

```typescript
interface SignupRequest {
  email: string;
  password: string;
  data?: {
    full_name: string;
    organization_name?: string;
    phone?: string;
  };
}

interface SignupResponse {
  user: User;
  session: Session;
  access_token: string;
  refresh_token: string;
}
```

#### POST /auth/v1/token?grant_type=password
User login.

```typescript
interface LoginRequest {
  email: string;
  password: string;
}

interface LoginResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  user: User;
}
```

#### POST /auth/v1/logout
Logout current user.

```typescript
// No request body required
// Returns 204 No Content on success
```

#### GET /rest/v1/user_profiles
Get user profile information.

```typescript
interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  role: 'admin' | 'editor' | 'viewer' | 'analyst';
  organization_id: string;
  created_at: string;
  updated_at: string;
}

// Query parameters
{
  select: "*,organizations(name)",
  id: "eq.{user-id}"
}
```

### 2. Document Management

#### GET /rest/v1/documents
List documents with filtering and pagination.

```typescript
interface DocumentListRequest {
  // Filtering
  status?: 'pending' | 'processing' | 'completed' | 'failed';
  created_at?: string; // gte.2024-01-01
  organization_id?: string;
  
  // Pagination
  offset?: number;
  limit?: number; // default: 25, max: 100
  
  // Sorting
  order?: string; // created_at.desc
  
  // Selection
  select?: string; // *,medical_extractions(*)
}

interface Document {
  id: string;
  file_name: string;
  file_size: number;
  mime_type: string;
  status: DocumentStatus;
  page_count: number;
  uploaded_by: string;
  organization_id: string;
  created_at: string;
  medical_extractions?: MedicalExtraction[];
}
```

#### POST /rest/v1/documents
Create a new document record.

```typescript
interface CreateDocumentRequest {
  file_name: string;
  file_size: number;
  mime_type: string;
  file_hash: string;
  storage_path: string;
  page_count?: number;
}

interface CreateDocumentResponse {
  id: string;
  status: 'pending';
  created_at: string;
}
```

#### PATCH /rest/v1/documents?id=eq.{id}
Update document status.

```typescript
interface UpdateDocumentRequest {
  status?: DocumentStatus;
  error_message?: string;
  page_count?: number;
}
```

#### DELETE /rest/v1/documents?id=eq.{id}
Soft delete a document.

```typescript
// Automatically sets deleted_at timestamp
// Returns 204 No Content on success
```

### 3. OCR Processing

#### POST /functions/v1/process-document
Trigger OCR processing for a document (Edge Function).

```typescript
interface ProcessDocumentRequest {
  document_id: string;
  priority?: 'low' | 'normal' | 'high';
  options?: {
    enable_ultra_think?: boolean;
    confidence_threshold?: number;
    extract_medical_fields?: boolean;
  };
}

interface ProcessDocumentResponse {
  transaction_id: string;
  status: 'queued' | 'processing';
  estimated_completion_time: string;
}
```

#### GET /rest/v1/ocr_processing_transactions
Get OCR processing status.

```typescript
interface ProcessingTransaction {
  id: string;
  document_id: string;
  status: ProcessingStatus;
  attempt_number: number;
  started_at?: string;
  completed_at?: string;
  processing_duration_ms?: number;
  error_message?: string;
  tokens_used?: number;
  api_cost_usd?: number;
}

// Query parameters
{
  select: "*,documents(file_name)",
  document_id: "eq.{document-id}",
  order: "created_at.desc",
  limit: 1
}
```

#### POST /functions/v1/batch-process
Process multiple documents in batch (Edge Function).

```typescript
interface BatchProcessRequest {
  document_ids: string[];
  name: string;
  priority?: number; // 1-10
  parallel_workers?: number; // 1-5
  options?: ProcessingOptions;
}

interface BatchProcessResponse {
  batch_id: string;
  total_documents: number;
  status: 'pending';
  estimated_completion_time: string;
}
```

### 4. Medical Data Extraction

#### GET /rest/v1/medical_records_extraction
Retrieve extracted medical data.

```typescript
interface MedicalExtraction {
  id: string;
  document_id: string;
  patient_name_encrypted?: string; // Decrypted by RLS
  thai_id_encrypted?: string;
  hospital_number_encrypted?: string;
  lab_number_encrypted?: string;
  diagnoses?: string[];
  medications?: string[];
  overall_confidence_score: number;
  manual_review_required: boolean;
  created_at: string;
}

// Query with decryption (handled by RLS)
{
  select: `
    *,
    documents(file_name)
  `,
  is_current: "eq.true",
  manual_review_required: "eq.false"
}
```

#### PATCH /rest/v1/medical_records_extraction?id=eq.{id}
Update extraction with manual corrections.

```typescript
interface UpdateExtractionRequest {
  // Update specific fields
  patient_name_encrypted?: string;
  diagnoses?: string[];
  
  // Review status
  manual_review_required: false;
  reviewed_by: string;
  reviewed_at: string;
  review_notes?: string;
}
```

### 5. Analytics & Reporting

#### GET /rest/v1/dashboard_metrics
Retrieve dashboard analytics data.

```typescript
interface MetricsRequest {
  organization_id: string;
  metric_type: 'daily' | 'weekly' | 'monthly';
  date_range: {
    start: string; // YYYY-MM-DD
    end: string;
  };
}

interface DashboardMetrics {
  metric_date: string;
  total_documents: number;
  processed_documents: number;
  failed_documents: number;
  average_processing_time_ms: number;
  average_confidence_score: number;
  success_rate: number;
  api_cost_usd: number;
  active_users_count: number;
}
```

#### GET /functions/v1/generate-report
Generate a custom report (Edge Function).

```typescript
interface GenerateReportRequest {
  template_id?: string;
  report_type: 'summary' | 'detailed' | 'audit';
  period: {
    start: string;
    end: string;
  };
  filters?: {
    document_status?: string[];
    confidence_threshold?: number;
  };
  format: 'pdf' | 'xlsx' | 'csv';
}

interface GenerateReportResponse {
  report_id: string;
  title: string;
  status: 'generating';
  estimated_time_seconds: number;
  download_url?: string; // Available when complete
}
```

### 6. User Preferences & Settings

#### GET /rest/v1/user_preferences
Get user preferences.

```typescript
interface UserPreferences {
  user_id: string;
  theme: 'light' | 'dark' | 'system';
  language: 'th' | 'en';
  timezone: string;
  dashboard_layout: object;
  notification_preferences: {
    email: boolean;
    push: boolean;
    types: string[];
  };
}
```

#### PATCH /rest/v1/user_preferences?user_id=eq.{id}
Update user preferences.

```typescript
interface UpdatePreferencesRequest {
  theme?: 'light' | 'dark' | 'system';
  language?: 'th' | 'en';
  dashboard_layout?: object;
  notification_preferences?: object;
}
```

### 7. Search & Filtering

#### POST /rest/v1/rpc/search_documents
Full-text search across documents (RPC function).

```typescript
interface SearchRequest {
  query: string;
  search_in: ('content' | 'metadata' | 'extractions')[];
  filters?: {
    date_range?: DateRange;
    status?: string[];
    confidence_min?: number;
  };
  limit?: number;
  offset?: number;
}

interface SearchResult {
  document_id: string;
  file_name: string;
  match_snippet: string;
  relevance_score: number;
  matched_fields: string[];
}
```

## Real-time Subscriptions

### WebSocket Connection
```typescript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(url, anonKey, {
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})
```

### 1. Document Status Updates
Monitor real-time document processing status.

```typescript
// Subscribe to document status changes
const subscription = supabase
  .channel('document-status')
  .on(
    'postgres_changes',
    {
      event: 'UPDATE',
      schema: 'public',
      table: 'documents',
      filter: `organization_id=eq.${orgId}`
    },
    (payload) => {
      console.log('Document updated:', payload.new)
    }
  )
  .subscribe()
```

### 2. OCR Processing Progress
Track OCR processing progress in real-time.

```typescript
// Subscribe to processing transactions
const ocrSubscription = supabase
  .channel('ocr-progress')
  .on(
    'postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'ocr_processing_transactions',
      filter: `document_id=eq.${documentId}`
    },
    (payload) => {
      console.log('Processing update:', payload)
    }
  )
  .subscribe()
```

### 3. Batch Processing Updates
Monitor batch job progress.

```typescript
// Subscribe to batch processing updates
const batchSubscription = supabase
  .channel('batch-progress')
  .on(
    'postgres_changes',
    {
      event: 'UPDATE',
      schema: 'public',
      table: 'batch_processing_jobs',
      filter: `id=eq.${batchId}`
    },
    (payload) => {
      const { progress_percentage, status } = payload.new
      updateProgressBar(progress_percentage)
    }
  )
  .subscribe()
```

### 4. Real-time Analytics
Subscribe to live dashboard metrics.

```typescript
// Subscribe to real-time stats
const statsSubscription = supabase
  .channel('live-stats')
  .on(
    'postgres_changes',
    {
      event: 'UPDATE',
      schema: 'public',
      table: 'real_time_stats',
      filter: `organization_id=eq.${orgId}`
    },
    (payload) => {
      updateDashboard(payload.new)
    }
  )
  .subscribe()
```

### 5. Notification Channel
Receive real-time notifications.

```typescript
// Subscribe to user notifications
const notificationSubscription = supabase
  .channel('notifications')
  .on(
    'postgres_changes',
    {
      event: 'INSERT',
      schema: 'public',
      table: 'notifications',
      filter: `user_id=eq.${userId}`
    },
    (payload) => {
      showNotification(payload.new)
    }
  )
  .subscribe()
```

### 6. Presence Channel
Track online users and their activities.

```typescript
// Presence channel for online users
const presenceChannel = supabase.channel('online-users')

presenceChannel
  .on('presence', { event: 'sync' }, () => {
    const state = presenceChannel.presenceState()
    updateOnlineUsers(state)
  })
  .on('presence', { event: 'join' }, ({ key, newPresences }) => {
    console.log('User joined:', newPresences)
  })
  .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
    console.log('User left:', leftPresences)
  })
  .subscribe(async (status) => {
    if (status === 'SUBSCRIBED') {
      await presenceChannel.track({
        user_id: userId,
        online_at: new Date().toISOString(),
        status: 'active'
      })
    }
  })
```

## Edge Functions

### 1. Document Processing Orchestrator
`/functions/v1/process-document`

```typescript
// Deno Edge Function
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  const { document_id, options } = await req.json()
  
  // 1. Validate document exists
  // 2. Create processing transaction
  // 3. Queue OCR job
  // 4. Return transaction ID
  
  return new Response(
    JSON.stringify({ transaction_id, status: 'queued' }),
    { headers: { 'Content-Type': 'application/json' } }
  )
})
```

### 2. Report Generator
`/functions/v1/generate-report`

```typescript
// Complex report generation with data aggregation
serve(async (req) => {
  const { template_id, period, format } = await req.json()
  
  // 1. Load report template
  // 2. Fetch and aggregate data
  // 3. Generate report file
  // 4. Upload to storage
  // 5. Return download URL
  
  return new Response(
    JSON.stringify({ report_id, download_url }),
    { headers: { 'Content-Type': 'application/json' } }
  )
})
```

### 3. Notification Dispatcher
`/functions/v1/send-notification`

```typescript
// Multi-channel notification delivery
serve(async (req) => {
  const { user_id, notification } = await req.json()
  
  // 1. Get user preferences
  // 2. Queue notifications by channel
  // 3. Send immediate notifications
  // 4. Log delivery status
  
  return new Response(
    JSON.stringify({ sent: true, channels: ['email', 'push'] }),
    { headers: { 'Content-Type': 'application/json' } }
  )
})
```

## API Rate Limiting

### Rate Limit Headers
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

### Rate Limits by Endpoint
```typescript
const rateLimits = {
  '/auth/*': '10 requests per minute',
  '/rest/v1/*': '1000 requests per hour',
  '/functions/v1/process-document': '100 requests per hour',
  '/functions/v1/generate-report': '10 requests per hour',
  '/storage/v1/*': '500 requests per hour',
  'realtime': '100 messages per second'
}
```

## Error Handling

### Standard Error Response
```typescript
interface ErrorResponse {
  error: {
    code: string;
    message: string;
    details?: any;
    hint?: string;
  };
  status: number;
}

// Example error response
{
  "error": {
    "code": "23505",
    "message": "duplicate key value violates unique constraint",
    "details": "Key (email)=(<EMAIL>) already exists.",
    "hint": "Use a different email address"
  },
  "status": 409
}
```

### Common Error Codes
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Unprocessable Entity
- `429` - Too Many Requests
- `500` - Internal Server Error

## SDK Usage Examples

### TypeScript/JavaScript
```typescript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(url, anonKey)

// Fetch documents with related data
const { data, error } = await supabase
  .from('documents')
  .select(`
    *,
    medical_records_extraction!inner(
      overall_confidence_score,
      manual_review_required
    )
  `)
  .eq('status', 'completed')
  .gte('medical_records_extraction.overall_confidence_score', 0.8)
  .order('created_at', { ascending: false })
  .limit(25)
```

### React Query Integration
```typescript
import { useQuery, useMutation } from '@tanstack/react-query'

// Fetch documents hook
export const useDocuments = (filters) => {
  return useQuery({
    queryKey: ['documents', filters],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('documents')
        .select('*')
        .match(filters)
      
      if (error) throw error
      return data
    }
  })
}

// Process document mutation
export const useProcessDocument = () => {
  return useMutation({
    mutationFn: async (documentId) => {
      const { data, error } = await supabase.functions.invoke(
        'process-document',
        { body: { document_id: documentId } }
      )
      
      if (error) throw error
      return data
    }
  })
}
```