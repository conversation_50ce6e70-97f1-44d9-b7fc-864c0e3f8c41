# ChromoForge Admin Dashboard - Security Model

## Overview

This document outlines the comprehensive security model for the ChromoForge Admin Dashboard, ensuring medical-grade data protection, HIPAA compliance, and defense against common security threats while maintaining usability for Thai healthcare professionals.

## Security Architecture

```mermaid
graph TB
    subgraph "Security Layers"
        subgraph "Network Security"
            WAF[Web Application Firewall]
            DDoS[DDoS Protection]
            SSL[SSL/TLS Encryption]
        end
        
        subgraph "Application Security"
            Auth[Authentication]
            AuthZ[Authorization]
            Session[Session Management]
            CSRF[CSRF Protection]
        end
        
        subgraph "Data Security"
            Encrypt[Encryption at Rest]
            Transit[Encryption in Transit]
            PII[PII Protection]
            Audit[Audit Logging]
        end
        
        subgraph "Infrastructure Security"
            RLS[Row Level Security]
            Secrets[Secret Management]
            Backup[Secure Backups]
            Monitor[Security Monitoring]
        end
    end
```

## Authentication System

### Multi-Factor Authentication (MFA)

```typescript
// MFA Configuration
interface MFAConfig {
  required: boolean;
  methods: ('totp' | 'sms' | 'email')[];
  gracePeriodDays: number; // Allow setup grace period
  rememberDeviceDays: number; // Trusted device duration
}

// Organization MFA Policy
const mfaPolicy: MFAConfig = {
  required: true,
  methods: ['totp', 'email'],
  gracePeriodDays: 7,
  rememberDeviceDays: 30
}
```

### Authentication Flow

```sql
-- Enhanced user authentication table
CREATE TABLE auth_factors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    factor_type TEXT NOT NULL, -- 'totp', 'sms', 'email'
    secret_encrypted BYTEA, -- Encrypted TOTP secret
    phone_number TEXT, -- For SMS
    verified BOOLEAN DEFAULT false,
    verified_at TIMESTAMPTZ,
    last_used_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Trusted devices table
CREATE TABLE trusted_devices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    device_fingerprint TEXT NOT NULL,
    device_name TEXT,
    last_ip INET,
    trusted_until TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT unique_user_device UNIQUE(user_id, device_fingerprint)
);
```

### Password Policy

```typescript
interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  preventReuse: number; // Number of previous passwords to check
  expirationDays: number;
  preventCommonPasswords: boolean;
}

const passwordPolicy: PasswordPolicy = {
  minLength: 12,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  preventReuse: 5,
  expirationDays: 90,
  preventCommonPasswords: true
}
```

### Session Management

```typescript
interface SessionConfig {
  duration: number; // minutes
  idleTimeout: number; // minutes
  absoluteTimeout: number; // minutes
  concurrentSessions: number;
  bindToIP: boolean;
  bindToUserAgent: boolean;
}

const sessionConfig: SessionConfig = {
  duration: 480, // 8 hours
  idleTimeout: 30, // 30 minutes
  absoluteTimeout: 720, // 12 hours
  concurrentSessions: 3,
  bindToIP: true,
  bindToUserAgent: true
}
```

## Authorization Model

### Role-Based Access Control (RBAC)

```sql
-- Enhanced roles with granular permissions
CREATE TYPE permission_action AS ENUM (
    'create', 'read', 'update', 'delete', 'export', 'share', 'approve'
);

CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role user_role NOT NULL,
    resource TEXT NOT NULL, -- 'documents', 'users', 'reports', etc.
    actions permission_action[] NOT NULL,
    conditions JSONB DEFAULT '{}', -- Additional conditions
    
    CONSTRAINT unique_role_resource UNIQUE(role, resource)
);

-- Default role permissions
INSERT INTO role_permissions (role, resource, actions, conditions) VALUES
    ('admin', 'all', ARRAY['create', 'read', 'update', 'delete', 'export', 'share', 'approve'], '{}'),
    ('editor', 'documents', ARRAY['create', 'read', 'update', 'export'], '{"own_only": false}'),
    ('viewer', 'documents', ARRAY['read', 'export'], '{"approved_only": true}'),
    ('analyst', 'reports', ARRAY['create', 'read', 'export', 'share'], '{}');
```

### Attribute-Based Access Control (ABAC)

```typescript
// ABAC policy engine
interface AccessPolicy {
  resource: string;
  action: string;
  conditions: {
    user?: UserCondition;
    resource?: ResourceCondition;
    environment?: EnvironmentCondition;
  };
}

interface UserCondition {
  roles?: string[];
  organization?: string;
  attributes?: Record<string, any>;
}

interface ResourceCondition {
  owner?: string;
  status?: string[];
  confidenceScore?: { min?: number; max?: number };
  age?: { days: number; operator: 'lt' | 'gt' };
}

interface EnvironmentCondition {
  ipRange?: string[];
  timeOfDay?: { start: string; end: string };
  location?: string[];
}

// Example policy
const documentAccessPolicy: AccessPolicy = {
  resource: 'documents',
  action: 'read',
  conditions: {
    user: {
      roles: ['viewer', 'editor', 'admin']
    },
    resource: {
      status: ['completed', 'reviewed'],
      confidenceScore: { min: 0.7 }
    },
    environment: {
      ipRange: ['10.0.0.0/8', '***********/16'],
      timeOfDay: { start: '06:00', end: '22:00' }
    }
  }
}
```

## Row Level Security (RLS) Policies

### Enhanced RLS Implementation

```sql
-- Function to check complex permissions
CREATE OR REPLACE FUNCTION check_document_access(
    p_user_id UUID,
    p_document_id UUID,
    p_action permission_action
) RETURNS BOOLEAN AS $$
DECLARE
    v_user_role user_role;
    v_user_org UUID;
    v_doc_org UUID;
    v_doc_status TEXT;
    v_permissions permission_action[];
BEGIN
    -- Get user info
    SELECT role, organization_id INTO v_user_role, v_user_org
    FROM user_profiles WHERE id = p_user_id;
    
    -- Get document info
    SELECT organization_id, status INTO v_doc_org, v_doc_status
    FROM documents WHERE id = p_document_id;
    
    -- Check organization match
    IF v_user_org != v_doc_org THEN
        RETURN FALSE;
    END IF;
    
    -- Get role permissions
    SELECT actions INTO v_permissions
    FROM role_permissions
    WHERE role = v_user_role AND resource = 'documents';
    
    -- Check if action is allowed
    IF p_action = ANY(v_permissions) THEN
        -- Apply additional conditions based on role
        IF v_user_role = 'viewer' AND v_doc_status != 'completed' THEN
            RETURN FALSE;
        END IF;
        
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply enhanced RLS policies
CREATE POLICY "Enhanced document access"
    ON documents FOR ALL
    USING (check_document_access(auth.uid(), id, 
        CASE 
            WHEN TG_OP = 'SELECT' THEN 'read'::permission_action
            WHEN TG_OP = 'INSERT' THEN 'create'::permission_action
            WHEN TG_OP = 'UPDATE' THEN 'update'::permission_action
            WHEN TG_OP = 'DELETE' THEN 'delete'::permission_action
        END
    ));
```

### Data Isolation Policies

```sql
-- Strict organization isolation
CREATE POLICY "Strict org isolation"
    ON medical_records_extraction FOR ALL
    USING (
        organization_id = get_user_organization(auth.uid())
        AND EXISTS (
            SELECT 1 FROM documents d
            WHERE d.id = medical_records_extraction.document_id
            AND d.deleted_at IS NULL
        )
    );

-- Time-based access restrictions
CREATE POLICY "Working hours access"
    ON sensitive_data FOR SELECT
    USING (
        EXTRACT(HOUR FROM CURRENT_TIME) BETWEEN 6 AND 22
        AND EXTRACT(DOW FROM CURRENT_DATE) BETWEEN 1 AND 6
    );
```

## Data Encryption

### Encryption Architecture

```typescript
// Encryption configuration
interface EncryptionConfig {
  algorithm: 'AES-256-GCM';
  keyRotationDays: number;
  keyDerivation: 'PBKDF2' | 'scrypt' | 'argon2';
  saltLength: number;
  tagLength: number;
}

const encryptionConfig: EncryptionConfig = {
  algorithm: 'AES-256-GCM',
  keyRotationDays: 90,
  keyDerivation: 'argon2',
  saltLength: 32,
  tagLength: 16
}
```

### Field-Level Encryption

```sql
-- Encryption key management
CREATE TABLE encryption_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    key_version INTEGER NOT NULL,
    encrypted_key BYTEA NOT NULL, -- Master key encrypted with KEK
    algorithm TEXT DEFAULT 'AES-256-GCM',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    rotated_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    
    CONSTRAINT unique_active_key UNIQUE(organization_id, is_active) WHERE is_active = true
);

-- Encrypt PII function
CREATE OR REPLACE FUNCTION encrypt_pii(
    p_plaintext TEXT,
    p_organization_id UUID
) RETURNS BYTEA AS $$
DECLARE
    v_key BYTEA;
    v_nonce BYTEA;
    v_encrypted BYTEA;
BEGIN
    -- Get active encryption key
    SELECT pgp_sym_decrypt(encrypted_key, get_master_key())
    INTO v_key
    FROM encryption_keys
    WHERE organization_id = p_organization_id
    AND is_active = true;
    
    -- Generate random nonce
    v_nonce := gen_random_bytes(12);
    
    -- Encrypt with AES-256-GCM
    v_encrypted := encrypt_aes_gcm(
        p_plaintext::BYTEA,
        v_key,
        v_nonce
    );
    
    -- Return nonce + ciphertext + tag
    RETURN v_nonce || v_encrypted;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Transparent Data Encryption

```sql
-- Enable TDE for sensitive tables
ALTER TABLE medical_records_extraction 
    SET (encryption_key_id = 'org-specific-key');

-- Encrypted columns with automatic encryption/decryption
CREATE OR REPLACE VIEW decrypted_medical_records AS
SELECT 
    id,
    document_id,
    pgp_sym_decrypt(patient_name_encrypted, get_user_key()) as patient_name,
    pgp_sym_decrypt(thai_id_encrypted, get_user_key()) as thai_id,
    -- Other fields...
FROM medical_records_extraction
WHERE has_decrypt_permission(auth.uid(), id);
```

## API Security

### Request Validation

```typescript
// Request validation middleware
interface RequestValidation {
  validateSchema: boolean;
  validateAuth: boolean;
  validateRateLimit: boolean;
  validateCSRF: boolean;
  sanitizeInput: boolean;
}

const requestValidation: RequestValidation = {
  validateSchema: true,
  validateAuth: true,
  validateRateLimit: true,
  validateCSRF: true,
  sanitizeInput: true
}

// Input sanitization
const sanitizeInput = (input: any): any => {
  // Remove potential XSS vectors
  // Validate against schema
  // Escape special characters
  // Validate file uploads
  return sanitized;
}
```

### API Rate Limiting

```typescript
// Advanced rate limiting configuration
interface RateLimitConfig {
  windowMs: number;
  max: number;
  keyGenerator: (req: Request) => string;
  skip: (req: Request) => boolean;
  handler: (req: Request, res: Response) => void;
}

const rateLimitByRole: Record<string, RateLimitConfig> = {
  admin: {
    windowMs: 60 * 1000, // 1 minute
    max: 100,
    keyGenerator: (req) => `${req.user.id}:${req.ip}`,
    skip: (req) => req.path.startsWith('/health'),
    handler: (req, res) => {
      res.status(429).json({
        error: 'Too many requests',
        retryAfter: 60
      })
    }
  },
  viewer: {
    windowMs: 60 * 1000,
    max: 30,
    // More restrictive for viewers
  }
}
```

### CORS Configuration

```typescript
const corsConfig = {
  origin: (origin: string, callback: Function) => {
    const allowedOrigins = [
      'https://chromoforge.health',
      'https://admin.chromoforge.health',
      /^https:\/\/.*\.chromoforge\.health$/
    ];
    
    if (!origin || allowedOrigins.some(allowed => 
      typeof allowed === 'string' ? allowed === origin : allowed.test(origin)
    )) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PATCH', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
  maxAge: 86400 // 24 hours
}
```

## Security Monitoring

### Audit Logging

```sql
-- Comprehensive audit log
CREATE TABLE security_audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type TEXT NOT NULL, -- 'auth', 'access', 'modification', 'export'
    severity TEXT NOT NULL, -- 'info', 'warning', 'critical'
    user_id UUID REFERENCES auth.users(id),
    organization_id UUID REFERENCES organizations(id),
    
    -- Event details
    resource_type TEXT,
    resource_id UUID,
    action TEXT,
    result TEXT, -- 'success', 'failure', 'blocked'
    
    -- Security context
    ip_address INET,
    user_agent TEXT,
    session_id UUID,
    
    -- Threat detection
    risk_score DECIMAL(3,2), -- 0-1 scale
    anomaly_flags TEXT[],
    
    -- Additional data
    request_data JSONB,
    response_data JSONB,
    error_details JSONB,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for security analysis
CREATE INDEX idx_security_audit_severity ON security_audit_log(severity, created_at DESC);
CREATE INDEX idx_security_audit_risk ON security_audit_log(risk_score DESC) WHERE risk_score > 0.7;
CREATE INDEX idx_security_audit_user ON security_audit_log(user_id, created_at DESC);
```

### Anomaly Detection

```typescript
// Anomaly detection rules
interface AnomalyRule {
  name: string;
  condition: (event: SecurityEvent) => boolean;
  riskScore: number;
  action: 'log' | 'alert' | 'block';
}

const anomalyRules: AnomalyRule[] = [
  {
    name: 'Unusual access time',
    condition: (event) => {
      const hour = new Date(event.timestamp).getHours();
      return hour < 6 || hour > 22;
    },
    riskScore: 0.3,
    action: 'log'
  },
  {
    name: 'Rapid failed attempts',
    condition: (event) => {
      return event.failedAttempts > 5 && event.timeWindow < 300; // 5 min
    },
    riskScore: 0.8,
    action: 'alert'
  },
  {
    name: 'Data exfiltration attempt',
    condition: (event) => {
      return event.exportCount > 100 && event.timeWindow < 3600; // 1 hour
    },
    riskScore: 0.9,
    action: 'block'
  }
]
```

### Security Alerts

```sql
-- Security alert configuration
CREATE TABLE security_alert_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    
    -- Trigger conditions
    event_types TEXT[],
    severity_threshold TEXT,
    frequency_threshold INTEGER, -- Events per time window
    time_window_minutes INTEGER,
    
    -- Alert configuration
    enabled BOOLEAN DEFAULT true,
    alert_channels TEXT[], -- 'email', 'sms', 'webhook'
    recipient_roles user_role[],
    custom_recipients TEXT[],
    
    -- Alert template
    alert_template JSONB,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Example alert rules
INSERT INTO security_alert_rules (name, event_types, severity_threshold, alert_channels) VALUES
    ('Failed login attempts', ARRAY['auth_failure'], 'warning', ARRAY['email']),
    ('Data breach attempt', ARRAY['unauthorized_access', 'mass_export'], 'critical', ARRAY['email', 'sms']),
    ('System compromise', ARRAY['privilege_escalation', 'sql_injection'], 'critical', ARRAY['email', 'sms', 'webhook']);
```

## Compliance & Regulations

### HIPAA Compliance

```typescript
// HIPAA compliance checklist
interface HIPAARequirements {
  accessControls: boolean;
  auditControls: boolean;
  integrity: boolean;
  transmission: boolean;
  encryption: boolean;
  minimumNecessary: boolean;
  businessAssociates: boolean;
}

const hipaaCompliance: HIPAARequirements = {
  accessControls: true, // Unique user identification, automatic logoff
  auditControls: true, // Audit logs, activity tracking
  integrity: true, // Data integrity controls
  transmission: true, // Encryption in transit
  encryption: true, // Encryption at rest
  minimumNecessary: true, // Minimum necessary access
  businessAssociates: true // BAA with Supabase
}
```

### PDPA (Thailand) Compliance

```typescript
// Thai Personal Data Protection Act compliance
interface PDPACompliance {
  consent: boolean;
  purpose: boolean;
  minimization: boolean;
  accuracy: boolean;
  retention: boolean;
  security: boolean;
  rights: boolean;
}

const pdpaCompliance: PDPACompliance = {
  consent: true, // Explicit consent for data processing
  purpose: true, // Clear purpose limitation
  minimization: true, // Collect only necessary data
  accuracy: true, // Keep data accurate and updated
  retention: true, // Data retention policies
  security: true, // Appropriate security measures
  rights: true // Support data subject rights
}
```

## Security Headers

```typescript
// Security headers configuration
const securityHeaders = {
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https:",
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; '),
  'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
}
```

## Incident Response

### Incident Response Plan

```typescript
interface IncidentResponse {
  detection: string[];
  containment: string[];
  eradication: string[];
  recovery: string[];
  lessonsLearned: string[];
}

const incidentResponsePlan: IncidentResponse = {
  detection: [
    'Monitor security alerts',
    'Analyze audit logs',
    'Check anomaly detection',
    'Review user reports'
  ],
  containment: [
    'Isolate affected systems',
    'Revoke compromised credentials',
    'Block malicious IPs',
    'Enable emergency mode'
  ],
  eradication: [
    'Remove malicious code',
    'Patch vulnerabilities',
    'Update security rules',
    'Reset affected accounts'
  ],
  recovery: [
    'Restore from backups',
    'Verify system integrity',
    'Resume normal operations',
    'Monitor for recurrence'
  ],
  lessonsLearned: [
    'Document incident timeline',
    'Analyze root cause',
    'Update security policies',
    'Train staff on prevention'
  ]
}
```

## Security Testing

### Automated Security Testing

```yaml
# Security testing pipeline
security_tests:
  static_analysis:
    - tool: "ESLint Security Plugin"
      rules: "recommended"
    - tool: "Semgrep"
      rulesets: ["owasp", "security-audit"]
  
  dependency_scanning:
    - tool: "npm audit"
      level: "moderate"
    - tool: "Snyk"
      threshold: "high"
  
  dynamic_testing:
    - tool: "OWASP ZAP"
      mode: "active"
    - tool: "Burp Suite"
      scope: "full"
  
  penetration_testing:
    frequency: "quarterly"
    scope: "full application"
    provider: "external"
```

## Key Management

### Encryption Key Lifecycle

```sql
-- Key rotation procedure
CREATE OR REPLACE FUNCTION rotate_encryption_keys()
RETURNS VOID AS $$
DECLARE
    v_org RECORD;
    v_new_key BYTEA;
BEGIN
    FOR v_org IN SELECT id FROM organizations LOOP
        -- Generate new key
        v_new_key := gen_random_bytes(32);
        
        -- Create new key version
        INSERT INTO encryption_keys (
            organization_id,
            key_version,
            encrypted_key,
            created_at
        )
        SELECT 
            v_org.id,
            COALESCE(MAX(key_version), 0) + 1,
            pgp_sym_encrypt(v_new_key, get_master_key()),
            NOW()
        FROM encryption_keys
        WHERE organization_id = v_org.id;
        
        -- Mark old keys as rotated
        UPDATE encryption_keys
        SET is_active = false,
            rotated_at = NOW()
        WHERE organization_id = v_org.id
        AND is_active = true
        AND created_at < NOW() - INTERVAL '90 days';
        
        -- Re-encrypt data with new key (async job)
        PERFORM schedule_reencryption_job(v_org.id);
    END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```