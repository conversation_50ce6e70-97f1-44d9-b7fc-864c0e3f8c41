# ChromoForge Docker Setup Guide

## Overview

ChromoForge supports Docker Desktop for better development visibility and environment isolation. This setup provides hot-reload capabilities, semantic versioning, and easy management through Docker Desktop's interface.

## Version Management

ChromoForge uses semantic versioning (MAJOR.MINOR.PATCH) starting from version 1.0.0:
- **Container Name**: `chromoforge-app` (main application container)
- **Image Name**: `chromoforge:1.0.0` and `chromoforge:latest`
- **Version File**: `VERSION` in project root
- **Version Script**: `scripts/version.sh` for version management

## Prerequisites

✅ **Docker Desktop** - Install and start Docker Desktop
✅ **Environment Variables** - Your `.env` file is already configured

## Quick Start

### 1. Build the Docker Image

```bash
./docker-run.sh build
```

### 2. Start the Container

```bash
./docker-run.sh start
```

### 3. Run Quick Test

```bash
./docker-run.sh test
```

### 4. Process a PDF File

```bash
./docker-run.sh process-file "original-pdf-examples/CSF TT04035.pdf"
```

## Available Commands

The `docker-run.sh` script provides easy access to all ChromoForge functionality:

### Basic Operations
```bash
./docker-run.sh build          # Build Docker image with version tags
./docker-run.sh start          # Start container
./docker-run.sh stop           # Stop container
./docker-run.sh status         # Show container status
./docker-run.sh version        # Show version information
./docker-run.sh logs           # View container logs
./docker-run.sh clean          # Clean up everything
```

### Version Management
```bash
# Show current version
scripts/version.sh show

# Set specific version
scripts/version.sh set 1.2.3

# Bump version (patch: 1.0.0 -> 1.0.1)
scripts/version.sh bump patch

# Bump minor version (1.0.1 -> 1.1.0)
scripts/version.sh bump minor

# Bump major version (1.1.0 -> 2.0.0)
scripts/version.sh bump major

# Build with current version
scripts/version.sh build
```

### OCR Processing
```bash
# Quick functionality test
./docker-run.sh test

# Process single file (simple)
./docker-run.sh process-file "original-pdf-examples/CSF TT04035.pdf" test-results

# Process single file (advanced with options)
./docker-run.sh process-advanced "original-pdf-examples/CSF TT04035.pdf" test-results --verbose --confidence-threshold 0.8

# Process entire directory
./docker-run.sh process-batch original-pdf-examples batch-results
```

### Development
```bash
# Open shell in container
./docker-run.sh shell

# View real-time logs
./docker-run.sh logs
```

## Docker Desktop Visibility

With Docker Desktop, you get full visibility into:

### 1. Container Status
- **Container Name**: `chromoforge-app` (updated from chromoforge-ocr)
- **Image Name**: `chromoforge:1.0.0` and `chromoforge:latest`
- View running containers in Docker Desktop
- Monitor resource usage (CPU, Memory)
- See container logs in real-time

### 2. Volume Mounts
- **Source Code**: `./src` → `/app/src` (read-only, hot-reload)
- **Input PDFs**: `./original-pdf-examples` → `/app/original-pdf-examples`
- **Results**: `./test-results` → `/app/test-results`
- **Batch Results**: `./batch-results` → `/app/batch-results`

### 3. Environment Variables
All your `.env` variables are automatically loaded into the container.

### 4. File Processing
- Input files: Place PDFs in `original-pdf-examples/`
- Output files: Results appear in `test-results/` or `batch-results/`
- Real-time file watching through Docker Desktop

## Development Workflow

### 1. Start Development Environment
```bash
./docker-run.sh build
./docker-run.sh start
```

### 2. Test Core Functionality
```bash
./docker-run.sh test
```

### 3. Process Sample Files
```bash
# Single file
./docker-run.sh process-file "original-pdf-examples/CSF TT04034.pdf"

# Multiple files
./docker-run.sh process-batch
```

### 4. Monitor in Docker Desktop
- Open Docker Desktop
- Go to "Containers" tab
- Click on "chromoforge-app" to see:
  - Real-time logs
  - Resource usage
  - File system
  - Environment variables

### 5. Interactive Development
```bash
# Open shell for debugging
./docker-run.sh shell

# Inside container, you can run:
python quick_test_ocr.py
python run_ocr_pipeline.py "original-pdf-examples/CSF TT04035.pdf" test-results
python -m src.main --help
```

## Hot Reload

The Docker setup includes hot-reload capabilities:
- Source code changes are reflected immediately
- No need to rebuild the image for code changes
- Environment variables: `WATCHPACK_POLLING=true`, `CHOKIDAR_USEPOLLING=true`

## Output and Results

### File Locations
- **OCR Results**: `test-results/{filename}_ocr_results.json`
- **Obfuscated PDFs**: `test-results/obfuscated_{filename}.pdf`
- **Batch Results**: `batch-results/`

### Docker Desktop File Browser
- Navigate to container in Docker Desktop
- Click "Files" tab to browse container filesystem
- View `/app/test-results/` for output files

## Troubleshooting

### Container Issues
```bash
# Check container status
./docker-run.sh status

# View logs
./docker-run.sh logs

# Restart container
./docker-run.sh stop
./docker-run.sh start
```

### Permission Issues
```bash
# Fix file permissions
./docker-run.sh shell
# Inside container:
ls -la test-results/
```

### Clean Start
```bash
# Complete cleanup and rebuild
./docker-run.sh clean
./docker-run.sh build
./docker-run.sh start
```

## Advanced Usage

### Custom Docker Commands
```bash
# Run specific Python commands
docker-compose exec chromoforge python -m src.main --input "original-pdf-examples" --output batch-results --pattern "*.pdf" --verbose

# Access container directly
docker-compose exec chromoforge bash
```

### Environment Overrides
```bash
# Override environment variables
CONFIDENCE_THRESHOLD=0.9 docker-compose up -d
```

## Benefits of Docker Approach

✅ **Full Visibility**: Docker Desktop shows everything
✅ **Environment Isolation**: No virtual environment conflicts
✅ **Hot Reload**: Code changes reflected immediately
✅ **Easy Management**: Simple commands for all operations
✅ **Resource Monitoring**: See CPU/memory usage in real-time
✅ **File System Access**: Browse container files through Docker Desktop
✅ **Log Streaming**: Real-time log viewing
✅ **Consistent Environment**: Same setup across different machines

This Docker setup provides the visibility and control you want while maintaining all the functionality of ChromoForge!
