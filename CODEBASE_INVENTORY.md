# ChromoForge Codebase Inventory

**Generated**: 2025-01-05  
**Purpose**: Comprehensive file and folder categorization for refactoring analysis

## File Categories

### 📁 Source Code (Production)
```
src/
├── __init__.py                 # Package initialization
├── main.py                     # Main application entry point
├── core/                       # Core domain models and configuration
│   ├── __init__.py
│   ├── config.py              # Pydantic settings management
│   ├── constants.py           # Application constants
│   ├── exceptions.py          # Custom exceptions
│   └── models.py              # Domain models (OCRResult, PIIMatch, etc.)
├── processing/                # Business logic layer
│   ├── __init__.py
│   ├── batch_processor.py     # Batch and concurrent processing
│   ├── ocr_processor.py       # Gemini OCR with ULTRA THINK
│   ├── pdf_obfuscator.py      # PDF obfuscation methods
│   └── pii_detector.py        # Thai PII detection patterns
├── security/                  # Security and compliance
│   ├── __init__.py
│   ├── audit_logger.py        # HIPAA compliance logging
│   └── pii_encryption.py      # PII encryption utilities
├── database/                  # Database integration
│   ├── enhanced_medical_service.py  # Supabase medical data service
│   └── enhanced_schema.sql    # Database schema definitions
└── utils/                     # Utilities and helpers
    ├── __init__.py
    ├── logging_config.py      # Structured logging configuration
    └── utils.py               # General utility functions
```

### 🧪 Test Suite
```
tests/
├── __init__.py                # Test package initialization
├── conftest.py               # Pytest configuration and fixtures
├── test_batch_processor_real.py      # Batch processing tests
├── test_core_config_real.py          # Configuration tests
├── test_core_constants_real.py       # Constants tests
├── test_core_exceptions_real.py      # Exception handling tests
├── test_end_to_end_real.py           # End-to-end integration tests
├── test_enhanced_obfuscation.py      # Enhanced obfuscation tests
├── test_gemini_integration_real.py   # Gemini API integration tests
├── test_generic_schema.py            # Database schema tests
├── test_obfuscation_diagnostic.py    # Obfuscation diagnostic tests
├── test_ocr_processor_real.py        # OCR processing tests
├── test_pdf_obfuscation_real.py      # PDF obfuscation tests
├── test_pii_detection_real.py        # PII detection tests
├── test_schema_simple.py             # Simple schema tests
├── test_security_audit_logger_real.py # Security audit tests
├── test_security_pii_encryption_real.py # PII encryption tests
├── test_setup.py                     # Test setup utilities
├── test_supabase_integration_real.py # Supabase integration tests
├── test_utils_logging_config_real.py # Logging configuration tests
└── test_utils_utils_real.py          # Utility function tests
```

### 🐳 Docker Configuration
```
Dockerfile                    # Docker image configuration
docker-compose.yml           # Docker Compose service definition
docker-run.sh               # Docker management script
```

### 📊 Database
```
migrations/
├── README.md                # Migration documentation
├── 001_ocr_pipeline_tables.sql      # Core OCR tables
├── 002_ocr_pipeline_secure_views.sql # Security views
├── 003_ocr_pipeline_indexes_optimization.sql # Performance indexes
└── 004_generic_medical_schema.sql   # Medical data schema

storage-policies.sql         # Supabase storage policies
supabase-setup.sql          # Initial Supabase setup
supabase-architecture.md    # Database architecture documentation
```

### 📋 Configuration
```
requirements.txt            # Python dependencies
pytest.ini                 # Pytest configuration
VERSION                     # Semantic version tracking
.env.example               # Environment variable template
```

### 📚 Documentation
```
README.md                   # Main project documentation
README-Docker.md           # Docker setup guide
CLAUDE.md                  # Claude Code development guide
setup_guide.md             # Setup instructions
implementation-guide.md    # Implementation guidance
```

### 🔧 Scripts
```
scripts/
└── version.sh             # Version management script
```

### 📁 Data Directories
```
original-pdf-examples/      # Sample PDF files for testing
processed/                  # Processed output files
batch-results/             # Batch processing results
results-v2/               # Advanced processing results (v2.0)
test-results/              # Test output files
test-obfuscation/          # Obfuscation test files
test-obfuscation-v2/      # Advanced obfuscation test files (v2.0)
diagnostic-results/        # Diagnostic output files
logs/                      # Application logs
temp/                      # Temporary processing files
```

## Issues Identified

### ✅ Naming Convention Issues Resolved
- ✅ `ENHANCED_FEATURES.md` → `FEATURES_v2.0.0.md`
- ✅ `enhanced-results/` → `results-v2/`
- ✅ `test-enhanced-obfuscation/` → `test-obfuscation-v2/`
- ✅ Redundant cleanup reports consolidated into `PROJECT_HISTORY_v1.0.0.md`

### 📄 Redundant Documentation
- Multiple cleanup reports that may be consolidated
- Overlapping documentation files

### 🧪 Missing Test Coverage
- `src/core/models.py` - Missing dedicated test file `test_core_models_real.py`
- `src/database/enhanced_medical_service.py` - No dedicated test file found

### 🔍 Analysis Required
- Verify all test files have corresponding source code coverage
- Check for unused imports and dependencies
- Validate Docker configuration follows best practices
