"""Common utilities and patterns for ChromoForge OCR pipeline."""

import asyncio
import functools
import logging
import time
from collections import defaultdict
from datetime import datetime
from typing import Callable, TypeVar

from ..core.exceptions import OCRProcessingException

logger = logging.getLogger(__name__)

T = TypeVar("T")


class CircuitBreaker:
    """Unified circuit breaker implementation for all services."""

    def __init__(
        self, failure_threshold: int = 5, timeout: int = 60, name: str = "default"
    ):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.name = name
        self.failure_count = 0
        self.last_failure_time = None
        self.is_open = False

    def __call__(self, func: Callable[..., T]) -> Callable[..., T]:
        """Decorator for circuit breaker pattern."""

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> T:
            if self.is_open:
                if (datetime.now() - self.last_failure_time).seconds < self.timeout:
                    raise OCRProcessingException(
                        message=f"Circuit breaker {self.name} is open",
                        error_code="CIRCUIT_OPEN",
                        retry_able=False,
                    )
                else:
                    # Try to reset
                    self.is_open = False
                    self.failure_count = 0

            try:
                result = await func(*args, **kwargs)
                # Success - reset failure count
                self.failure_count = 0
                return result

            except Exception as e:
                self.failure_count += 1
                self.last_failure_time = datetime.now()

                if self.failure_count >= self.failure_threshold:
                    self.is_open = True
                    logger.error(
                        f"Circuit breaker {self.name} opened after {self.failure_count} failures"
                    )

                raise e

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> T:
            if self.is_open:
                if (datetime.now() - self.last_failure_time).seconds < self.timeout:
                    raise OCRProcessingException(
                        message=f"Circuit breaker {self.name} is open",
                        error_code="CIRCUIT_OPEN",
                        retry_able=False,
                    )
                else:
                    # Try to reset
                    self.is_open = False
                    self.failure_count = 0

            try:
                result = func(*args, **kwargs)
                # Success - reset failure count
                self.failure_count = 0
                return result

            except Exception as e:
                self.failure_count += 1
                self.last_failure_time = datetime.now()

                if self.failure_count >= self.failure_threshold:
                    self.is_open = True
                    logger.error(
                        f"Circuit breaker {self.name} opened after {self.failure_count} failures"
                    )

                raise e

        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper


class RateLimiter:
    """Unified rate limiter implementation."""

    def __init__(
        self, max_calls: int = 60, time_window: int = 60, name: str = "default"
    ):
        self.max_calls = max_calls
        self.time_window = time_window
        self.name = name
        self.calls = defaultdict(list)

    def __call__(self, func: Callable[..., T]) -> Callable[..., T]:
        """Decorator for rate limiting."""

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> T:
            now = time.time()
            window_start = now - self.time_window

            # Clean old calls
            self.calls[self.name] = [
                call_time
                for call_time in self.calls[self.name]
                if call_time > window_start
            ]

            # Check rate limit
            if len(self.calls[self.name]) >= self.max_calls:
                wait_time = self.calls[self.name][0] + self.time_window - now
                logger.warning(
                    f"Rate limit reached for {self.name}. Waiting {wait_time:.1f}s"
                )
                await asyncio.sleep(wait_time)

            # Record call
            self.calls[self.name].append(now)

            return await func(*args, **kwargs)

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> T:
            now = time.time()
            window_start = now - self.time_window

            # Clean old calls
            self.calls[self.name] = [
                call_time
                for call_time in self.calls[self.name]
                if call_time > window_start
            ]

            # Check rate limit
            if len(self.calls[self.name]) >= self.max_calls:
                wait_time = self.calls[self.name][0] + self.time_window - now
                logger.warning(
                    f"Rate limit reached for {self.name}. Waiting {wait_time:.1f}s"
                )
                time.sleep(wait_time)

            # Record call
            self.calls[self.name].append(now)

            return func(*args, **kwargs)

        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper


def exponential_backoff_retry(
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential_base: float = 2.0,
    jitter: bool = True,
):
    """Unified exponential backoff retry decorator.

    Args:
        max_retries: Maximum number of retry attempts
        base_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries
        exponential_base: Base for exponential backoff calculation
        jitter: Whether to add random jitter to prevent thundering herd
    """

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> T:
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)

                except Exception as e:
                    last_exception = e

                    if attempt == max_retries:
                        logger.error(
                            f"All {max_retries + 1} attempts failed for {func.__name__}"
                        )
                        raise e

                    # Calculate delay with exponential backoff
                    delay = min(base_delay * (exponential_base**attempt), max_delay)

                    # Add jitter if enabled
                    if jitter:
                        import random

                        delay = delay * (0.5 + random.random())

                    logger.warning(
                        f"Attempt {attempt + 1}/{max_retries + 1} failed for {func.__name__}. "
                        f"Retrying in {delay:.1f}s..."
                    )

                    await asyncio.sleep(delay)

            # Should never reach here, but just in case
            if last_exception:
                raise last_exception

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> T:
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)

                except Exception as e:
                    last_exception = e

                    if attempt == max_retries:
                        logger.error(
                            f"All {max_retries + 1} attempts failed for {func.__name__}"
                        )
                        raise e

                    # Calculate delay with exponential backoff
                    delay = min(base_delay * (exponential_base**attempt), max_delay)

                    # Add jitter if enabled
                    if jitter:
                        import random

                        delay = delay * (0.5 + random.random())

                    logger.warning(
                        f"Attempt {attempt + 1}/{max_retries + 1} failed for {func.__name__}. "
                        f"Retrying in {delay:.1f}s..."
                    )

                    time.sleep(delay)

            # Should never reach here, but just in case
            if last_exception:
                raise last_exception

        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


# Common configuration access helpers
def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with consistent configuration."""
    return logging.getLogger(name)


# Health check utilities
def get_circuit_breaker_status(circuit_breakers: dict) -> dict:
    """Get status of all circuit breakers."""
    status = {}
    for name, cb in circuit_breakers.items():
        status[name] = {
            "is_open": cb.is_open,
            "failure_count": cb.failure_count,
            "last_failure": (
                cb.last_failure_time.isoformat() if cb.last_failure_time else None
            ),
        }
    return status


def get_rate_limiter_status(rate_limiters: dict) -> dict:
    """Get status of all rate limiters."""
    status = {}
    now = time.time()

    for name, rl in rate_limiters.items():
        window_start = now - rl.time_window
        recent_calls = [
            call_time for call_time in rl.calls[name] if call_time > window_start
        ]

        status[name] = {
            "current_calls": len(recent_calls),
            "max_calls": rl.max_calls,
            "time_window": rl.time_window,
            "available": rl.max_calls - len(recent_calls),
        }

    return status


# OCR-specific decorators
def rate_limiter_ocr(max_calls: int = 60, time_window: int = 60):
    """Rate limiting decorator specifically for OCR operations.

    Args:
        max_calls: Maximum number of calls allowed in time window
        time_window: Time window in seconds
    """
    import threading

    from ..core.exceptions import OCRQuotaExceededException

    def decorator(func):
        func._call_times = []
        func._lock = threading.RLock()

        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            with func._lock:
                current_time = time.time()
                # Remove calls outside the time window
                func._call_times = [
                    call_time
                    for call_time in func._call_times
                    if current_time - call_time < time_window
                ]

                # Check if we're within the rate limit
                if len(func._call_times) >= max_calls:
                    wait_time = time_window - (current_time - func._call_times[0])
                    logger.warning(
                        f"Rate limit reached for {func.__name__}, waiting {wait_time:.2f}s"
                    )

                    raise OCRQuotaExceededException(
                        quota_type="rate_limit",
                        context={
                            "wait_time": wait_time,
                            "current_calls": len(func._call_times),
                        },
                    )

                # Record this call
                func._call_times.append(current_time)

            return await func(*args, **kwargs)

        return wrapper

    return decorator


def circuit_breaker_ocr(failure_threshold: int = 5, timeout: int = 300):
    """Circuit breaker specifically for OCR operations.

    Args:
        failure_threshold: Number of failures before opening circuit
        timeout: Seconds to wait before attempting reset (5 minutes default)
    """
    import threading

    from ..core.exceptions import (OCRAPIException, OCRParsingException,
                                   OCRQuotaExceededException)

    def decorator(func):
        func._failure_count = 0
        func._last_failure_time = 0
        func._circuit_open = False
        func._lock = threading.RLock()

        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            with func._lock:
                # Check if circuit is open
                if func._circuit_open:
                    if time.time() - func._last_failure_time < timeout:
                        raise OCRAPIException(
                            message="OCR circuit breaker is open - API temporarily unavailable",
                            status_code=503,
                            context={
                                "circuit_open_time": func._last_failure_time,
                                "failure_count": func._failure_count,
                                "retry_after": timeout
                                - (time.time() - func._last_failure_time),
                            },
                        )
                    else:
                        # Attempt to reset circuit
                        func._circuit_open = False
                        func._failure_count = 0
                        logger.info(f"OCR circuit breaker reset for {func.__name__}")

            try:
                result = await func(*args, **kwargs)
                # Reset failure count on success
                with func._lock:
                    func._failure_count = 0
                return result
            except (
                OCRAPIException,
                OCRQuotaExceededException,
                OCRParsingException,
            ) as e:
                with func._lock:
                    func._failure_count += 1
                    func._last_failure_time = time.time()

                    if func._failure_count >= failure_threshold:
                        func._circuit_open = True
                        logger.error(
                            f"OCR circuit breaker opened for {func.__name__} after {func._failure_count} failures"
                        )

                raise e

        return wrapper

    return decorator
