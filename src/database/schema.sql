-- ChromoForge Enhanced Medical Document Processing Database Schema
-- Version: 2.0.0
-- This schema implements the comprehensive 14-field medical document extraction
-- with confidence scoring, reasoning, and alternative readings for each field.

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Enhanced medical extractions table with 14-field support
CREATE TABLE IF NOT EXISTS enhanced_medical_extractions (
    -- Primary identification
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID NOT NULL,
    
    -- Processing metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processing_time DECIMAL(10,3) NOT NULL,
    page_count INTEGER NOT NULL DEFAULT 1,
    overall_confidence_score DECIMAL(5,3) NOT NULL CHECK (overall_confidence_score >= 0 AND overall_confidence_score <= 1),
    
    -- 14 Required Medical Document Fields with Enhanced Metadata
    -- Each field stores: value, confidence, reasoning, alternative_readings
    
    -- Patient Code
    patient_code_value TEXT,
    patient_code_confidence TEXT CHECK (patient_code_confidence IN ('Low', 'Medium', 'High')),
    patient_code_reasoning TEXT,
    patient_code_alternatives TEXT[], -- Array of alternative readings
    
    -- Sample Code  
    sample_code_value TEXT,
    sample_code_confidence TEXT CHECK (sample_code_confidence IN ('Low', 'Medium', 'High')),
    sample_code_reasoning TEXT,
    sample_code_alternatives TEXT[],
    
    -- Investigation
    investigation_value TEXT,
    investigation_confidence TEXT CHECK (investigation_confidence IN ('Low', 'Medium', 'High')),
    investigation_reasoning TEXT,
    investigation_alternatives TEXT[],
    
    -- Patient's Full Name (TH)
    patient_name_th_value TEXT,
    patient_name_th_confidence TEXT CHECK (patient_name_th_confidence IN ('Low', 'Medium', 'High')),
    patient_name_th_reasoning TEXT,
    patient_name_th_alternatives TEXT[],
    
    -- Patient's Full Name (EN)
    patient_name_en_value TEXT,
    patient_name_en_confidence TEXT CHECK (patient_name_en_confidence IN ('Low', 'Medium', 'High')),
    patient_name_en_reasoning TEXT,
    patient_name_en_alternatives TEXT[],
    
    -- Gender
    gender_value TEXT,
    gender_confidence TEXT CHECK (gender_confidence IN ('Low', 'Medium', 'High')),
    gender_reasoning TEXT,
    gender_alternatives TEXT[],
    
    -- DOB (YYYY-MM-DD, Gregorian)
    dob_gregorian_value DATE,
    dob_gregorian_confidence TEXT CHECK (dob_gregorian_confidence IN ('Low', 'Medium', 'High')),
    dob_gregorian_reasoning TEXT,
    dob_gregorian_alternatives TEXT[],
    
    -- DOB (BE) (DD/MM/YYYY)
    dob_buddhist_era_value TEXT,
    dob_buddhist_era_confidence TEXT CHECK (dob_buddhist_era_confidence IN ('Low', 'Medium', 'High')),
    dob_buddhist_era_reasoning TEXT,
    dob_buddhist_era_alternatives TEXT[],
    
    -- Patient's Contact No.
    patient_contact_no_value TEXT,
    patient_contact_no_confidence TEXT CHECK (patient_contact_no_confidence IN ('Low', 'Medium', 'High')),
    patient_contact_no_reasoning TEXT,
    patient_contact_no_alternatives TEXT[],
    
    -- Place of Treatment
    place_of_treatment_value TEXT,
    place_of_treatment_confidence TEXT CHECK (place_of_treatment_confidence IN ('Low', 'Medium', 'High')),
    place_of_treatment_reasoning TEXT,
    place_of_treatment_alternatives TEXT[],
    
    -- Referring Physician (TH)
    referring_physician_th_value TEXT,
    referring_physician_th_confidence TEXT CHECK (referring_physician_th_confidence IN ('Low', 'Medium', 'High')),
    referring_physician_th_reasoning TEXT,
    referring_physician_th_alternatives TEXT[],
    
    -- Referring Physician (EN)
    referring_physician_en_value TEXT,
    referring_physician_en_confidence TEXT CHECK (referring_physician_en_confidence IN ('Low', 'Medium', 'High')),
    referring_physician_en_reasoning TEXT,
    referring_physician_en_alternatives TEXT[],
    
    -- Referring Physician MD Code
    referring_physician_md_code_value TEXT,
    referring_physician_md_code_confidence TEXT CHECK (referring_physician_md_code_confidence IN ('Low', 'Medium', 'High')),
    referring_physician_md_code_reasoning TEXT,
    referring_physician_md_code_alternatives TEXT[],
    
    -- Referring Physician Email
    referring_physician_email_value TEXT,
    referring_physician_email_confidence TEXT CHECK (referring_physician_email_confidence IN ('Low', 'Medium', 'High')),
    referring_physician_email_reasoning TEXT,
    referring_physician_email_alternatives TEXT[],
    
    -- Manual review and quality control
    fields_for_manual_review TEXT[], -- List of field names requiring manual review
    manual_review_completed BOOLEAN DEFAULT FALSE,
    manual_review_by UUID, -- User ID who completed manual review
    manual_review_at TIMESTAMP WITH TIME ZONE,
    manual_review_notes TEXT,
    
    -- Full document content and metadata
    full_text TEXT NOT NULL,
    detected_languages TEXT[] DEFAULT ARRAY['thai', 'english'],
    extraction_errors TEXT[] DEFAULT ARRAY[]::TEXT[],
    extraction_warnings TEXT[] DEFAULT ARRAY[]::TEXT[],
    
    -- Audit trail
    created_by UUID, -- User or system that created this record
    updated_by UUID, -- User or system that last updated this record
    
    -- Foreign key constraints
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_enhanced_medical_extractions_document_id ON enhanced_medical_extractions(document_id);
CREATE INDEX IF NOT EXISTS idx_enhanced_medical_extractions_created_at ON enhanced_medical_extractions(created_at);
CREATE INDEX IF NOT EXISTS idx_enhanced_medical_extractions_patient_code ON enhanced_medical_extractions(patient_code_value);
CREATE INDEX IF NOT EXISTS idx_enhanced_medical_extractions_investigation ON enhanced_medical_extractions(investigation_value);
CREATE INDEX IF NOT EXISTS idx_enhanced_medical_extractions_manual_review ON enhanced_medical_extractions(manual_review_completed);
CREATE INDEX IF NOT EXISTS idx_enhanced_medical_extractions_confidence ON enhanced_medical_extractions(overall_confidence_score);

-- Full-text search indexes for Thai and English content
CREATE INDEX IF NOT EXISTS idx_enhanced_medical_extractions_patient_name_th_gin ON enhanced_medical_extractions USING gin(patient_name_th_value gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_enhanced_medical_extractions_patient_name_en_gin ON enhanced_medical_extractions USING gin(patient_name_en_value gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_enhanced_medical_extractions_full_text_gin ON enhanced_medical_extractions USING gin(full_text gin_trgm_ops);

-- Update trigger for updated_at
CREATE OR REPLACE FUNCTION update_enhanced_medical_extractions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_enhanced_medical_extractions_updated_at
    BEFORE UPDATE ON enhanced_medical_extractions
    FOR EACH ROW
    EXECUTE FUNCTION update_enhanced_medical_extractions_updated_at();

-- Row Level Security (RLS) policies
ALTER TABLE enhanced_medical_extractions ENABLE ROW LEVEL SECURITY;

-- Policy for authenticated users to read their own organization's data
CREATE POLICY enhanced_medical_extractions_select_policy ON enhanced_medical_extractions
    FOR SELECT USING (auth.role() = 'authenticated');

-- Policy for authenticated users to insert data
CREATE POLICY enhanced_medical_extractions_insert_policy ON enhanced_medical_extractions
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Policy for authenticated users to update data
CREATE POLICY enhanced_medical_extractions_update_policy ON enhanced_medical_extractions
    FOR UPDATE USING (auth.role() = 'authenticated');

-- Comments for documentation
COMMENT ON TABLE enhanced_medical_extractions IS 'Enhanced medical document extractions with 14-field support, confidence scoring, reasoning, and alternative readings for each field. Implements the comprehensive ChromoForge medical document processing pipeline.';

COMMENT ON COLUMN enhanced_medical_extractions.overall_confidence_score IS 'Overall confidence score (0-1) calculated from individual field confidence levels';
COMMENT ON COLUMN enhanced_medical_extractions.fields_for_manual_review IS 'Array of field names that require manual review due to Low or Medium confidence';
COMMENT ON COLUMN enhanced_medical_extractions.patient_code_value IS 'Patient identifier beginning with TT (e.g., TT04035)';
COMMENT ON COLUMN enhanced_medical_extractions.investigation_value IS 'Test names (K-TRACK, SPOT-MAS, K4CARE, K-TRACK MET, etc.)';
COMMENT ON COLUMN enhanced_medical_extractions.dob_gregorian_value IS 'Date of birth in Gregorian calendar (YYYY-MM-DD)';
COMMENT ON COLUMN enhanced_medical_extractions.dob_buddhist_era_value IS 'Date of birth in Buddhist Era format (DD/MM/YYYY)';
