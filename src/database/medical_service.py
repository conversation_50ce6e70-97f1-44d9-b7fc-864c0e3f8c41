"""Medical document database service for ChromoForge.

This module provides comprehensive database operations for the 14-field
medical document extraction pipeline with confidence scoring, reasoning,
and alternative readings for each field.
"""

import logging
import uuid
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field
from supabase import Client, create_client

from ..core.config import get_supabase_config
from ..core.models import ConfidenceLevel, OCRResult, ExtractedField

logger = logging.getLogger(__name__)


class MedicalExtractionRecord(BaseModel):
    """Database record for medical extractions."""

    id: Optional[str] = None
    document_id: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    processing_time: float
    page_count: int
    overall_confidence_score: float

    # 14 fields with metadata
    patient_code_value: Optional[str] = None
    patient_code_confidence: Optional[str] = None
    patient_code_reasoning: Optional[str] = None
    patient_code_alternatives: List[str] = Field(default_factory=list)

    sample_code_value: Optional[str] = None
    sample_code_confidence: Optional[str] = None
    sample_code_reasoning: Optional[str] = None
    sample_code_alternatives: List[str] = Field(default_factory=list)

    investigation_value: Optional[str] = None
    investigation_confidence: Optional[str] = None
    investigation_reasoning: Optional[str] = None
    investigation_alternatives: List[str] = Field(default_factory=list)

    patient_name_th_value: Optional[str] = None
    patient_name_th_confidence: Optional[str] = None
    patient_name_th_reasoning: Optional[str] = None
    patient_name_th_alternatives: List[str] = Field(default_factory=list)

    patient_name_en_value: Optional[str] = None
    patient_name_en_confidence: Optional[str] = None
    patient_name_en_reasoning: Optional[str] = None
    patient_name_en_alternatives: List[str] = Field(default_factory=list)

    gender_value: Optional[str] = None
    gender_confidence: Optional[str] = None
    gender_reasoning: Optional[str] = None
    gender_alternatives: List[str] = Field(default_factory=list)

    dob_gregorian_value: Optional[str] = None
    dob_gregorian_confidence: Optional[str] = None
    dob_gregorian_reasoning: Optional[str] = None
    dob_gregorian_alternatives: List[str] = Field(default_factory=list)

    dob_buddhist_era_value: Optional[str] = None
    dob_buddhist_era_confidence: Optional[str] = None
    dob_buddhist_era_reasoning: Optional[str] = None
    dob_buddhist_era_alternatives: List[str] = Field(default_factory=list)

    patient_contact_no_value: Optional[str] = None
    patient_contact_no_confidence: Optional[str] = None
    patient_contact_no_reasoning: Optional[str] = None
    patient_contact_no_alternatives: List[str] = Field(default_factory=list)

    place_of_treatment_value: Optional[str] = None
    place_of_treatment_confidence: Optional[str] = None
    place_of_treatment_reasoning: Optional[str] = None
    place_of_treatment_alternatives: List[str] = Field(default_factory=list)

    referring_physician_th_value: Optional[str] = None
    referring_physician_th_confidence: Optional[str] = None
    referring_physician_th_reasoning: Optional[str] = None
    referring_physician_th_alternatives: List[str] = Field(default_factory=list)

    referring_physician_en_value: Optional[str] = None
    referring_physician_en_confidence: Optional[str] = None
    referring_physician_en_reasoning: Optional[str] = None
    referring_physician_en_alternatives: List[str] = Field(default_factory=list)

    referring_physician_md_code_value: Optional[str] = None
    referring_physician_md_code_confidence: Optional[str] = None
    referring_physician_md_code_reasoning: Optional[str] = None
    referring_physician_md_code_alternatives: List[str] = Field(default_factory=list)

    referring_physician_email_value: Optional[str] = None
    referring_physician_email_confidence: Optional[str] = None
    referring_physician_email_reasoning: Optional[str] = None
    referring_physician_email_alternatives: List[str] = Field(default_factory=list)

    # Manual review and quality control
    fields_for_manual_review: List[str] = Field(default_factory=list)
    manual_review_completed: bool = False
    manual_review_by: Optional[str] = None
    manual_review_at: Optional[datetime] = None
    manual_review_notes: Optional[str] = None

    # Full document content and metadata
    full_text: str
    detected_languages: List[str] = Field(default_factory=lambda: ["thai", "english"])
    extraction_errors: List[str] = Field(default_factory=list)
    extraction_warnings: List[str] = Field(default_factory=list)

    # Audit trail
    created_by: Optional[str] = None
    updated_by: Optional[str] = None


class MedicalDatabaseService:
    """Database service for medical document extractions."""

    def __init__(self):
        """Initialize the medical database service."""
        self.config = get_supabase_config()
        self.client: Optional[Client] = None
        self._initialize_client()

    def _initialize_client(self) -> None:
        """Initialize Supabase client with configuration."""
        try:
            if not self.config.get("supabase_url") or not self.config.get(
                "supabase_key"
            ):
                logger.warning(
                    "Supabase configuration not found - database operations will be disabled"
                )
                return

            self.client = create_client(
                self.config["supabase_url"], self.config["supabase_key"]
            )
            logger.info("Medical database service initialized successfully")

        except Exception as e:
            logger.error(
                f"Failed to initialize medical database service: {str(e)}"
            )
            self.client = None

    def _extract_field_data(
        self, field: ExtractedField, field_name: str
    ) -> Dict[str, Any]:
        """Extract field data for database storage.

        Args:
            field: ExtractedField object
            field_name: Name of the field (e.g., 'patient_code')

        Returns:
            Dictionary with field data for database insertion
        """
        return {
            f"{field_name}_value": field.value,
            f"{field_name}_confidence": field.confidence.value,
            f"{field_name}_reasoning": field.reasoning,
            f"{field_name}_alternatives": field.alternative_readings,
        }

    async def store_extraction(
        self,
        ocr_result: OCRResult,
        document_id: str,
        created_by: Optional[str] = None,
    ) -> Optional[str]:
        """Store OCR extraction result in database.

        Args:
            ocr_result: OCR result with 14 fields
            document_id: ID of the source document
            created_by: User ID who created this extraction

        Returns:
            ID of the created extraction record, or None if failed
        """
        if not self.client:
            logger.warning(
                "Database client not available - skipping extraction storage"
            )
            return None

        try:
            # Prepare extraction data
            extraction_data = {
                "document_id": document_id,
                "processing_time": ocr_result.processing_time,
                "page_count": ocr_result.page_count,
                "overall_confidence_score": ocr_result.overall_confidence_score,
                "fields_for_manual_review": ocr_result.fields_for_manual_review,
                "full_text": ocr_result.full_text,
                "detected_languages": ocr_result.detected_languages,
                "extraction_errors": ocr_result.errors,
                "extraction_warnings": ocr_result.warnings,
                "created_by": created_by,
            }

            # Add all 14 fields with their metadata
            field_mapping = {
                "patient_code": ocr_result.patient_code,
                "sample_code": ocr_result.sample_code,
                "investigation": ocr_result.investigation,
                "patient_name_th": ocr_result.patient_name_th,
                "patient_name_en": ocr_result.patient_name_en,
                "gender": ocr_result.gender,
                "dob_gregorian": ocr_result.dob_gregorian,
                "dob_buddhist_era": ocr_result.dob_buddhist_era,
                "patient_contact_no": ocr_result.patient_contact_no,
                "place_of_treatment": ocr_result.place_of_treatment,
                "referring_physician_th": ocr_result.referring_physician_th,
                "referring_physician_en": ocr_result.referring_physician_en,
                "referring_physician_md_code": ocr_result.referring_physician_md_code,
                "referring_physician_email": ocr_result.referring_physician_email,
            }

            for field_name, field_obj in field_mapping.items():
                field_data = self._extract_field_data(field_obj, field_name)
                extraction_data.update(field_data)

            # Insert into database
            result = (
                self.client.table("medical_extractions")
                .insert(extraction_data)
                .execute()
            )

            if result.data and len(result.data) > 0:
                extraction_id = result.data[0]["id"]
                logger.info(f"Successfully stored extraction: {extraction_id}")
                return extraction_id
            else:
                logger.error("Failed to store extraction - no data returned")
                return None

        except Exception as e:
            logger.error(f"Failed to store extraction: {str(e)}")
            return None

    async def get_extraction_by_document_id(
        self, document_id: str
    ) -> Optional[MedicalExtractionRecord]:
        """Retrieve extraction by document ID.

        Args:
            document_id: ID of the source document

        Returns:
            Extraction record or None if not found
        """
        if not self.client:
            logger.warning("Database client not available")
            return None

        try:
            result = (
                self.client.table("medical_extractions")
                .select("*")
                .eq("document_id", document_id)
                .execute()
            )

            if result.data and len(result.data) > 0:
                return MedicalExtractionRecord(**result.data[0])
            else:
                logger.info(f"No extraction found for document: {document_id}")
                return None

        except Exception as e:
            logger.error(f"Failed to retrieve extraction: {str(e)}")
            return None

    async def get_extractions_for_manual_review(
        self, limit: int = 50
    ) -> List[MedicalExtractionRecord]:
        """Get extractions that require manual review.

        Args:
            limit: Maximum number of records to return

        Returns:
            List of extraction records requiring manual review
        """
        if not self.client:
            logger.warning("Database client not available")
            return []

        try:
            result = (
                self.client.table("medical_extractions")
                .select("*")
                .eq("manual_review_completed", False)
                .not_.is_("fields_for_manual_review", "null")
                .order("created_at", desc=True)
                .limit(limit)
                .execute()
            )

            return [MedicalExtractionRecord(**record) for record in result.data]

        except Exception as e:
            logger.error(f"Failed to retrieve extractions for manual review: {str(e)}")
            return []

    async def update_manual_review_status(
        self,
        extraction_id: str,
        completed: bool,
        reviewed_by: str,
        notes: Optional[str] = None,
    ) -> bool:
        """Update manual review status for an extraction.

        Args:
            extraction_id: ID of the extraction record
            completed: Whether manual review is completed
            reviewed_by: User ID who completed the review
            notes: Optional review notes

        Returns:
            True if update was successful, False otherwise
        """
        if not self.client:
            logger.warning("Database client not available")
            return False

        try:
            update_data = {
                "manual_review_completed": completed,
                "manual_review_by": reviewed_by,
                "manual_review_at": datetime.now(timezone.utc).isoformat(),
                "updated_by": reviewed_by,
            }

            if notes:
                update_data["manual_review_notes"] = notes

            result = (
                self.client.table("medical_extractions")
                .update(update_data)
                .eq("id", extraction_id)
                .execute()
            )

            if result.data and len(result.data) > 0:
                logger.info(
                    f"Successfully updated manual review status for extraction: {extraction_id}"
                )
                return True
            else:
                logger.error(
                    f"Failed to update manual review status - no data returned"
                )
                return False

        except Exception as e:
            logger.error(f"Failed to update manual review status: {str(e)}")
            return False
