"""Core OCR processor using Google Gemini 2.5 Pro API.

This module provides OCR processing capabilities using Google's Gemini 2.5 Pro API
for extracting structured medical data from Thai PDF documents. It includes:

- Comprehensive error handling and recovery mechanisms
- Circuit breaker pattern for API reliability
- Exponential backoff retry logic with jitter
- Graceful degradation when services are unavailable
- Rate limiting and quota management
- HIPAA-compliant audit logging for PII operations
- Fallback strategies for common failure scenarios
"""

import asyncio
import base64
import json
import logging
import random
import threading
import time
import uuid
from datetime import datetime, timezone
from functools import wraps
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import google.genai as genai
import httpx
# Safety settings are now under genai.types
from pydantic import BaseModel, Field, field_validator

from ..core.config import get_gemini_config, get_processing_config
from ..core.exceptions import (OCRAPIException, OCRConfigurationException,
                               OCRParsingException, OCRProcessingException,
                               OCRQuotaExceededException, handle_exception)
from ..core.models import (ConfidenceLevel, OCRResult, ExtractedField,
                           OCRResult)
from ..security.audit_logger import (AuditEventType, AuditSeverity,
                                     get_audit_logger)
from ..utils.utils import circuit_breaker_ocr, exponential_backoff_retry
from ..utils.utils import rate_limiter_ocr as rate_limiter

logger = logging.getLogger(__name__)

# Import Supabase for database integration
try:
    from supabase import Client, create_client

    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    logger.warning("Supabase not available. Database transaction recording disabled.")


class GeminiOCRProcessor:
    """OCR processor using Google Gemini 2.5 Pro API.

    This class provides OCR processing capabilities using Google's Gemini 2.5 Pro API
    for extracting structured data from Thai medical PDFs. It includes comprehensive
    error handling, retry logic with exponential backoff, and audit logging.

    The processor supports:
    - Direct PDF processing without image conversion
    - Mixed Thai and English text recognition
    - Handwritten text extraction
    - Structured medical data extraction
    - Comprehensive error handling and recovery
    - Audit logging for PII operations

    Attributes:
        config: Gemini API configuration
        processing_config: Processing configuration parameters
        model: Initialized Gemini generative model
        _lock: Thread lock for concurrent access safety
        _request_count: Counter for API request tracking
        _last_request_time: Timestamp of last API request
    """

    def __init__(
        self, user_id: Optional[str] = None, session_id: Optional[str] = None
    ) -> None:
        """Initialize the Gemini OCR processor.

        Args:
            user_id: User ID for audit logging
            session_id: Session ID for audit logging

        Raises:
            OCRConfigurationException: If configuration is invalid
        """
        try:
            self.config = get_gemini_config()
            self.processing_config = get_processing_config()
            self.user_id = user_id
            self.session_id = session_id

            # Thread safety
            self._lock = threading.RLock()
            self._request_count = 0
            self._last_request_time = 0.0

            # Initialize database client for transaction recording
            self.db_client = None
            if SUPABASE_AVAILABLE:
                try:
                    from ..core.config import settings

                    # Create client with updated API to avoid deprecation warnings
                    self.db_client = create_client(
                        settings.supabase_url,
                        settings.supabase_service_key,
                        options={
                            "postgrest": {
                                "timeout": 30,
                                "verify": True
                            }
                        }
                    )
                    logger.info("Database client initialized for transaction recording")
                except Exception as e:
                    logger.warning(f"Failed to initialize database client: {e}")
                    self.db_client = None

            # Validate configuration
            self._validate_config()

            # Configure Gemini client with new google-genai library
            self.client = genai.Client(api_key=self.config["api_key"])

            # Model configuration with thinking capabilities (based on reference implementation)
            tools = []
            if self.config.get("enable_google_search", True):
                tools.append(genai.types.Tool(googleSearch=genai.types.GoogleSearch()))
            if self.config.get("enable_url_context", True):
                tools.append(genai.types.Tool(url_context=genai.types.UrlContext()))

            self.model_config = genai.types.GenerateContentConfig(
                thinking_config=genai.types.ThinkingConfig(
                    thinking_budget=self.config.get("thinking_budget", -1)
                ),
                tools=tools if tools else None,
            )

            logger.info(
                f"Initialized Gemini OCR processor with model: {self.config['model']}",
                extra={"user_id": user_id, "session_id": session_id},
            )

        except Exception as e:
            raise OCRConfigurationException(
                message=f"Failed to initialize OCR processor: {str(e)}",
                context={"config": self.config if hasattr(self, "config") else None},
                original_exception=e,
            )

    def _validate_config(self) -> None:
        """Validate OCR processor configuration.

        Raises:
            OCRConfigurationException: If configuration is invalid
        """
        required_keys = ["api_key", "model", "temperature", "max_tokens"]
        missing_keys = [key for key in required_keys if key not in self.config]

        if missing_keys:
            raise OCRConfigurationException(
                message=f"Missing required configuration keys: {missing_keys}",
                context={
                    "missing_keys": missing_keys,
                    "config_keys": list(self.config.keys()),
                },
            )

        # Validate API key format
        if not self.config["api_key"] or len(self.config["api_key"]) < 10:
            raise OCRConfigurationException(
                message="Invalid API key format", config_key="api_key"
            )

        # Validate temperature range
        if not 0.0 <= self.config["temperature"] <= 2.0:
            raise OCRConfigurationException(
                message="Temperature must be between 0.0 and 2.0",
                config_key="temperature",
                context={"temperature": self.config["temperature"]},
            )

        # Validate max tokens
        if not 1 <= self.config["max_tokens"] <= 32000:
            raise OCRConfigurationException(
                message="Max tokens must be between 1 and 32000",
                config_key="max_tokens",
                context={"max_tokens": self.config["max_tokens"]},
            )

    def _validate_pdf_file(self, pdf_path: Path) -> None:
        """Validate PDF file before processing.

        Args:
            pdf_path: Path to the PDF file to validate

        Raises:
            OCRProcessingException: If PDF file is invalid or cannot be accessed
        """
        # Check if file exists
        if not pdf_path.exists():
            raise OCRProcessingException(
                message=f"PDF file does not exist: {pdf_path}",
                error_code="PDF_NOT_FOUND_001",
                context={"file_path": str(pdf_path)},
            )

        # Check if it's a file (not a directory)
        if not pdf_path.is_file():
            raise OCRProcessingException(
                message=f"Path is not a file: {pdf_path}",
                error_code="PDF_NOT_FILE_001",
                context={"file_path": str(pdf_path)},
            )

        # Check file size (limit to 50MB)
        max_size = 50 * 1024 * 1024  # 50MB
        file_size = pdf_path.stat().st_size
        if file_size > max_size:
            raise OCRProcessingException(
                message=f"PDF file too large: {file_size} bytes (max: {max_size} bytes)",
                error_code="PDF_TOO_LARGE_001",
                context={
                    "file_path": str(pdf_path),
                    "file_size": file_size,
                    "max_size": max_size,
                },
            )

        # Check if file is empty
        if file_size == 0:
            raise OCRProcessingException(
                message=f"PDF file is empty: {pdf_path}",
                error_code="PDF_EMPTY_001",
                context={"file_path": str(pdf_path)},
            )

        # Check file extension
        if pdf_path.suffix.lower() != ".pdf":
            raise OCRProcessingException(
                message=f"File is not a PDF: {pdf_path} (extension: {pdf_path.suffix})",
                error_code="PDF_INVALID_EXT_001",
                context={"file_path": str(pdf_path), "extension": pdf_path.suffix},
            )

        # Try to read the first few bytes to check if it's a valid PDF
        try:
            with open(pdf_path, "rb") as f:
                header = f.read(8)
                if not header.startswith(b"%PDF-"):
                    raise OCRProcessingException(
                        message=f"File does not appear to be a valid PDF: {pdf_path}",
                        error_code="PDF_INVALID_FORMAT_001",
                        context={
                            "file_path": str(pdf_path),
                            "header": header.hex() if header else "empty",
                        },
                    )
        except IOError as e:
            raise OCRProcessingException(
                message=f"Cannot read PDF file: {pdf_path} - {str(e)}",
                error_code="PDF_READ_ERROR_001",
                context={"file_path": str(pdf_path)},
                original_exception=e,
            )

        logger.debug(f"PDF file validation passed: {pdf_path.name} ({file_size} bytes)")

    @circuit_breaker_ocr(failure_threshold=3, timeout=300)
    @rate_limiter(max_calls=100, time_window=60)
    async def process_pdf_medical(self, pdf_path: Path) -> OCRResult:
        """Process a PDF file with medical 14-field extraction and confidence scoring.

        This method implements the comprehensive ChromoForge medical document processing
        pipeline with detailed field extraction including confidence, reasoning, and
        alternative readings for each field.

        Args:
            pdf_path: Path to the PDF file to process

        Returns:
            OCRResult with 14 fields and detailed metadata

        Raises:
            OCRProcessingException: If processing fails
            OCRAPIException: If API calls fail
            FileNotFoundError: If PDF file doesn't exist
        """
        start_time = time.time()
        processing_context = {
            "file_path": str(pdf_path),
            "file_size": pdf_path.stat().st_size if pdf_path.exists() else 0,
            "processor_type": "medical_14_field",
            "session_id": self.session_id,
        }

        try:
            # Step 1: Validate file
            if not pdf_path.exists():
                raise FileNotFoundError(f"PDF file not found: {pdf_path}")

            if pdf_path.stat().st_size == 0:
                raise OCRProcessingException(
                    message="PDF file is empty",
                    error_code="FILE_EMPTY_001",
                    context=processing_context,
                )

            # Step 2: Convert to base64
            try:
                pdf_base64 = self._convert_pdf_to_base64(pdf_path)
                logger.debug(f"Converted PDF to base64: {len(pdf_base64)} characters")
            except Exception as e:
                raise OCRProcessingException(
                    message=f"Failed to convert PDF to base64: {str(e)}",
                    error_code="PDF_READ_001",
                    context=processing_context,
                    original_exception=e,
                )

            # Step 3: Create medical OCR prompt
            try:
                ocr_prompt = self._create_ocr_prompt()
                logger.debug("Created medical OCR prompt for 14-field extraction")
            except Exception as e:
                raise OCRProcessingException(
                    message=f"Failed to create medical OCR prompt: {str(e)}",
                    error_code="PROMPT_001",
                    context=processing_context,
                    original_exception=e,
                )

            # Step 4: Process with Gemini API
            try:
                logger.info(
                    f"Processing PDF with medical Gemini API (request #{self._request_count + 1})"
                )

                # Apply exponential backoff retry for API calls
                @exponential_backoff_retry(max_retries=2, base_delay=0.5)
                async def api_call() -> str:
                    return await self._process_with_gemini(pdf_base64, ocr_prompt)

                raw_response = await api_call()

                if not raw_response or not raw_response.strip():
                    raise OCRAPIException(
                        message="Empty response from Gemini API",
                        error_code="API_EMPTY_001",
                        context=processing_context,
                    )

                logger.info(
                    f"Received medical response from Gemini API ({len(raw_response)} characters)"
                )

            except Exception as e:
                logger.error(f"Enhanced Gemini API processing failed: {str(e)}")
                raise OCRAPIException(
                    message=f"Enhanced API processing failed: {str(e)}",
                    error_code="API_FAIL_001",
                    context=processing_context,
                    original_exception=e,
                )

            # Step 5: Parse medical response
            try:
                medical_result = self._parse_medical_gemini_response(raw_response)

                # Add processing metadata
                medical_result.processing_time = time.time() - start_time
                medical_result.page_count = self._get_page_count(pdf_path)
                medical_result.document_id = str(uuid.uuid4())
                medical_result.timestamp = datetime.now(timezone.utc).isoformat()

                logger.info(
                    f"Successfully parsed medical OCR result - Overall confidence: {medical_result.overall_confidence_score:.2f}, "
                    f"Processing time: {medical_result.processing_time:.2f}s, "
                    f"Page count: {medical_result.page_count}, "
                    f"Fields for review: {len(medical_result.fields_for_manual_review)}"
                )

                return medical_result

            except Exception as e:
                logger.error(f"Failed to parse medical Gemini response: {str(e)}")
                raise OCRParsingException(
                    message=f"Enhanced response parsing failed: {str(e)}",
                    error_code="PARSE_001",
                    context=processing_context,
                    original_exception=e,
                )

        except Exception as e:
            # Log the error with context
            logger.error(
                f"Enhanced OCR processing failed for {pdf_path}: {str(e)}",
                extra={"context": processing_context},
            )
            raise

    @circuit_breaker_ocr(failure_threshold=3, timeout=300)
    @rate_limiter(max_calls=100, time_window=60)
    async def process_pdf(self, pdf_path: Path) -> OCRResult:
        """Process a PDF file and extract structured medical data with comprehensive error handling.

        This method implements:
        - File validation and preprocessing
        - Circuit breaker pattern for API reliability
        - Rate limiting to prevent quota exhaustion
        - Comprehensive error handling and recovery
        - HIPAA-compliant audit logging

        Args:
            pdf_path: Path to the PDF file to process

        Returns:
            OCRResult with extracted data and metadata

        Raises:
            OCRProcessingException: If processing fails after all recovery attempts
            FileAccessException: If PDF file cannot be accessed or read
            OCRAPIException: If API calls fail
        """
        start_time = time.time()
        processing_context = {
            "file_path": str(pdf_path),
            "file_name": pdf_path.name,
            "user_id": self.user_id,
            "session_id": self.session_id,
        }

        logger.info(
            f"Starting OCR processing for: {pdf_path.name}", extra=processing_context
        )

        try:
            # Step 1: Validate and preprocess PDF file
            self._validate_pdf_file(pdf_path)

            # Step 2: Convert PDF to base64 with error handling
            try:
                pdf_base64 = self._pdf_to_base64(pdf_path)
            except Exception as e:
                raise OCRProcessingException(
                    message=f"Failed to read PDF file: {str(e)}",
                    error_code="PDF_READ_001",
                    context=processing_context,
                    original_exception=e,
                )

            # Step 3: Create OCR prompt
            try:
                ocr_prompt = self._create_ocr_prompt()
                logger.debug("Created OCR prompt for Gemini processing")
            except Exception as e:
                raise OCRProcessingException(
                    message=f"Failed to create OCR prompt: {str(e)}",
                    error_code="PROMPT_001",
                    context=processing_context,
                    original_exception=e,
                )

            # Step 4: Process with Gemini API
            try:
                with self._lock:
                    self._request_count += 1
                    self._last_request_time = time.time()

                logger.info(
                    f"Processing PDF with Gemini API (request #{self._request_count})"
                )

                # Apply exponential backoff retry for API calls
                @exponential_backoff_retry(max_retries=2, base_delay=0.5)
                async def api_call() -> str:
                    return await self._process_with_gemini(pdf_base64, ocr_prompt)

                raw_response = await api_call()

                if not raw_response or not raw_response.strip():
                    raise OCRAPIException(
                        message="Empty response from Gemini API",
                        error_code="API_EMPTY_001",
                        context=processing_context,
                    )

                logger.info(
                    f"Received response from Gemini API ({len(raw_response)} characters)"
                )

            except Exception as e:
                logger.error(f"Gemini API processing failed: {str(e)}")
                raise OCRAPIException(
                    message=f"API processing failed: {str(e)}",
                    error_code="API_FAIL_001",
                    context=processing_context,
                    original_exception=e,
                )

            # Step 5: Parse response
            try:
                ocr_result = self._parse_gemini_response(raw_response)

                # Add processing metadata
                ocr_result.processing_time = time.time() - start_time
                ocr_result.page_count = self._get_page_count(pdf_path)
                ocr_result.document_id = str(uuid.uuid4())
                ocr_result.timestamp = datetime.now(timezone.utc).isoformat()

                logger.info(
                    f"Successfully parsed OCR result - Confidence: {ocr_result.confidence_score:.2f}, "
                    f"Processing time: {ocr_result.processing_time:.2f}s, "
                    f"Page count: {ocr_result.page_count}"
                )

            except Exception as e:
                logger.error(f"Failed to parse Gemini response: {str(e)}")
                raise OCRParsingException(
                    message=f"Response parsing failed: {str(e)}",
                    error_code="PARSE_001",
                    context=processing_context,
                    original_exception=e,
                )

            # Step 6: Audit logging
            try:
                audit_logger = get_audit_logger()

                # Log successful processing
                audit_logger.log_event(
                    event_type=AuditEventType.DOCUMENT_PROCESSED,
                    user_id=self.user_id,
                    session_id=self.session_id,
                    action="pdf_ocr_processing",
                    resource="pdf_document",
                    resource_id=ocr_result.document_id,
                    success=len(ocr_result.errors) == 0,
                    metadata={
                        "file_name": pdf_path.name,
                        "file_size": pdf_path.stat().st_size,
                        "processing_time": ocr_result.processing_time,
                        "confidence_score": ocr_result.confidence_score,
                        "page_count": ocr_result.page_count,
                        "has_pii": ocr_result.has_pii(),
                        "pii_summary": (
                            ocr_result.get_pii_summary() if ocr_result.has_pii() else {}
                        ),
                        "extraction_completeness": ocr_result.get_extraction_completeness(),
                        "detected_languages": ocr_result.detected_languages,
                        "errors": ocr_result.errors,
                        "warnings": ocr_result.warnings,
                    },
                    pii_types=(
                        list(ocr_result.get_pii_summary().keys())
                        if ocr_result.has_pii()
                        else []
                    ),
                )

                # Log PII detection if applicable
                if ocr_result.has_pii():
                    audit_logger.log_event(
                        event_type=AuditEventType.PII_DETECTED,
                        user_id=self.user_id,
                        session_id=self.session_id,
                        action="pii_detection",
                        resource="pdf_document",
                        resource_id=ocr_result.document_id,
                        success=True,
                        metadata={
                            "file_name": pdf_path.name,
                            "pii_types_detected": ocr_result.get_pii_summary(),
                            "confidence_score": ocr_result.confidence_score,
                        },
                        pii_types=list(ocr_result.get_pii_summary().keys()),
                    )

                logger.info(
                    f"Audit logging completed for document: {ocr_result.document_id}"
                )

            except Exception as e:
                # Don't fail the entire operation if audit logging fails
                logger.error(f"Audit logging failed: {str(e)}")
                ocr_result.warnings.append(f"Audit logging failed: {str(e)}")

            # Return successful result
            logger.info(
                f"OCR processing completed successfully for: {pdf_path.name} "
                f"(Processing time: {ocr_result.processing_time:.2f}s, "
                f"Confidence: {ocr_result.confidence_score:.2f})",
                extra=processing_context,
            )

            return ocr_result

        except (OCRProcessingException, OCRAPIException, OCRParsingException) as e:
            # Re-raise known OCR exceptions
            logger.error(f"OCR processing failed: {str(e)}")
            raise

        except Exception as e:
            # Handle unexpected exceptions
            processing_time = time.time() - start_time
            logger.error(
                f"Unexpected error during OCR processing: {str(e)}",
                extra=processing_context,
                exc_info=True,
            )

            # Try to log the error
            try:
                audit_logger = get_audit_logger()
                audit_logger.log_event(
                    event_type=AuditEventType.SYSTEM_ERROR,
                    severity=AuditSeverity.ERROR,
                    user_id=self.user_id,
                    session_id=self.session_id,
                    action="pdf_ocr_processing",
                    resource="pdf_document",
                    success=False,
                    error_code="UNEXPECTED_001",
                    metadata={
                        "file_name": pdf_path.name,
                        "processing_time": processing_time,
                        "error_message": str(e),
                        "error_type": type(e).__name__,
                    },
                )
            except Exception as audit_error:
                logger.error(f"Failed to log error to audit system: {audit_error}")

            raise OCRProcessingException(
                message=f"Unexpected error during OCR processing: {str(e)}",
                error_code="UNEXPECTED_001",
                context=processing_context,
                original_exception=e,
            )

    async def process_pdf_with_retry(self, pdf_path: Path) -> OCRResult:
        """Process PDF with retry logic for failed requests.

        Args:
            pdf_path: Path to the PDF file to process

        Returns:
            OCRResult with extracted data and metadata
        """
        max_retries = self.processing_config["max_retries"]
        retry_delay = self.processing_config["retry_delay"]

        for attempt in range(max_retries + 1):
            try:
                result = await self.process_pdf(pdf_path)

                # If no errors, return result
                if not result.errors:
                    return result

                # If this was the last attempt, return the error result
                if attempt == max_retries:
                    return result

            except Exception as e:
                logger.warning(
                    f"OCR attempt {attempt + 1}/{max_retries + 1} failed for {pdf_path}: {str(e)}"
                )

                # If this was the last attempt, return error result
                if attempt == max_retries:
                    return OCRResult(
                        full_text="",
                        confidence_score=0.0,
                        processing_time=0.0,
                        page_count=0,
                        errors=[f"All {max_retries + 1} attempts failed: {str(e)}"],
                    )

            # Wait before retry
            if attempt < max_retries:
                await asyncio.sleep(retry_delay * (2**attempt))  # Exponential backoff

        # This should never be reached, but just in case
        return OCRResult(
            full_text="",
            confidence_score=0.0,
            processing_time=0.0,
            page_count=0,
            errors=["Maximum retries exceeded"],
        )

    def _pdf_to_base64(self, pdf_path: Path) -> str:
        """Convert PDF file to base64 string.

        Args:
            pdf_path: Path to the PDF file

        Returns:
            Base64 encoded PDF content
        """
        try:
            with open(pdf_path, "rb") as pdf_file:
                pdf_bytes = pdf_file.read()
                return base64.b64encode(pdf_bytes).decode("utf-8")
        except Exception as e:
            logger.error(f"Failed to convert PDF to base64: {str(e)}")
            raise

    def _create_ocr_prompt(self) -> str:
        """Create medical OCR prompt for 14-field medical document extraction.

        Returns:
            Enhanced prompt template implementing the exact ChromoForge requirements
            with 4-part response format (Value, Confidence, Reasoning, Alternative Readings)
        """
        return """Please extract the information listed below from the provided document.

**Crucial Instructions:**
1.  This document contains handwritten entries that are known to be ambiguous. You must proceed with high caution and avoid making definitive assumptions.
2.  For **every single field**, you must provide the following four-part response:
    *   **Value:** The extracted information. If it cannot be found, state "[Blank]".
    *   **Confidence:** Your confidence level in the accuracy of the value [Low, Medium, High].
    *   **Reasoning:** A brief justification for your confidence score. Mention if the data is from a printed sticker, clear handwriting, or ambiguous handwriting. If there are conflicting pieces of information, state them here.
    *   **Alternative Readings:** If the text is ambiguous, provide at least one other possible interpretation. If there is no ambiguity, you can state "None".
3.  After providing the detailed breakdown, create a final summary section titled **"Fields Flagged for Manual Review"** and list all fields with 'Low' or 'Medium' confidence.

**Required Information:**
*   Patient Code
*   Sample Code
*   Investigation
*   Patient's Full Name (TH)
*   Patient's Full Name (EN)
*   Gender
*   DOB (YYYY-MM-DD, Gregorian)
*   DOB (BE) (DD/MM/YYYY)
*   Patient's Contact No.
*   Place of Treatment
*   Referring Physician (TH)
*   Referring Physician (EN)
*   Referring Physician MD Code
*   Referring Physician Email

"""

    async def _process_with_gemini(self, pdf_base64: str, prompt: str) -> str:
        """Process PDF with medical Gemini 2.5 Pro API using new google-genai library.

        Args:
            pdf_base64: Base64 encoded PDF content
            prompt: Enhanced OCR prompt with ULTRA THINK directive

        Returns:
            Raw response from Gemini API with thinking process
        """
        try:
            # Convert base64 string back to bytes (as per reference implementation)
            import base64

            pdf_bytes = base64.b64decode(pdf_base64)

            # Create content parts with PDF and medical prompt (based on reference implementation)
            contents = [
                genai.types.Content(
                    role="user",
                    parts=[
                        genai.types.Part.from_bytes(
                            mime_type="application/pdf", data=pdf_bytes
                        ),
                        genai.types.Part.from_text(text=prompt),
                    ],
                )
            ]

            # Execute request with thinking capabilities
            response = await asyncio.to_thread(
                self.client.models.generate_content,
                model=self.config["model"],
                contents=contents,
                config=self.model_config,
            )

            # Check for safety blocks
            if response.prompt_feedback and response.prompt_feedback.block_reason:
                raise ValueError(
                    f"Content blocked: {response.prompt_feedback.block_reason}"
                )

            # Extract content with thinking process
            if response.candidates and len(response.candidates) > 0:
                candidate = response.candidates[0]

                # Log thinking process if available
                if hasattr(candidate, "thinking") and candidate.thinking:
                    logger.info(
                        f"Gemini thinking process: {candidate.thinking[:200]}..."
                    )

                # Extract final response
                if candidate.content and candidate.content.parts:
                    return candidate.content.parts[0].text
                else:
                    raise ValueError("No content in candidate response")
            else:
                raise ValueError("No candidates generated by Gemini API")

        except Exception as e:
            logger.error(f"Enhanced Gemini API processing failed: {str(e)}")
            raise

    def _parse_gemini_response(self, response_text: str) -> OCRResult:
        """Parse structured response from Gemini API using new generic schema.

        Args:
            response_text: Raw response text from Gemini

        Returns:
            Parsed OCRResult object with new 13-field schema
        """
        try:
            # Clean response text (remove markdown formatting if present)
            clean_text = response_text.strip()
            if clean_text.startswith("```json"):
                clean_text = clean_text[7:]
            if clean_text.endswith("```"):
                clean_text = clean_text[:-3]
            clean_text = clean_text.strip()

            # Parse JSON response
            parsed_data = json.loads(clean_text)

            # Create OCRResult with new generic schema
            return OCRResult(
                # Basic content
                full_text=parsed_data.get("full_text", ""),
                # Core 13-field medical record schema
                patient_code=parsed_data.get("patient_code"),
                sample_code=parsed_data.get("sample_code"),
                investigation=parsed_data.get("investigation"),
                patient_name_th=parsed_data.get("patient_name_th"),
                patient_name_en=parsed_data.get("patient_name_en"),
                dob_gregorian=parsed_data.get("dob_gregorian"),
                dob_buddhist_era=parsed_data.get("dob_buddhist_era"),
                patient_contact_no=parsed_data.get("patient_contact_no"),
                place_of_treatment=parsed_data.get("place_of_treatment"),
                referring_physician_th=parsed_data.get("referring_physician_th"),
                referring_physician_en=parsed_data.get("referring_physician_en"),
                referring_physician_md_code=parsed_data.get(
                    "referring_physician_md_code"
                ),
                referring_physician_email=parsed_data.get(
                    "referring_physician_email", []
                ),
                # Legacy compatibility
                thai_id=parsed_data.get("thai_id"),
                # Additional medical information
                diagnoses=parsed_data.get("diagnoses", []),
                medications=parsed_data.get("medications", []),
                test_results=parsed_data.get("test_results", {}),
                # Processing metadata
                detected_languages=parsed_data.get("detected_languages", []),
                confidence_score=float(parsed_data.get("confidence_score", 0.5)),
                warnings=parsed_data.get("warnings", []),
                # Will be set by caller
                processing_time=0.0,
                page_count=0,
                errors=[],
            )

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse Gemini response as JSON: {str(e)}")
            logger.debug(f"Raw response (first 500 chars): {response_text[:500]}")

            # Try to extract text from markdown-formatted response
            if "```json" in response_text:
                try:
                    # Extract JSON from markdown code block
                    start = response_text.find("```json") + 7
                    end = response_text.find("```", start)
                    if end > start:
                        json_text = response_text[start:end].strip()
                        parsed_response = json.loads(json_text)
                        logger.info("Successfully extracted JSON from markdown response")
                        # Continue with normal processing
                        return self._process_gemini_response(parsed_response, pdf_path)
                except (json.JSONDecodeError, ValueError) as inner_e:
                    logger.error(f"Failed to extract JSON from markdown: {str(inner_e)}")

            # Return basic result with raw text
            return OCRResult(
                full_text=response_text,
                confidence_score=0.3,
                processing_time=0.0,
                page_count=0,
                errors=[f"JSON parsing failed: {str(e)}"],
            )

        except Exception as e:
            logger.error(f"Failed to parse Gemini response: {str(e)}")

            return OCRResult(
                full_text="",
                confidence_score=0.0,
                processing_time=0.0,
                page_count=0,
                errors=[f"Response parsing failed: {str(e)}"],
            )

    def _parse_medical_gemini_response(self, response_text: str) -> OCRResult:
        """Parse medical 14-field response from Gemini API with confidence and reasoning.

        Args:
            response_text: Raw response text from Gemini with 4-part field format

        Returns:
            Parsed OCRResult object with 14 fields and metadata
        """
        try:
            # Extract field information using regex patterns
            import re

            # Initialize fields dictionary
            fields = {}
            fields_for_review = []

            # Field mapping for the 14 required fields
            field_mapping = {
                "Patient Code": "patient_code",
                "Sample Code": "sample_code",
                "Investigation": "investigation",
                "Patient's Full Name (TH)": "patient_name_th",
                "Patient's Full Name (EN)": "patient_name_en",
                "Gender": "gender",
                "DOB (YYYY-MM-DD, Gregorian)": "dob_gregorian",
                "DOB (BE) (DD/MM/YYYY)": "dob_buddhist_era",
                "Patient's Contact No.": "patient_contact_no",
                "Place of Treatment": "place_of_treatment",
                "Referring Physician (TH)": "referring_physician_th",
                "Referring Physician (EN)": "referring_physician_en",
                "Referring Physician MD Code": "referring_physician_md_code",
                "Referring Physician Email": "referring_physician_email",
            }

            # Parse each field using regex
            for field_name, field_key in field_mapping.items():
                # Pattern to match field sections
                pattern = rf"\*\*{re.escape(field_name)}\*\*\s*\*\s*\*\*Value:\*\*\s*(.+?)\s*\*\s*\*\*Confidence:\*\*\s*(Low|Medium|High)\s*\*\s*\*\*Reasoning:\*\*\s*(.+?)\s*\*\s*\*\*Alternative Readings:\*\*\s*(.+?)(?=\*\*|$)"

                match = re.search(pattern, response_text, re.DOTALL | re.IGNORECASE)

                if match:
                    value = match.group(1).strip()
                    confidence = match.group(2).strip()
                    reasoning = match.group(3).strip()
                    alternatives = match.group(4).strip()

                    # Handle [Blank] values
                    if value == "[Blank]":
                        value = None

                    # Parse alternative readings
                    alt_list = []
                    if alternatives and alternatives.lower() != "none":
                        alt_list = [
                            alt.strip()
                            for alt in alternatives.split(",")
                            if alt.strip()
                        ]

                    # Create ExtractedField
                    fields[field_key] = ExtractedField(
                        value=value,
                        confidence=ConfidenceLevel(confidence),
                        reasoning=reasoning,
                        alternative_readings=alt_list,
                    )

                    # Add to manual review if confidence is Low or Medium
                    if confidence in ["Low", "Medium"]:
                        fields_for_review.append(field_name)

                else:
                    # Create blank field if not found
                    fields[field_key] = ExtractedField(
                        value=None,
                        confidence=ConfidenceLevel.LOW,
                        reasoning="Field not found in response",
                        alternative_readings=[],
                    )
                    fields_for_review.append(field_name)

            # Extract manual review section
            review_pattern = (
                r"\*\*Fields Flagged for Manual Review\*\*\s*(.+?)(?=\n\n|\Z)"
            )
            review_match = re.search(review_pattern, response_text, re.DOTALL)
            if review_match:
                review_text = review_match.group(1).strip()
                # Parse the list items
                review_items = re.findall(r"\*\s*(.+)", review_text)
                fields_for_review.extend([item.strip() for item in review_items])

            # Remove duplicates from manual review list
            fields_for_review = list(set(fields_for_review))

            # Calculate overall confidence
            confidence_scores = {
                ConfidenceLevel.HIGH: 1.0,
                ConfidenceLevel.MEDIUM: 0.7,
                ConfidenceLevel.LOW: 0.3,
            }

            total_confidence = sum(
                confidence_scores[field.confidence] for field in fields.values()
            )
            overall_confidence = total_confidence / len(fields) if fields else 0.0

            # Create OCRResult
            return OCRResult(
                **fields,
                fields_for_manual_review=fields_for_review,
                full_text=response_text,
                overall_confidence_score=overall_confidence,
                processing_time=0.0,  # Will be set by caller
                page_count=0,  # Will be set by caller
                detected_languages=["thai", "english"],
                errors=[],
                warnings=[],
            )

        except Exception as e:
            logger.error(f"Failed to parse medical Gemini response: {str(e)}")

            # Create default result with all blank fields
            blank_fields = {}
            for field_key in field_mapping.values():
                blank_fields[field_key] = ExtractedField(
                    value=None,
                    confidence=ConfidenceLevel.LOW,
                    reasoning=f"Parsing failed: {str(e)}",
                    alternative_readings=[],
                )

            return OCRResult(
                **blank_fields,
                fields_for_manual_review=list(field_mapping.keys()),
                full_text=response_text,
                overall_confidence_score=0.0,
                processing_time=0.0,
                page_count=0,
                detected_languages=[],
                errors=[f"Enhanced parsing failed: {str(e)}"],
                warnings=[],
            )

    def _get_page_count(self, pdf_path: Path) -> int:
        """Get the number of pages in a PDF file.

        Args:
            pdf_path: Path to the PDF file

        Returns:
            Number of pages in the PDF
        """
        try:
            import pypdf

            with open(pdf_path, "rb") as pdf_file:
                pdf_reader = pypdf.PdfReader(pdf_file)
                return len(pdf_reader.pages)

        except Exception as e:
            logger.warning(f"Failed to get page count for {pdf_path}: {str(e)}")
            return 1  # Default to 1 page

    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics for this processor instance.

        Returns:
            Dictionary containing processing statistics
        """
        with self._lock:
            return {
                "total_requests": self._request_count,
                "last_request_time": self._last_request_time,
                "model_name": self.config.get("model", "unknown"),
                "temperature": self.config.get("temperature", 0.0),
                "max_tokens": self.config.get("max_tokens", 0),
            }

    def reset_stats(self) -> None:
        """Reset processing statistics."""
        with self._lock:
            self._request_count = 0
            self._last_request_time = 0.0

    async def record_processing_transaction(
        self,
        pdf_path: Path,
        ocr_result: OCRResult,
        organization_id: Optional[str] = None,
    ) -> Optional[str]:
        """Record OCR processing transaction in database using new generic schema.

        Args:
            pdf_path: Path to processed PDF file
            ocr_result: OCR processing result with generic schema
            organization_id: Organization ID for multi-tenant support

        Returns:
            Transaction ID if successful, None otherwise
        """
        if not self.db_client:
            logger.debug(
                "Database client not available, skipping transaction recording"
            )
            return None

        try:
            # Generate transaction ID
            transaction_id = str(uuid.uuid4())

            # Create document record
            document_data = {
                "id": transaction_id,
                "organization_id": organization_id
                or str(uuid.uuid4()),  # Default org if not provided
                "uploaded_by": self.user_id
                or str(uuid.uuid4()),  # Default user if not provided
                "file_name": pdf_path.name,
                "file_size": pdf_path.stat().st_size,
                "mime_type": "application/pdf",
                "checksum": self._calculate_file_checksum(pdf_path),
                "status": "completed" if not ocr_result.errors else "failed",
                "processing_started_at": datetime.utcnow().isoformat(),
                "processing_completed_at": datetime.utcnow().isoformat(),
                "processing_error": (
                    {"errors": ocr_result.errors} if ocr_result.errors else None
                ),
                "page_count": ocr_result.page_count,
                "metadata": {
                    "confidence_score": ocr_result.confidence_score,
                    "detected_languages": ocr_result.detected_languages,
                    "processing_time": ocr_result.processing_time,
                    "session_id": self.session_id,
                    "extraction_completeness": ocr_result.get_extraction_completeness(),
                },
            }

            # Insert document record
            document_result = await asyncio.to_thread(
                self.db_client.table("documents").insert(document_data).execute
            )

            # Create medical record extraction if OCR was successful and contains medical data
            has_generic_fields = any(
                [
                    ocr_result.patient_code,
                    ocr_result.sample_code,
                    ocr_result.investigation,
                    ocr_result.patient_name_th,
                    ocr_result.patient_name_en,
                ]
            )

            # Also check legacy fields for backward compatibility
            has_legacy_fields = any(
                [
                    ocr_result.patient_name_th
                    or ocr_result.patient_name_en,  # Legacy patient_name
                    ocr_result.thai_id,
                ]
            )

            if not ocr_result.errors and (has_generic_fields or has_legacy_fields):
                # Create OCR processing transaction record
                ocr_transaction_data = {
                    "id": str(uuid.uuid4()),
                    "organization_id": organization_id or str(uuid.uuid4()),
                    "document_id": transaction_id,
                    "status": "completed",
                    "attempt_number": 1,
                    "max_attempts": 3,
                    "started_at": datetime.utcnow().isoformat(),
                    "completed_at": datetime.utcnow().isoformat(),
                    "processing_duration_ms": int(ocr_result.processing_time * 1000),
                    "model_name": self.config.get("model", "gemini-2.5-pro"),
                    "model_version": "2.5-pro",
                    "model_parameters": {
                        "temperature": self.config.get("temperature", 0.1),
                        "max_tokens": self.config.get("max_tokens", 32000),
                        "thinking_budget": -1,
                    },
                    "created_by": self.user_id,
                }

                ocr_transaction_result = await asyncio.to_thread(
                    self.db_client.table("ocr_processing_transactions")
                    .insert(ocr_transaction_data)
                    .execute
                )

                ocr_transaction_id = ocr_transaction_result.data[0]["id"]

                # Create medical records extraction with generic schema
                extraction_data = {
                    "id": str(uuid.uuid4()),
                    "document_id": transaction_id,
                    "organization_id": organization_id or str(uuid.uuid4()),
                    "transaction_id": ocr_transaction_id,
                    "version": 1,
                    "is_current": True,
                    # Generic schema fields (non-encrypted for now - would use encryption in production)
                    "patient_code": ocr_result.patient_code,
                    "patient_code_confidence": 0.9 if ocr_result.patient_code else None,
                    "sample_code": ocr_result.sample_code,
                    "sample_code_confidence": 0.8 if ocr_result.sample_code else None,
                    "investigation": ocr_result.investigation,
                    "investigation_confidence": (
                        0.9 if ocr_result.investigation else None
                    ),
                    # PII fields would be encrypted in production
                    "patient_name_th_encrypted": (
                        f"ENCRYPT:{ocr_result.patient_name_th}".encode()
                        if ocr_result.patient_name_th
                        else None
                    ),
                    "patient_name_th_confidence": (
                        0.9 if ocr_result.patient_name_th else None
                    ),
                    "patient_name_en_encrypted": (
                        f"ENCRYPT:{ocr_result.patient_name_en}".encode()
                        if ocr_result.patient_name_en
                        else None
                    ),
                    "patient_name_en_confidence": (
                        0.8 if ocr_result.patient_name_en else None
                    ),
                    "dob_gregorian_encrypted": (
                        f"ENCRYPT:{ocr_result.dob_gregorian}".encode()
                        if ocr_result.dob_gregorian
                        else None
                    ),
                    "dob_gregorian_confidence": (
                        0.8 if ocr_result.dob_gregorian else None
                    ),
                    "dob_buddhist_era_encrypted": (
                        f"ENCRYPT:{ocr_result.dob_buddhist_era}".encode()
                        if ocr_result.dob_buddhist_era
                        else None
                    ),
                    "dob_buddhist_era_confidence": (
                        0.8 if ocr_result.dob_buddhist_era else None
                    ),
                    "patient_contact_no_encrypted": (
                        f"ENCRYPT:{ocr_result.patient_contact_no}".encode()
                        if ocr_result.patient_contact_no
                        else None
                    ),
                    "patient_contact_no_confidence": (
                        0.7 if ocr_result.patient_contact_no else None
                    ),
                    "place_of_treatment": ocr_result.place_of_treatment,
                    "place_of_treatment_confidence": (
                        0.8 if ocr_result.place_of_treatment else None
                    ),
                    "referring_physician_th_encrypted": (
                        f"ENCRYPT:{ocr_result.referring_physician_th}".encode()
                        if ocr_result.referring_physician_th
                        else None
                    ),
                    "referring_physician_th_confidence": (
                        0.8 if ocr_result.referring_physician_th else None
                    ),
                    "referring_physician_en_encrypted": (
                        f"ENCRYPT:{ocr_result.referring_physician_en}".encode()
                        if ocr_result.referring_physician_en
                        else None
                    ),
                    "referring_physician_en_confidence": (
                        0.8 if ocr_result.referring_physician_en else None
                    ),
                    "referring_physician_md_code": ocr_result.referring_physician_md_code,
                    "referring_physician_md_code_confidence": (
                        0.7 if ocr_result.referring_physician_md_code else None
                    ),
                    "referring_physician_email_array_encrypted": (
                        f"ENCRYPT:{json.dumps(ocr_result.referring_physician_email)}".encode()
                        if ocr_result.referring_physician_email
                        else None
                    ),
                    "referring_physician_email_array_confidence": (
                        0.9 if ocr_result.referring_physician_email else None
                    ),
                    # Legacy fields for backward compatibility
                    "patient_name_encrypted": (
                        f"ENCRYPT:{ocr_result.patient_name_th or ocr_result.patient_name_en}".encode()
                        if (ocr_result.patient_name_th or ocr_result.patient_name_en)
                        else None
                    ),
                    "patient_name_confidence": (
                        0.9
                        if (ocr_result.patient_name_th or ocr_result.patient_name_en)
                        else None
                    ),
                    "thai_id_encrypted": (
                        f"ENCRYPT:{ocr_result.thai_id}".encode()
                        if ocr_result.thai_id
                        else None
                    ),
                    "thai_id_confidence": 0.8 if ocr_result.thai_id else None,
                    "phone_encrypted": (
                        f"ENCRYPT:{ocr_result.patient_contact_no}".encode()
                        if ocr_result.patient_contact_no
                        else None
                    ),
                    "phone_confidence": 0.7 if ocr_result.patient_contact_no else None,
                    "referring_physician_encrypted": (
                        f"ENCRYPT:{ocr_result.referring_physician_th or ocr_result.referring_physician_en}".encode()
                        if (
                            ocr_result.referring_physician_th
                            or ocr_result.referring_physician_en
                        )
                        else None
                    ),
                    "referring_physician_confidence": (
                        0.8
                        if (
                            ocr_result.referring_physician_th
                            or ocr_result.referring_physician_en
                        )
                        else None
                    ),
                    "referring_physician_email_encrypted": (
                        f"ENCRYPT:{json.dumps(ocr_result.referring_physician_email)}".encode()
                        if ocr_result.referring_physician_email
                        else None
                    ),
                    "referring_physician_email_confidence": (
                        0.9 if ocr_result.referring_physician_email else None
                    ),
                    # Medical information
                    "diagnoses": ocr_result.diagnoses,
                    "diagnoses_confidence": 0.8 if ocr_result.diagnoses else None,
                    "medications": ocr_result.medications,
                    "medications_confidence": 0.8 if ocr_result.medications else None,
                    "test_results": ocr_result.test_results,
                    "test_results_confidence": 0.8 if ocr_result.test_results else None,
                    # Overall metrics
                    "overall_confidence_score": ocr_result.confidence_score,
                    "extracted_text_full": ocr_result.full_text,
                    "detected_languages": ocr_result.detected_languages,
                    "extraction_warnings": ocr_result.warnings,
                    "manual_review_required": ocr_result.confidence_score < 0.7,
                }

                # Insert medical extraction record
                await asyncio.to_thread(
                    self.db_client.table("medical_records_extraction")
                    .insert(extraction_data)
                    .execute
                )

            logger.info(
                f"Successfully recorded transaction with generic schema: {transaction_id}"
            )
            return transaction_id

        except Exception as e:
            logger.warning(f"Failed to record processing transaction: {str(e)}")
            logger.debug(f"Error details: {e}", exc_info=True)
            return None

    def _calculate_file_checksum(self, file_path: Path) -> str:
        """Calculate MD5 checksum of file.

        Args:
            file_path: Path to file

        Returns:
            MD5 checksum as hex string
        """
        import hashlib

        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.warning(f"Failed to calculate checksum for {file_path}: {e}")
            return "unknown"

    async def process_pdf_batch(
        self, pdf_paths: List[Path], organization_id: Optional[str] = None
    ) -> List[Tuple[Path, OCRResult, Optional[str]]]:
        """Process multiple PDF files with database transaction recording.

        Args:
            pdf_paths: List of PDF file paths to process
            organization_id: Organization ID for multi-tenant support

        Returns:
            List of tuples containing (pdf_path, ocr_result, transaction_id)
        """
        results = []

        for pdf_path in pdf_paths:
            try:
                # Process PDF
                ocr_result = await self.process_pdf_with_retry(pdf_path)

                # Record transaction in database
                transaction_id = await self.record_processing_transaction(
                    pdf_path, ocr_result, organization_id
                )

                results.append((pdf_path, ocr_result, transaction_id))

            except Exception as e:
                logger.error(f"Failed to process {pdf_path}: {str(e)}")
                # Create error result
                error_result = OCRResult(
                    full_text="",
                    confidence_score=0.0,
                    processing_time=0.0,
                    page_count=0,
                    errors=[str(e)],
                )
                results.append((pdf_path, error_result, None))

        return results
