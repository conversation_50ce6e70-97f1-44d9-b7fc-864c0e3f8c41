"""PII detection module with Thai and English regex patterns.

This module provides comprehensive PII detection with error handling and recovery:
- Robust regex pattern compilation with fallback strategies
- Graceful degradation when pattern matching fails
- Validation of detection results with confidence scoring
- HIPAA-compliant logging (no sensitive data in logs)
- Fallback detection strategies for critical PII types
"""

import logging
import re
from dataclasses import dataclass
from enum import Enum
from functools import wraps
from typing import Any, List, Optional, Pattern, Tuple

import regex

from ..core.exceptions import PIIDetectionException, handle_exception

logger = logging.getLogger(__name__)


def safe_regex_compile(
    pattern: str, flags: int = 0, pattern_name: str = "unknown"
) -> Optional[Pattern]:
    """Safely compile regex pattern with error handling.

    Args:
        pattern: Regex pattern to compile
        flags: Regex flags
        pattern_name: Name of pattern for logging

    Returns:
        Compiled pattern or None if compilation fails
    """
    try:
        return re.compile(pattern, flags)
    except re.error as e:
        logger.warning(
            f"Failed to compile regex pattern '{pattern_name}': {str(e)}",
            extra={"pattern_name": pattern_name, "pattern_preview": pattern[:50]},
        )
        return None
    except Exception as e:
        logger.error(
            f"Unexpected error compiling regex pattern '{pattern_name}': {str(e)}",
            extra={"pattern_name": pattern_name},
        )
        return None


def safe_unicode_regex_compile(
    pattern: str, flags: int = 0, pattern_name: str = "unknown"
) -> Optional[Pattern]:
    """Safely compile Unicode regex pattern with error handling.

    Args:
        pattern: Unicode regex pattern to compile
        flags: Regex flags
        pattern_name: Name of pattern for logging

    Returns:
        Compiled pattern or None if compilation fails
    """
    try:
        return regex.compile(pattern, flags)
    except regex.error as e:
        logger.warning(
            f"Failed to compile Unicode regex pattern '{pattern_name}': {str(e)}",
            extra={"pattern_name": pattern_name, "pattern_preview": pattern[:50]},
        )
        return None
    except Exception as e:
        logger.error(
            f"Unexpected error compiling Unicode regex pattern '{pattern_name}': {str(e)}",
            extra={"pattern_name": pattern_name},
        )
        return None


def pii_detection_error_handler(func: Any) -> Any:
    """Decorator for handling PII detection errors gracefully.

    This decorator ensures that PII detection failures don't crash the entire pipeline.
    """

    @wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        try:
            return func(*args, **kwargs)
        except PIIDetectionException:
            # Re-raise PII-specific exceptions
            raise
        except Exception as e:
            # Convert unexpected exceptions to PII detection exceptions
            cf_exception = handle_exception(
                e,
                logger,
                context={"function": func.__name__, "args_count": len(args)},
                reraise=False,
            )

            # Return empty list for detection functions to allow graceful degradation
            if func.__name__.startswith("_detect_"):
                logger.warning(
                    f"PII detection function {func.__name__} failed, returning empty results",
                    extra={"error_code": cf_exception.error_code},
                )
                return []

            # For other functions, raise as PII detection exception
            raise PIIDetectionException(
                message=f"PII detection error in {func.__name__}: {cf_exception.message}",
                context={"original_error": cf_exception.to_dict()},
                original_exception=e,
            )

    return wrapper


class PIIType(Enum):
    """Types of personally identifiable information."""

    THAI_ID = "thai_id"
    PASSPORT = "passport"
    PHONE_NUMBER = "phone"
    EMAIL = "email"
    THAI_NAME = "thai_name"
    ENGLISH_NAME = "english_name"
    ADDRESS = "address"
    HOSPITAL_NUMBER = "hospital_number"
    LAB_NUMBER = "lab_number"
    DATE_OF_BIRTH = "date_of_birth"
    CREDIT_CARD = "credit_card"
    BANK_ACCOUNT = "bank_account"


@dataclass
class PIIMatch:
    """Represents a detected PII match with location information."""

    pii_type: PIIType
    text: str
    start_pos: int
    end_pos: int
    line_number: int
    confidence: float
    context: str  # Surrounding text for context
    coordinates: Optional[Tuple[float, float, float, float]] = None  # x1, y1, x2, y2


class PIIPatterns:
    """Comprehensive PII regex patterns for Thai and English text."""

    def __init__(self) -> None:
        """Initialize regex patterns for PII detection."""
        self._compile_patterns()

    def _compile_patterns(self) -> None:
        """Compile all regex patterns for efficient matching."""

        # Thai National ID patterns
        self.thai_id_patterns = [
            # Standard format: X-XXXX-XXXXX-XX-X
            re.compile(r"\b\d-\d{4}-\d{5}-\d{2}-\d\b"),
            # No dashes: 13 consecutive digits
            re.compile(r"\b\d{13}\b"),
            # Partial dashes: XXXXX-XXXXX-XX-X
            re.compile(r"\b\d{5}-\d{5}-\d{2}-\d\b"),
            # Space separated: X XXXX XXXXX XX X
            re.compile(r"\b\d \d{4} \d{5} \d{2} \d\b"),
        ]

        # Thai name patterns (using Unicode Thai script)
        self.thai_name_patterns = [
            # Full Thai name with title
            regex.compile(
                r"(?:นาย|นาง|นางสาว|ด\.ช\.|ด\.ญ\.)\s*[\u0E01-\u0E5B\s]{2,50}"
            ),
            # Thai name without title (2-4 Thai words)
            regex.compile(
                r"\b[\u0E01-\u0E5B]+\s+[\u0E01-\u0E5B]+(?:\s+[\u0E01-\u0E5B]+){0,2}\b"
            ),
            # Pattern with common Thai name components
            regex.compile(r"[\u0E01-\u0E5B]{2,}(?:\s+[\u0E01-\u0E5B]{2,})+"),
        ]

        # English name patterns
        self.english_name_patterns = [
            # Title + First + Last name
            re.compile(
                r"\b(?:Mr\.?|Mrs\.?|Ms\.?|Miss|Dr\.?|Prof\.?)\s+[A-Z][a-z]+\s+[A-Z][a-z]+\b",
                re.IGNORECASE,
            ),
            # First + Middle + Last name
            re.compile(r"\b[A-Z][a-z]{1,15}\s+[A-Z][a-z]{1,15}\s+[A-Z][a-z]{1,15}\b"),
            # First + Last name (capitalized)
            re.compile(r"\b[A-Z][a-z]{1,15}\s+[A-Z][a-z]{1,15}\b"),
        ]

        # Hospital number patterns
        self.hospital_number_patterns = [
            # HN: followed by numbers
            re.compile(
                r"\b(?:HN|H\.N\.?|Hospital\s+No\.?)\s*:?\s*(\d{4,12})\b", re.IGNORECASE
            ),
            # AN (Admission Number)
            re.compile(
                r"\b(?:AN|A\.N\.?|Admission\s+No\.?)\s*:?\s*(\d{4,12})\b", re.IGNORECASE
            ),
            # Patient ID patterns
            re.compile(
                r"\b(?:Patient\s+(?:ID|No\.?)|PID)\s*:?\s*([A-Z0-9]{4,12})\b",
                re.IGNORECASE,
            ),
        ]

        # Lab number patterns
        self.lab_number_patterns = [
            # LN: followed by alphanumeric
            re.compile(
                r"\b(?:LN|L\.N\.?|Lab\s+No\.?)\s*:?\s*([A-Z0-9]{4,15})\b", re.IGNORECASE
            ),
            # Specimen number
            re.compile(
                r"\b(?:Specimen\s+No\.?|Spec\.?\s+No\.?)\s*:?\s*([A-Z0-9]{4,15})\b",
                re.IGNORECASE,
            ),
            # Test number patterns
            re.compile(
                r"\b(?:Test\s+No\.?|TN)\s*:?\s*([A-Z0-9]{4,15})\b", re.IGNORECASE
            ),
        ]

        # Phone number patterns (Thai format)
        self.phone_patterns = [
            # Mobile: 0XX-XXX-XXXX or 0XXXXXXXXX
            re.compile(r"\b0[0-9]{1}-?[0-9]{3}-?[0-9]{4}\b"),
            # Landline: 0X-XXX-XXXX
            re.compile(r"\b0[0-9]-[0-9]{3}-[0-9]{4}\b"),
            # International: +66-X-XXX-XXXX
            re.compile(r"\+66-?[0-9]{1,2}-?[0-9]{3}-?[0-9]{4}\b"),
        ]

        # Email patterns
        self.email_patterns = [
            re.compile(r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"),
        ]

        # Date of birth patterns
        self.dob_patterns = [
            # DD/MM/YYYY or DD-MM-YYYY
            re.compile(
                r"\b(?:0?[1-9]|[12][0-9]|3[01])[\/\-](?:0?[1-9]|1[0-2])[\/\-](?:19|20)\d{2}\b"
            ),
            # Thai Buddhist calendar (BE)
            re.compile(
                r"\b(?:0?[1-9]|[12][0-9]|3[01])[\/\-](?:0?[1-9]|1[0-2])[\/\-](?:24|25)\d{2}\b"
            ),
            # MM/DD/YYYY format
            re.compile(
                r"\b(?:0?[1-9]|1[0-2])[\/\-](?:0?[1-9]|[12][0-9]|3[01])[\/\-](?:19|20)\d{2}\b"
            ),
        ]

        # Thai address patterns
        self.address_patterns = [
            # Thai address with common keywords
            regex.compile(
                r"(?:ที่อยู่|อยู่เลขที่|บ้านเลขที่)\s*[\u0E01-\u0E5B0-9\s\/\-,\.]{10,100}"
            ),
            # Address with district/province
            regex.compile(
                r"[\u0E01-\u0E5B0-9\s\/\-,\.]*(?:แขวง|เขต|อำเภอ|จังหวัด)[\u0E01-\u0E5B\s]{3,30}"
            ),
            # Postal code in address
            regex.compile(r"[\u0E01-\u0E5B0-9\s\/\-,\.]*\s+\d{5}\b"),
        ]

        logger.info("PII regex patterns compiled successfully")


class PIIDetector:
    """Advanced PII detector for Thai and English medical documents."""

    def __init__(self, confidence_threshold: float = 0.7) -> None:
        """Initialize PII detector with patterns.

        Args:
            confidence_threshold: Minimum confidence score for PII matches
        """
        self.patterns = PIIPatterns()
        self.confidence_threshold = confidence_threshold

        # Context window for extracting surrounding text
        self.context_window = 50

        logger.info(
            f"PII detector initialized with confidence threshold: {confidence_threshold}"
        )

    def detect_pii(
        self, text: str, include_coordinates: bool = False
    ) -> List[PIIMatch]:
        """Detect all PII in the given text.

        Args:
            text: Input text to analyze
            include_coordinates: Whether to include coordinate information

        Returns:
            List of PIIMatch objects for detected PII
        """
        matches = []
        lines = text.split("\n")

        # Process each line for PII detection
        for line_num, line in enumerate(lines, 1):
            line_matches = []

            # Detect different types of PII
            line_matches.extend(self._detect_thai_ids(line, line_num))
            line_matches.extend(self._detect_thai_names(line, line_num))
            line_matches.extend(self._detect_english_names(line, line_num))
            line_matches.extend(self._detect_hospital_numbers(line, line_num))
            line_matches.extend(self._detect_lab_numbers(line, line_num))
            line_matches.extend(self._detect_phone_numbers(line, line_num))
            line_matches.extend(self._detect_emails(line, line_num))
            line_matches.extend(self._detect_dates_of_birth(line, line_num))
            line_matches.extend(self._detect_addresses(line, line_num))

            # Add context to matches
            for match in line_matches:
                match.context = self._extract_context(
                    text, match.start_pos, match.end_pos
                )

            # Filter by confidence threshold
            filtered_matches = [
                m for m in line_matches if m.confidence >= self.confidence_threshold
            ]
            matches.extend(filtered_matches)

        # Remove overlapping matches (keep highest confidence)
        matches = self._remove_overlapping_matches(matches)

        logger.info(
            f"Detected {len(matches)} PII items above confidence threshold {self.confidence_threshold}"
        )

        return matches

    def _detect_thai_ids(self, text: str, line_num: int) -> List[PIIMatch]:
        """Detect Thai national ID numbers."""
        matches = []

        for pattern in self.patterns.thai_id_patterns:
            for match in pattern.finditer(text):
                # Validate Thai ID checksum if possible
                thai_id = re.sub(r"[^\d]", "", match.group())
                confidence = self._validate_thai_id_checksum(thai_id)

                if confidence > 0:
                    matches.append(
                        PIIMatch(
                            pii_type=PIIType.THAI_ID,
                            text=match.group(),
                            start_pos=match.start(),
                            end_pos=match.end(),
                            line_number=line_num,
                            confidence=confidence,
                            context="",
                        )
                    )

        return matches

    def _detect_thai_names(self, text: str, line_num: int) -> List[PIIMatch]:
        """Detect Thai names using Unicode patterns."""
        matches = []

        for pattern in self.patterns.thai_name_patterns:
            for match in pattern.finditer(text):
                # Calculate confidence based on length and structure
                name_text = match.group().strip()
                confidence = self._calculate_thai_name_confidence(name_text)

                if confidence > 0.3:  # Lower threshold for names
                    matches.append(
                        PIIMatch(
                            pii_type=PIIType.THAI_NAME,
                            text=name_text,
                            start_pos=match.start(),
                            end_pos=match.end(),
                            line_number=line_num,
                            confidence=confidence,
                            context="",
                        )
                    )

        return matches

    def _detect_english_names(self, text: str, line_num: int) -> List[PIIMatch]:
        """Detect English names."""
        matches = []

        for pattern in self.patterns.english_name_patterns:
            for match in pattern.finditer(text):
                name_text = match.group().strip()
                confidence = self._calculate_english_name_confidence(name_text)

                if confidence > 0.4:  # Lower threshold for names
                    matches.append(
                        PIIMatch(
                            pii_type=PIIType.ENGLISH_NAME,
                            text=name_text,
                            start_pos=match.start(),
                            end_pos=match.end(),
                            line_number=line_num,
                            confidence=confidence,
                            context="",
                        )
                    )

        return matches

    def _detect_hospital_numbers(self, text: str, line_num: int) -> List[PIIMatch]:
        """Detect hospital numbers."""
        matches = []

        for pattern in self.patterns.hospital_number_patterns:
            for match in pattern.finditer(text):
                confidence = 0.9  # High confidence for explicit patterns

                matches.append(
                    PIIMatch(
                        pii_type=PIIType.HOSPITAL_NUMBER,
                        text=match.group(),
                        start_pos=match.start(),
                        end_pos=match.end(),
                        line_number=line_num,
                        confidence=confidence,
                        context="",
                    )
                )

        return matches

    def _detect_lab_numbers(self, text: str, line_num: int) -> List[PIIMatch]:
        """Detect laboratory numbers."""
        matches = []

        for pattern in self.patterns.lab_number_patterns:
            for match in pattern.finditer(text):
                confidence = 0.9  # High confidence for explicit patterns

                matches.append(
                    PIIMatch(
                        pii_type=PIIType.LAB_NUMBER,
                        text=match.group(),
                        start_pos=match.start(),
                        end_pos=match.end(),
                        line_number=line_num,
                        confidence=confidence,
                        context="",
                    )
                )

        return matches

    def _detect_phone_numbers(self, text: str, line_num: int) -> List[PIIMatch]:
        """Detect phone numbers."""
        matches = []

        for pattern in self.patterns.phone_patterns:
            for match in pattern.finditer(text):
                confidence = self._validate_thai_phone_number(match.group())

                if confidence > 0:
                    matches.append(
                        PIIMatch(
                            pii_type=PIIType.PHONE_NUMBER,
                            text=match.group(),
                            start_pos=match.start(),
                            end_pos=match.end(),
                            line_number=line_num,
                            confidence=confidence,
                            context="",
                        )
                    )

        return matches

    def _detect_emails(self, text: str, line_num: int) -> List[PIIMatch]:
        """Detect email addresses."""
        matches = []

        for pattern in self.patterns.email_patterns:
            for match in pattern.finditer(text):
                confidence = 0.95  # High confidence for email patterns

                matches.append(
                    PIIMatch(
                        pii_type=PIIType.EMAIL,
                        text=match.group(),
                        start_pos=match.start(),
                        end_pos=match.end(),
                        line_number=line_num,
                        confidence=confidence,
                        context="",
                    )
                )

        return matches

    def _detect_dates_of_birth(self, text: str, line_num: int) -> List[PIIMatch]:
        """Detect dates of birth."""
        matches = []

        for pattern in self.patterns.dob_patterns:
            for match in pattern.finditer(text):
                # Check if this appears to be a birth date based on context
                confidence = self._validate_birth_date_context(text, match)

                if confidence > 0.3:
                    matches.append(
                        PIIMatch(
                            pii_type=PIIType.DATE_OF_BIRTH,
                            text=match.group(),
                            start_pos=match.start(),
                            end_pos=match.end(),
                            line_number=line_num,
                            confidence=confidence,
                            context="",
                        )
                    )

        return matches

    def _detect_addresses(self, text: str, line_num: int) -> List[PIIMatch]:
        """Detect Thai addresses."""
        matches = []

        for pattern in self.patterns.address_patterns:
            for match in pattern.finditer(text):
                address_text = match.group().strip()
                confidence = self._calculate_address_confidence(address_text)

                if confidence > 0.4:
                    matches.append(
                        PIIMatch(
                            pii_type=PIIType.ADDRESS,
                            text=address_text,
                            start_pos=match.start(),
                            end_pos=match.end(),
                            line_number=line_num,
                            confidence=confidence,
                            context="",
                        )
                    )

        return matches

    def _validate_thai_id_checksum(self, thai_id: str) -> float:
        """Validate Thai ID checksum algorithm.

        Args:
            thai_id: 13-digit Thai ID string

        Returns:
            Confidence score (0.0-1.0)
        """
        if len(thai_id) != 13 or not thai_id.isdigit():
            return 0.0

        try:
            # Thai ID checksum algorithm
            digits = [int(d) for d in thai_id[:12]]
            check_digit = int(thai_id[12])

            sum_digits = sum((13 - i) * digit for i, digit in enumerate(digits))
            calculated_check = (11 - (sum_digits % 11)) % 10

            return 0.95 if calculated_check == check_digit else 0.3

        except (ValueError, IndexError):
            return 0.0

    def _calculate_thai_name_confidence(self, name: str) -> float:
        """Calculate confidence for Thai name detection."""
        if len(name) < 4:
            return 0.0

        # Check for common Thai name components
        common_titles = ["นาย", "นาง", "นางสาว", "ด.ช.", "ด.ญ."]

        confidence = 0.5  # Base confidence

        # Boost confidence for titles
        for title in common_titles:
            if name.startswith(title):
                confidence += 0.3
                break

        # Check word count (typical Thai names have 2-4 words)
        words = name.split()
        if 2 <= len(words) <= 4:
            confidence += 0.2

        return min(confidence, 1.0)

    def _calculate_english_name_confidence(self, name: str) -> float:
        """Calculate confidence for English name detection."""
        words = name.split()

        # Check for proper capitalization
        if not all(
            word[0].isupper() and word[1:].islower() for word in words if word.isalpha()
        ):
            return 0.3

        # Common English titles boost confidence
        titles = ["Mr.", "Mrs.", "Ms.", "Miss", "Dr.", "Prof."]
        if any(name.startswith(title) for title in titles):
            return 0.9

        # Length-based confidence
        if 2 <= len(words) <= 3:
            return 0.7

        return 0.5

    def _validate_thai_phone_number(self, phone: str) -> float:
        """Validate Thai phone number format."""
        digits_only = re.sub(r"[^\d]", "", phone)

        # Thai mobile numbers start with 06, 08, 09
        if len(digits_only) == 10 and digits_only.startswith(("06", "08", "09")):
            return 0.9

        # Thai landline numbers (various formats)
        if len(digits_only) == 9 and not digits_only.startswith("0"):
            return 0.7

        return 0.0

    def _validate_birth_date_context(self, text: str, match) -> float:
        """Validate if a date appears to be a birth date based on context."""
        context_before = text[max(0, match.start() - 20) : match.start()].lower()
        context_after = text[match.end() : match.end() + 20].lower()

        birth_keywords = ["birth", "born", "dob", "date of birth", "เกิด", "วันเกิด"]

        for keyword in birth_keywords:
            if keyword in context_before or keyword in context_after:
                return 0.8

        return 0.3  # Base confidence for date patterns

    def _calculate_address_confidence(self, address: str) -> float:
        """Calculate confidence for address detection."""
        # Thai address keywords
        address_keywords = ["ที่อยู่", "บ้านเลขที่", "แขวง", "เขต", "อำเภอ", "จังหวัด", "ตำบล"]

        confidence = 0.3  # Base confidence

        for keyword in address_keywords:
            if keyword in address:
                confidence += 0.2

        # Postal code pattern boosts confidence
        if re.search(r"\b\d{5}\b", address):
            confidence += 0.3

        return min(confidence, 1.0)

    def _extract_context(self, full_text: str, start_pos: int, end_pos: int) -> str:
        """Extract context around a PII match."""
        context_start = max(0, start_pos - self.context_window)
        context_end = min(len(full_text), end_pos + self.context_window)

        return full_text[context_start:context_end].strip()

    def _remove_overlapping_matches(self, matches: List[PIIMatch]) -> List[PIIMatch]:
        """Remove overlapping matches, keeping the one with highest confidence."""
        if not matches:
            return matches

        # Sort by start position
        sorted_matches = sorted(matches, key=lambda x: x.start_pos)

        filtered_matches = []
        last_end = -1

        for match in sorted_matches:
            # If this match doesn't overlap with the previous one, keep it
            if match.start_pos >= last_end:
                filtered_matches.append(match)
                last_end = match.end_pos
            else:
                # Overlapping match - keep the one with higher confidence
                if (
                    filtered_matches
                    and match.confidence > filtered_matches[-1].confidence
                ):
                    filtered_matches[-1] = match
                    last_end = match.end_pos

        return filtered_matches
