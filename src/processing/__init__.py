"""Processing modules for ChromoForge OCR Pipeline.

This package contains the processing components including:
- OCR processing using Gemini API
- Batch processing capabilities
- PDF obfuscation for privacy
- PII detection algorithms
"""

# Import on demand to avoid circular dependencies
__all__ = [
    "GeminiOCRProcessor",
    "BatchProcessor",
    "ProcessingStatus",
    "PDFObfuscator",
    "PIIDetector",
    "PIIMatch",
]
