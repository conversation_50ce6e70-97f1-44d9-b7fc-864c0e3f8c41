"""Core modules for ChromoForge OCR Pipeline.

This package contains the core components including:
- Configuration management
- Application constants
- Custom exceptions
- Data models
"""

# Import core modules (config imported on demand to avoid validation issues)
from .constants import (APP_DESCRIPTION, APP_NAME, APP_VERSION,
                        AUDIT_BUFFER_SIZE, AUDIT_FLUSH_INTERVAL,
                        CHECKSUM_CHUNK_SIZE, CIRCUIT_BREAKER_FAILURE_THRESHOLD,
                        CIRCUIT_BREAKER_TIMEOUT, CONTEXT_WINDOW_SIZE,
                        COORDINATE_MATCHING_TOLERANCE, DATE_CONTEXT_WINDOW,
                        DEFAULT_ENVIRONMENT, DEFAULT_FILE_PATTERN,
                        DEFAULT_MIME_TYPE, ESTIMATED_SECONDS_PER_FILE,
                        EXIT_CODES, FALLBACK_PII_CONFIDENCE_THRESHOLD,
                        FIELD_LENGTHS, LOG_FILE_BACKUP_COUNT,
                        LOG_FILE_MAX_BYTES, MAX_FONT_SIZE,
                        MAX_LOG_FILE_LIST_SIZE, MAX_LOG_RESPONSE_LENGTH,
                        MAX_RETRY_DELAY, MIN_API_KEY_LENGTH, MIN_FONT_SIZE,
                        OCR_CIRCUIT_BREAKER_FAILURE_THRESHOLD,
                        OCR_CIRCUIT_BREAKER_TIMEOUT,
                        OCR_RATE_LIMITER_MAX_CALLS, PATTERN_PREVIEW_LENGTH,
                        PDF_OBFUSCATOR_PADDING, PII_CONTEXT_WINDOW_SIZE,
                        RATE_LIMITER_MAX_CALLS, RATE_LIMITER_TIME_WINDOW,
                        RETRYABLE_STATUS_CODES, SYSTEM_NAME,
                        TEXT_PREVIEW_LENGTH)
from .exceptions import (AuditLoggingException, BatchProcessingException,
                         BatchResourceException, BatchValidationException,
                         ChromoForgeBaseException, ConfigurationException,
                         EncryptionException, ErrorSeverity,
                         FileAccessException, FileSystemException,
                         FileValidationException, OCRAPIException,
                         OCRConfigurationException, OCRParsingException,
                         OCRProcessingException, OCRQuotaExceededException,
                         PDFCoordinateException, PDFCorruptedException,
                         PDFObfuscationException, PDFParsingException,
                         PDFProcessingException, PIIDetectionException,
                         PIIPatternException, PIIValidationException,
                         SecurityException, get_error_response,
                         handle_exception)
from .models import OCRResult

__all__ = [
    # Models
    "OCRResult",
    # Constants
    "APP_VERSION",
    "APP_NAME",
    "APP_DESCRIPTION",
    "SYSTEM_NAME",
    "DEFAULT_ENVIRONMENT",
    "DEFAULT_FILE_PATTERN",
    "EXIT_CODES",
    "CIRCUIT_BREAKER_FAILURE_THRESHOLD",
    "CIRCUIT_BREAKER_TIMEOUT",
    "OCR_CIRCUIT_BREAKER_FAILURE_THRESHOLD",
    "OCR_CIRCUIT_BREAKER_TIMEOUT",
    "RATE_LIMITER_MAX_CALLS",
    "RATE_LIMITER_TIME_WINDOW",
    "OCR_RATE_LIMITER_MAX_CALLS",
    "MAX_RETRY_DELAY",
    "AUDIT_BUFFER_SIZE",
    "AUDIT_FLUSH_INTERVAL",
    "LOG_FILE_MAX_BYTES",
    "LOG_FILE_BACKUP_COUNT",
    "ESTIMATED_SECONDS_PER_FILE",
    "FALLBACK_PII_CONFIDENCE_THRESHOLD",
    "PII_CONTEXT_WINDOW_SIZE",
    "PATTERN_PREVIEW_LENGTH",
    "DATE_CONTEXT_WINDOW",
    "MIN_API_KEY_LENGTH",
    "RETRYABLE_STATUS_CODES",
    "MAX_LOG_RESPONSE_LENGTH",
    "MAX_LOG_FILE_LIST_SIZE",
    "FIELD_LENGTHS",
    "TEXT_PREVIEW_LENGTH",
    "CONTEXT_WINDOW_SIZE",
    "PDF_OBFUSCATOR_PADDING",
    "MIN_FONT_SIZE",
    "MAX_FONT_SIZE",
    "COORDINATE_MATCHING_TOLERANCE",
    "DEFAULT_MIME_TYPE",
    "CHECKSUM_CHUNK_SIZE",
    # Exceptions
    "ErrorSeverity",
    "ChromoForgeBaseException",
    "OCRProcessingException",
    "OCRConfigurationException",
    "OCRAPIException",
    "OCRQuotaExceededException",
    "OCRParsingException",
    "PIIDetectionException",
    "PIIPatternException",
    "PIIValidationException",
    "PDFProcessingException",
    "PDFParsingException",
    "PDFCorruptedException",
    "PDFObfuscationException",
    "PDFCoordinateException",
    "FileSystemException",
    "FileAccessException",
    "FileValidationException",
    "SecurityException",
    "EncryptionException",
    "AuditLoggingException",
    "BatchProcessingException",
    "BatchValidationException",
    "BatchResourceException",
    "ConfigurationException",
    "handle_exception",
    "get_error_response",
]
