"""Custom exceptions for ChromoForge OCR Pipeline.

This module defines a comprehensive set of custom exceptions for the ChromoForge OCR
Pipeline, providing specific error types for different failure scenarios to enable
better error handling and debugging.
"""

import traceback
from enum import Enum
from typing import Any, Dict, List, Optional


class ErrorSeverity(Enum):
    """Severity levels for exceptions."""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ChromoForgeBaseException(Exception):
    """Base exception for all ChromoForge OCR Pipeline exceptions.

    This base class provides common functionality for all custom exceptions,
    including error categorization, context information, and structured logging.

    Attributes:
        message: Human-readable error message
        error_code: Unique error code for categorization
        severity: Error severity level
        context: Additional context information
        retry_able: Whether the operation can be retried
        user_message: User-friendly error message
    """

    def __init__(
        self,
        message: str,
        error_code: str,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None,
        retry_able: bool = False,
        user_message: Optional[str] = None,
        original_exception: Optional[Exception] = None,
    ) -> None:
        """Initialize ChromoForge base exception.

        Args:
            message: Detailed error message for developers
            error_code: Unique error code (e.g., 'OCR_001')
            severity: Severity level of the error
            context: Additional context information
            retry_able: Whether the operation can be retried
            user_message: User-friendly error message
            original_exception: Original exception that caused this error
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.severity = severity
        self.context = context or {}
        self.retry_able = retry_able
        self.user_message = user_message or message
        self.original_exception = original_exception
        self.stack_trace = traceback.format_exc()

    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for logging and serialization.

        Returns:
            Dictionary representation of the exception
        """
        return {
            "error_type": self.__class__.__name__,
            "error_code": self.error_code,
            "message": self.message,
            "user_message": self.user_message,
            "severity": self.severity.value,
            "retry_able": self.retry_able,
            "context": self.context,
            "original_exception": (
                str(self.original_exception) if self.original_exception else None
            ),
            "stack_trace": self.stack_trace,
        }


# OCR Processing Exceptions
class OCRProcessingException(ChromoForgeBaseException):
    """Base exception for OCR processing errors."""

    pass


class OCRConfigurationException(OCRProcessingException):
    """Exception raised for OCR configuration errors."""

    def __init__(
        self, message: str, config_key: Optional[str] = None, **kwargs
    ) -> None:
        context = kwargs.pop("context", {})
        if config_key:
            context["config_key"] = config_key

        super().__init__(
            message=message,
            error_code="OCR_CONFIG_001",
            severity=ErrorSeverity.HIGH,
            context=context,
            retry_able=False,
            user_message="OCR service configuration error. Please check your settings.",
            **kwargs,
        )


class OCRAPIException(OCRProcessingException):
    """Exception raised for Gemini API errors."""

    def __init__(
        self,
        message: str,
        api_response: Optional[Dict[str, Any]] = None,
        status_code: Optional[int] = None,
        **kwargs,
    ) -> None:
        context = kwargs.pop("context", {})
        if api_response:
            context["api_response"] = api_response
        if status_code:
            context["status_code"] = status_code

        # Determine if retry is possible based on status code
        retry_able = status_code in [429, 500, 502, 503, 504] if status_code else True

        super().__init__(
            message=message,
            error_code="OCR_API_001",
            severity=ErrorSeverity.HIGH,
            context=context,
            retry_able=retry_able,
            user_message="OCR service temporarily unavailable. Please try again later.",
            **kwargs,
        )


class OCRQuotaExceededException(OCRAPIException):
    """Exception raised when API quota is exceeded."""

    def __init__(self, quota_type: str = "requests", **kwargs) -> None:
        context = kwargs.get("context", {})
        context["quota_type"] = quota_type

        super().__init__(
            message=f"OCR API {quota_type} quota exceeded",
            error_code="OCR_QUOTA_001",
            severity=ErrorSeverity.CRITICAL,
            context=context,
            retry_able=True,
            user_message="Daily processing limit reached. Please try again tomorrow or upgrade your plan.",
            **kwargs,
        )


class OCRParsingException(OCRProcessingException):
    """Exception raised when OCR response parsing fails."""

    def __init__(
        self, message: str, raw_response: Optional[str] = None, **kwargs
    ) -> None:
        context = kwargs.get("context", {})
        if raw_response:
            context["raw_response"] = raw_response[:1000]  # Truncate for logging

        super().__init__(
            message=message,
            error_code="OCR_PARSE_001",
            severity=ErrorSeverity.MEDIUM,
            context=context,
            retry_able=True,
            user_message="Error processing document content. The document may be corrupted or in an unsupported format.",
            **kwargs,
        )


# PII Detection Exceptions
class PIIDetectionException(ChromoForgeBaseException):
    """Base exception for PII detection errors."""

    pass


class PIIPatternException(PIIDetectionException):
    """Exception raised for PII pattern compilation or matching errors."""

    def __init__(
        self,
        message: str,
        pattern: Optional[str] = None,
        pii_type: Optional[str] = None,
        **kwargs,
    ) -> None:
        context = kwargs.get("context", {})
        if pattern:
            context["pattern"] = pattern
        if pii_type:
            context["pii_type"] = pii_type

        super().__init__(
            message=message,
            error_code="PII_PATTERN_001",
            severity=ErrorSeverity.MEDIUM,
            context=context,
            retry_able=False,
            user_message="Error in privacy detection system. Some sensitive information may not be properly identified.",
            **kwargs,
        )


class PIIValidationException(PIIDetectionException):
    """Exception raised for PII validation errors."""

    def __init__(
        self,
        message: str,
        pii_value: Optional[str] = None,
        validation_type: Optional[str] = None,
        **kwargs,
    ) -> None:
        context = kwargs.get("context", {})
        if pii_value:
            context["pii_value"] = "***REDACTED***"  # Never log actual PII
        if validation_type:
            context["validation_type"] = validation_type

        super().__init__(
            message=message,
            error_code="PII_VALIDATION_001",
            severity=ErrorSeverity.LOW,
            context=context,
            retry_able=False,
            user_message="Error validating detected sensitive information.",
            **kwargs,
        )


# PDF Processing Exceptions
class PDFProcessingException(ChromoForgeBaseException):
    """Base exception for PDF processing errors."""

    pass


class PDFParsingException(PDFProcessingException):
    """Exception raised when PDF parsing fails."""

    def __init__(
        self,
        message: str,
        file_path: Optional[str] = None,
        page_number: Optional[int] = None,
        **kwargs,
    ) -> None:
        context = kwargs.get("context", {})
        if file_path:
            context["file_path"] = file_path
        if page_number:
            context["page_number"] = page_number

        super().__init__(
            message=message,
            error_code="PDF_PARSE_001",
            severity=ErrorSeverity.HIGH,
            context=context,
            retry_able=False,
            user_message="Error reading PDF file. The file may be corrupted or password-protected.",
            **kwargs,
        )


class PDFCorruptedException(PDFProcessingException):
    """Exception raised when PDF file is corrupted or invalid."""

    def __init__(self, message: str, file_path: Optional[str] = None, **kwargs) -> None:
        context = kwargs.get("context", {})
        if file_path:
            context["file_path"] = file_path

        super().__init__(
            message=message,
            error_code="PDF_CORRUPT_001",
            severity=ErrorSeverity.HIGH,
            context=context,
            retry_able=False,
            user_message="The PDF file appears to be corrupted or damaged. Please try with a different file.",
            **kwargs,
        )


class PDFObfuscationException(PDFProcessingException):
    """Exception raised during PDF obfuscation process."""

    def __init__(
        self,
        message: str,
        file_path: Optional[str] = None,
        obfuscation_method: Optional[str] = None,
        **kwargs,
    ) -> None:
        context = kwargs.get("context", {})
        if file_path:
            context["file_path"] = file_path
        if obfuscation_method:
            context["obfuscation_method"] = obfuscation_method

        super().__init__(
            message=message,
            error_code="PDF_OBFUSC_001",
            severity=ErrorSeverity.HIGH,
            context=context,
            retry_able=True,
            user_message="Error processing document for privacy protection. Please try again.",
            **kwargs,
        )


class PDFCoordinateException(PDFProcessingException):
    """Exception raised when PDF coordinate mapping fails."""

    def __init__(
        self,
        message: str,
        pii_text: Optional[str] = None,
        coordinate_count: Optional[int] = None,
        **kwargs,
    ) -> None:
        context = kwargs.get("context", {})
        if pii_text:
            context["pii_text"] = "***REDACTED***"  # Never log actual PII
        if coordinate_count:
            context["coordinate_count"] = coordinate_count

        super().__init__(
            message=message,
            error_code="PDF_COORD_001",
            severity=ErrorSeverity.MEDIUM,
            context=context,
            retry_able=True,
            user_message="Error locating sensitive information in document for privacy protection.",
            **kwargs,
        )


# File System Exceptions
class FileSystemException(ChromoForgeBaseException):
    """Base exception for file system operations."""

    pass


class FileAccessException(FileSystemException):
    """Exception raised for file access errors."""

    def __init__(
        self,
        message: str,
        file_path: Optional[str] = None,
        operation: Optional[str] = None,
        **kwargs,
    ) -> None:
        context = kwargs.get("context", {})
        if file_path:
            context["file_path"] = file_path
        if operation:
            context["operation"] = operation

        super().__init__(
            message=message,
            error_code="FILE_ACCESS_001",
            severity=ErrorSeverity.HIGH,
            context=context,
            retry_able=True,
            user_message="Error accessing file. Please check file permissions and try again.",
            **kwargs,
        )


class FileValidationException(FileSystemException):
    """Exception raised for file validation errors."""

    def __init__(
        self,
        message: str,
        file_path: Optional[str] = None,
        validation_rule: Optional[str] = None,
        **kwargs,
    ) -> None:
        context = kwargs.get("context", {})
        if file_path:
            context["file_path"] = file_path
        if validation_rule:
            context["validation_rule"] = validation_rule

        super().__init__(
            message=message,
            error_code="FILE_VALID_001",
            severity=ErrorSeverity.MEDIUM,
            context=context,
            retry_able=False,
            user_message="File does not meet processing requirements. Please check file format and size.",
            **kwargs,
        )


# Security and Encryption Exceptions
class SecurityException(ChromoForgeBaseException):
    """Base exception for security-related errors."""

    pass


class EncryptionException(SecurityException):
    """Exception raised for encryption/decryption errors."""

    def __init__(self, message: str, operation: Optional[str] = None, **kwargs) -> None:
        context = kwargs.get("context", {})
        if operation:
            context["operation"] = operation

        super().__init__(
            message=message,
            error_code="SECURITY_ENC_001",
            severity=ErrorSeverity.CRITICAL,
            context=context,
            retry_able=False,
            user_message="Security error occurred during data processing. Please contact support.",
            **kwargs,
        )


class AuditLoggingException(SecurityException):
    """Exception raised for audit logging errors."""

    def __init__(
        self, message: str, audit_event: Optional[str] = None, **kwargs
    ) -> None:
        context = kwargs.get("context", {})
        if audit_event:
            context["audit_event"] = audit_event

        super().__init__(
            message=message,
            error_code="SECURITY_AUDIT_001",
            severity=ErrorSeverity.HIGH,
            context=context,
            retry_able=True,
            user_message="Error in security logging system. Processing may continue but audit trail may be incomplete.",
            **kwargs,
        )


# Batch Processing Exceptions
class BatchProcessingException(ChromoForgeBaseException):
    """Base exception for batch processing errors."""

    pass


class BatchValidationException(BatchProcessingException):
    """Exception raised for batch validation errors."""

    def __init__(
        self, message: str, invalid_files: Optional[List[str]] = None, **kwargs
    ) -> None:
        context = kwargs.get("context", {})
        if invalid_files:
            context["invalid_file_count"] = len(invalid_files)
            context["invalid_files"] = invalid_files[:10]  # Limit for logging

        super().__init__(
            message=message,
            error_code="BATCH_VALID_001",
            severity=ErrorSeverity.MEDIUM,
            context=context,
            retry_able=False,
            user_message="Some files in the batch cannot be processed. Please check file formats and requirements.",
            **kwargs,
        )


class BatchResourceException(BatchProcessingException):
    """Exception raised for batch resource exhaustion."""

    def __init__(
        self,
        message: str,
        resource_type: Optional[str] = None,
        current_usage: Optional[float] = None,
        **kwargs,
    ) -> None:
        context = kwargs.get("context", {})
        if resource_type:
            context["resource_type"] = resource_type
        if current_usage:
            context["current_usage"] = current_usage

        super().__init__(
            message=message,
            error_code="BATCH_RESOURCE_001",
            severity=ErrorSeverity.HIGH,
            context=context,
            retry_able=True,
            user_message="System resources are temporarily exhausted. Please try with a smaller batch size.",
            **kwargs,
        )


# Configuration Exceptions
class ConfigurationException(ChromoForgeBaseException):
    """Exception raised for configuration errors."""

    def __init__(
        self,
        message: str,
        config_section: Optional[str] = None,
        missing_keys: Optional[List[str]] = None,
        **kwargs,
    ) -> None:
        context = kwargs.get("context", {})
        if config_section:
            context["config_section"] = config_section
        if missing_keys:
            context["missing_keys"] = missing_keys

        super().__init__(
            message=message,
            error_code="CONFIG_001",
            severity=ErrorSeverity.CRITICAL,
            context=context,
            retry_able=False,
            user_message="System configuration error. Please contact support.",
            **kwargs,
        )


# Exception utilities
def handle_exception(
    exception: Exception,
    logger,
    context: Optional[Dict[str, Any]] = None,
    reraise: bool = True,
) -> Optional[ChromoForgeBaseException]:
    """Handle and log exceptions consistently.

    Args:
        exception: The exception to handle
        logger: Logger instance to use
        context: Additional context information
        reraise: Whether to reraise the exception

    Returns:
        ChromoForgeBaseException if not reraised, None otherwise

    Raises:
        ChromoForgeBaseException: If reraise is True
    """
    if isinstance(exception, ChromoForgeBaseException):
        # Already a ChromoForge exception, just log it
        logger.error(
            f"ChromoForge exception: {exception.error_code} - {exception.message}",
            extra={"exception_data": exception.to_dict()},
        )
        if reraise:
            raise exception
        return exception

    # Convert generic exception to ChromoForge exception
    cf_exception = ChromoForgeBaseException(
        message=str(exception),
        error_code="GENERIC_001",
        severity=ErrorSeverity.MEDIUM,
        context=context,
        retry_able=True,
        original_exception=exception,
    )

    logger.error(
        f"Unhandled exception converted: {cf_exception.error_code} - {cf_exception.message}",
        extra={"exception_data": cf_exception.to_dict()},
    )

    if reraise:
        raise cf_exception
    return cf_exception


def get_error_response(exception: ChromoForgeBaseException) -> Dict[str, Any]:
    """Get standardized error response for API endpoints.

    Args:
        exception: ChromoForge exception instance

    Returns:
        Standardized error response dictionary
    """
    return {
        "success": False,
        "error": {
            "code": exception.error_code,
            "message": exception.user_message,
            "severity": exception.severity.value,
            "retry_able": exception.retry_able,
            "details": (
                exception.message
                if exception.severity in [ErrorSeverity.LOW, ErrorSeverity.MEDIUM]
                else None
            ),
        },
        "context": {
            key: value
            for key, value in exception.context.items()
            if key
            not in [
                "raw_response",
                "stack_trace",
            ]  # Exclude verbose data from API responses
        },
    }
