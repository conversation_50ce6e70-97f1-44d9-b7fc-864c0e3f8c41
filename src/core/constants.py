"""Centralized constants for ChromoForge OCR Pipeline."""

from pathlib import Path


# Version Information - Read from VERSION file
def _get_version() -> str:
    """Read version from VERSION file in project root."""
    try:
        version_file = Path(__file__).parent.parent.parent / "VERSION"
        if version_file.exists():
            return version_file.read_text().strip()
        else:
            return "1.0.0"  # Fallback version
    except Exception:
        return "1.0.0"  # Fallback version


APP_VERSION = _get_version()
APP_NAME = "ChromoForge OCR Pipeline"
APP_DESCRIPTION = "Enhanced Thai Medical Document OCR with PII Detection"

# System Configuration
SYSTEM_NAME = "chromoforge_ocr_pipeline"
DEFAULT_ENVIRONMENT = "production"
DEFAULT_FILE_PATTERN = "*.pdf"

# Exit Codes
EXIT_CODES = {"SUCCESS": 0, "GENERAL_ERROR": 1, "KEYBOARD_INTERRUPT": 130}

# Circuit Breaker Settings
CIRCUIT_BREAKER_FAILURE_THRESHOLD = 5
CIRCUIT_BREAKER_TIMEOUT = 60
OCR_CIRCUIT_BREAKER_FAILURE_THRESHOLD = 3
OCR_CIRCUIT_BREAKER_TIMEOUT = 300

# Rate Limiter Settings
RATE_LIMITER_MAX_CALLS = 60
RATE_LIMITER_TIME_WINDOW = 60
OCR_RATE_LIMITER_MAX_CALLS = 50

# Retry Configuration
MAX_RETRY_DELAY = 60.0

# Audit Logger Settings
AUDIT_BUFFER_SIZE = 100
AUDIT_FLUSH_INTERVAL = 30

# Logging Configuration
LOG_FILE_MAX_BYTES = 10485760  # 10MB
LOG_FILE_BACKUP_COUNT = 10

# Processing Estimates
ESTIMATED_SECONDS_PER_FILE = 30

# PII Detection
FALLBACK_PII_CONFIDENCE_THRESHOLD = 0.5
PII_CONTEXT_WINDOW_SIZE = 50
PATTERN_PREVIEW_LENGTH = 50
DATE_CONTEXT_WINDOW = 20

# API Validation
MIN_API_KEY_LENGTH = 10

# Error Handling
RETRYABLE_STATUS_CODES = [429, 500, 502, 503, 504]
MAX_LOG_RESPONSE_LENGTH = 1000
MAX_LOG_FILE_LIST_SIZE = 10

# Model Field Lengths
FIELD_LENGTHS = {
    "patient_code": 50,
    "sample_code": 10,
    "investigation": 100,
    "patient_name": 200,
    "contact_no": 20,
    "email": 100,
    "md_code": 50,
    "national_id": 20,
    "date": 20,
    "healthcare_center": 200,
    "referring_physician": 200,
}

# Text Processing
TEXT_PREVIEW_LENGTH = 50
CONTEXT_WINDOW_SIZE = 20

# PDF Processing
PDF_OBFUSCATOR_PADDING = 2.0
MIN_FONT_SIZE = 6.0
MAX_FONT_SIZE = 72.0
COORDINATE_MATCHING_TOLERANCE = 0.8

# Database Fields
DEFAULT_MIME_TYPE = "application/pdf"
CHECKSUM_CHUNK_SIZE = 4096

# Model Configuration
DEFAULT_MODEL_NAME = "gemini-2.5-pro"
DEFAULT_MODEL_VERSION = "2.5-pro"
THINKING_BUDGET_UNLIMITED = -1
