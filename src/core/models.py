"""Data models for ChromoForge OCR pipeline."""

from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator


class ConfidenceLevel(str, Enum):
    """Confidence levels for extracted field values."""

    LOW = "Low"
    MEDIUM = "Medium"
    HIGH = "High"


class ExtractedField(BaseModel):
    """Enhanced field extraction with confidence, reasoning, and alternatives."""

    value: Optional[str] = Field(
        None, description="The extracted information. '[Blank]' if not found."
    )
    confidence: ConfidenceLevel = Field(
        description="Confidence level in the accuracy of the value"
    )
    reasoning: str = Field(
        description="Brief justification for confidence score and extraction method"
    )
    alternative_readings: List[str] = Field(
        default_factory=list,
        description="Alternative interpretations if text is ambiguous",
    )


class OCRResult(BaseModel):
    """OCR processing result with 14-field medical document extraction.

    This class implements the comprehensive medical document processing pipeline
    with detailed field extraction including confidence, reasoning, and alternatives
    for each field as specified in the ChromoForge requirements.
    """

    # 14 Required Medical Document Fields with Enhanced Metadata
    patient_code: ExtractedField = Field(
        description="Patient identifier beginning with 'TT' (e.g., TT04035)"
    )
    sample_code: ExtractedField = Field(
        description="Random mix of 6 characters (English letters and digits)"
    )
    investigation: ExtractedField = Field(
        description="Test names (K-TRACK, SPOT-MAS, K4CARE, K-TRACK MET, etc.)"
    )
    patient_name_th: ExtractedField = Field(description="Patient's full name in Thai")
    patient_name_en: ExtractedField = Field(
        description="Patient's full name in English"
    )
    gender: ExtractedField = Field(description="Patient's gender (Male/Female/Other)")
    dob_gregorian: ExtractedField = Field(
        description="Date of birth in YYYY-MM-DD format (Gregorian calendar)"
    )
    dob_buddhist_era: ExtractedField = Field(
        description="Date of birth in DD/MM/YYYY format (Buddhist Era)"
    )
    patient_contact_no: ExtractedField = Field(
        description="Patient's contact number/phone number"
    )
    place_of_treatment: ExtractedField = Field(
        description="Healthcare center/hospital name"
    )
    referring_physician_th: ExtractedField = Field(
        description="Referring physician name in Thai"
    )
    referring_physician_en: ExtractedField = Field(
        description="Referring physician name in English"
    )
    referring_physician_md_code: ExtractedField = Field(
        description="Medical license/registration code of referring physician"
    )
    referring_physician_email: ExtractedField = Field(
        description="Referring physician email address"
    )

    # Fields flagged for manual review
    fields_for_manual_review: List[str] = Field(
        default_factory=list,
        description="List of field names with Low or Medium confidence requiring manual review",
    )

    # Processing metadata
    full_text: str = Field(
        description="Complete extracted text from document", min_length=0
    )
    overall_confidence_score: float = Field(
        description="Overall confidence score (0-1)", ge=0.0, le=1.0
    )
    processing_time: float = Field(description="Processing time in seconds", ge=0.0)
    page_count: int = Field(description="Number of pages processed", ge=0)
    detected_languages: List[str] = Field(
        default_factory=list, description="Detected languages in document"
    )
    errors: List[str] = Field(
        default_factory=list, description="Processing errors encountered"
    )
    warnings: List[str] = Field(default_factory=list, description="Processing warnings")
    document_id: Optional[str] = Field(
        None, description="Unique identifier for the processed document"
    )
    timestamp: Optional[str] = Field(
        None, description="ISO timestamp when processing was completed"
    )


class OCRResult(BaseModel):
    """OCR processing result with extracted medical record data using generic schema.

    This class represents the complete result of OCR processing for medical records,
    supporting multiple file patterns and formats with a standardized 13-field schema.
    All fields are nullable to support partial extractions from various document types.

    Schema Fields (13 required fields):
        patient_code: Patient identifier beginning with 'TT' somewhere
        sample_code: Random mix of 6 characters (English letters and digits)
        investigation: Test names (K-TRACK, SPOT-MAS, K4CARE, K-TRACK MET, etc.)
        patient_name_th: Patient's full name in Thai
        patient_name_en: Patient's full name in English
        dob_gregorian: Date of birth in YYYY-MM-DD format (Gregorian)
        dob_buddhist_era: Date of birth in DD/MM/YYYY format (Buddhist Era, +543)
        patient_contact_no: Patient's contact number
        place_of_treatment: Any healthcare center
        referring_physician_th: Referring physician name in Thai
        referring_physician_en: Referring physician name in English
        referring_physician_md_code: Referring physician MD code
        referring_physician_email: Array of email addresses for multiple emails

    Processing Metadata:
        full_text: Complete extracted text from document
        confidence_score: Overall confidence score for extraction (0-1)
        processing_time: Time taken for processing in seconds
        page_count: Number of pages in processed document
        detected_languages: List of languages detected in document
        errors: List of processing errors encountered
        warnings: List of processing warnings
        document_id: Unique identifier for processed document
        timestamp: ISO timestamp when processing was completed
    """

    # Extracted text content
    full_text: str = Field(
        description="Complete extracted text from document", min_length=0
    )

    # Generic Medical Record Schema - 13 Required Fields (All Nullable)
    patient_code: Optional[str] = Field(
        None,
        description="Patient identifier beginning with 'TT' somewhere (e.g., TT04035)",
        max_length=50,
    )

    sample_code: Optional[str] = Field(
        None,
        description="Random mix of 6 characters (English letters and digits)",
        max_length=10,
    )

    investigation: Optional[str] = Field(
        None,
        description="Test names (K-TRACK, SPOT-MAS, K4CARE, K-TRACK MET, etc.)",
        max_length=100,
    )

    patient_name_th: Optional[str] = Field(
        None, description="Patient's full name in Thai", max_length=200
    )

    patient_name_en: Optional[str] = Field(
        None, description="Patient's full name in English", max_length=200
    )

    dob_gregorian: Optional[str] = Field(
        None,
        description="Date of birth in YYYY-MM-DD format (Gregorian calendar)",
        max_length=20,
    )

    dob_buddhist_era: Optional[str] = Field(
        None,
        description="Date of birth in DD/MM/YYYY format (Buddhist Era, +543 years)",
        max_length=20,
    )

    patient_contact_no: Optional[str] = Field(
        None, description="Patient's contact number", max_length=20
    )

    place_of_treatment: Optional[str] = Field(
        None, description="Any healthcare center", max_length=200
    )

    referring_physician_th: Optional[str] = Field(
        None, description="Referring physician name in Thai", max_length=200
    )

    referring_physician_en: Optional[str] = Field(
        None, description="Referring physician name in English", max_length=200
    )

    referring_physician_md_code: Optional[str] = Field(
        None, description="Referring physician MD code", max_length=50
    )

    referring_physician_email: List[str] = Field(
        default_factory=list, description="Array of email addresses for multiple emails"
    )

    # Legacy fields for backward compatibility (deprecated)
    thai_id: Optional[str] = Field(
        None,
        description="Thai national ID number (deprecated - use patient_code)",
        max_length=20,
    )

    # Additional medical information (optional)
    diagnoses: List[str] = Field(
        default_factory=list, description="Medical diagnoses found in document"
    )
    medications: List[str] = Field(
        default_factory=list, description="Prescribed medications found in document"
    )
    test_results: Dict[str, Any] = Field(
        default_factory=dict,
        description="Lab test results with values and reference ranges",
    )

    # Processing metadata
    confidence_score: float = Field(
        description="Overall confidence score (0-1)", ge=0.0, le=1.0
    )
    processing_time: float = Field(description="Processing time in seconds", ge=0.0)
    page_count: int = Field(description="Number of pages processed", ge=0)

    # Language detection
    detected_languages: List[str] = Field(
        default_factory=list, description="Detected languages in document"
    )

    # Error information
    errors: List[str] = Field(
        default_factory=list, description="Processing errors encountered"
    )
    warnings: List[str] = Field(default_factory=list, description="Processing warnings")

    # Additional metadata
    document_id: Optional[str] = Field(
        None, description="Unique identifier for the processed document"
    )
    timestamp: Optional[str] = Field(
        None, description="ISO timestamp when processing was completed"
    )

    @field_validator("confidence_score")
    @classmethod
    def validate_confidence_score(cls, v: float) -> float:
        """Validate confidence score is within valid range.

        Args:
            v: Confidence score value

        Returns:
            Validated confidence score

        Raises:
            ValueError: If confidence score is invalid
        """
        if not 0.0 <= v <= 1.0:
            raise ValueError("Confidence score must be between 0.0 and 1.0")
        return v

    @field_validator("patient_code")
    @classmethod
    def validate_patient_code(cls, v: Optional[str]) -> Optional[str]:
        """Validate patient code format if provided.

        Args:
            v: Patient code value

        Returns:
            Validated patient code or None
        """
        if v is None:
            return v

        # Check if 'TT' appears somewhere in the code
        if "TT" in v.upper():
            return v

        return v  # Return as-is for further validation

    @field_validator("sample_code")
    @classmethod
    def validate_sample_code(cls, v: Optional[str]) -> Optional[str]:
        """Validate sample code format if provided.

        Args:
            v: Sample code value

        Returns:
            Validated sample code or None
        """
        if v is None:
            return v

        # Check if it's alphanumeric and roughly 6 characters
        if v.isalnum() and 4 <= len(v) <= 8:
            return v

        return v  # Return as-is for further validation

    @field_validator("referring_physician_email")
    @classmethod
    def validate_emails(cls, v: List[str]) -> List[str]:
        """Validate email addresses in the list.

        Args:
            v: List of email addresses

        Returns:
            Validated list of email addresses
        """
        import re

        email_pattern = re.compile(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")

        validated_emails = []
        for email in v:
            if email and email_pattern.match(email):
                validated_emails.append(email)
            elif email:  # Keep invalid emails with warning
                validated_emails.append(email)

        return validated_emails

    def has_pii(self) -> bool:
        """Check if the result contains any PII data.

        Returns:
            True if any PII fields are populated
        """
        return any(
            [
                self.patient_name_th,
                self.patient_name_en,
                self.patient_code,
                self.patient_contact_no,
                self.dob_gregorian,
                self.dob_buddhist_era,
                self.thai_id,  # Legacy field
                self.referring_physician_email,
            ]
        )

    def get_pii_summary(self) -> Dict[str, bool]:
        """Get summary of PII types found in the result.

        Returns:
            Dictionary mapping PII type to whether it was found
        """
        return {
            "patient_code": bool(self.patient_code),
            "patient_name_th": bool(self.patient_name_th),
            "patient_name_en": bool(self.patient_name_en),
            "patient_contact_no": bool(self.patient_contact_no),
            "dob_gregorian": bool(self.dob_gregorian),
            "dob_buddhist_era": bool(self.dob_buddhist_era),
            "referring_physician_email": bool(self.referring_physician_email),
            "thai_id": bool(self.thai_id),  # Legacy field
        }

    def get_extraction_completeness(self) -> Dict[str, Any]:
        """Get completeness metrics for the 13 core fields.

        Returns:
            Dictionary with extraction completeness statistics
        """
        core_fields = [
            "patient_code",
            "sample_code",
            "investigation",
            "patient_name_th",
            "patient_name_en",
            "dob_gregorian",
            "dob_buddhist_era",
            "patient_contact_no",
            "place_of_treatment",
            "referring_physician_th",
            "referring_physician_en",
            "referring_physician_md_code",
            "referring_physician_email",
        ]

        extracted_count = 0
        field_status = {}

        for field in core_fields:
            value = getattr(self, field, None)
            is_extracted = (
                bool(value) if not isinstance(value, list) else bool(len(value))
            )
            field_status[field] = is_extracted
            if is_extracted:
                extracted_count += 1

        return {
            "extracted_fields": extracted_count,
            "total_fields": len(core_fields),
            "completeness_percentage": round(
                (extracted_count / len(core_fields)) * 100, 2
            ),
            "field_status": field_status,
        }
