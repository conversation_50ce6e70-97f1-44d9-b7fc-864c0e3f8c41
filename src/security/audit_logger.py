"""Audit logging system for PII operations and security events.

This module provides comprehensive audit logging capabilities for the ChromoForge OCR
Pipeline, ensuring all PII operations and security-sensitive events are properly
logged for compliance and monitoring purposes.
"""

import inspect
import json
import logging
import threading
import time
import uuid
from dataclasses import asdict, dataclass
from datetime import datetime, timezone
from enum import Enum
from functools import wraps
from pathlib import Path
from typing import Any, Dict, List, Optional

from ..core.exceptions import AuditLoggingException


class AuditEventType(Enum):
    """Types of audit events."""

    # PII Operations
    PII_DETECTED = "pii_detected"
    PII_ENCRYPTED = "pii_encrypted"
    PII_DECRYPTED = "pii_decrypted"
    PII_OBFUSCATED = "pii_obfuscated"
    PII_ACCESS = "pii_access"

    # Document Operations
    DOCUMENT_PROCESSED = "document_processed"
    DOCUMENT_UPLOADED = "document_uploaded"
    DOCUMENT_DOWNLOADED = "document_downloaded"
    DOCUMENT_DELETED = "document_deleted"

    # Authentication & Authorization
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    ACCESS_DENIED = "access_denied"
    PERMISSION_GRANTED = "permission_granted"

    # System Events
    SYSTEM_ERROR = "system_error"
    CONFIGURATION_CHANGED = "config_changed"
    BATCH_STARTED = "batch_started"
    BATCH_COMPLETED = "batch_completed"

    # Compliance Events
    DATA_RETENTION_APPLIED = "data_retention_applied"
    AUDIT_LOG_ACCESSED = "audit_log_accessed"
    COMPLIANCE_VIOLATION = "compliance_violation"


class AuditSeverity(Enum):
    """Severity levels for audit events."""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class AuditEvent:
    """Represents an audit event with all necessary metadata.

    Attributes:
        event_id: Unique identifier for the event
        event_type: Type of audit event
        timestamp: UTC timestamp when event occurred
        severity: Severity level of the event
        user_id: ID of the user who triggered the event (if applicable)
        session_id: Session ID associated with the event
        source_ip: IP address of the event source
        component: System component that generated the event
        action: Specific action that was performed
        resource: Resource that was affected
        resource_id: Unique identifier of the affected resource
        success: Whether the operation was successful
        error_code: Error code if operation failed
        metadata: Additional event-specific metadata
        pii_types: Types of PII involved (if applicable)
        compliance_tags: Compliance-related tags
    """

    event_id: str
    event_type: AuditEventType
    timestamp: str
    severity: AuditSeverity
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    source_ip: Optional[str] = None
    component: str = "chromoforge_ocr"
    action: str = ""
    resource: str = ""
    resource_id: Optional[str] = None
    success: bool = True
    error_code: Optional[str] = None
    metadata: Dict[str, Any] = None
    pii_types: List[str] = None
    compliance_tags: List[str] = None

    def __post_init__(self) -> None:
        """Initialize default values after creation."""
        if self.metadata is None:
            self.metadata = {}
        if self.pii_types is None:
            self.pii_types = []
        if self.compliance_tags is None:
            self.compliance_tags = []

    def to_dict(self) -> Dict[str, Any]:
        """Convert audit event to dictionary for serialization.

        Returns:
            Dictionary representation of the audit event
        """
        result = asdict(self)
        result["event_type"] = self.event_type.value
        result["severity"] = self.severity.value
        return result

    def to_json(self) -> str:
        """Convert audit event to JSON string.

        Returns:
            JSON representation of the audit event
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, default=str)


class AuditLogger:
    """Audit logger for PII operations and security events.

    This class provides thread-safe audit logging capabilities with support for
    multiple output formats and compliance requirements.
    """

    def __init__(
        self,
        log_file_path: Optional[Path] = None,
        console_output: bool = True,
        structured_logging: bool = True,
        buffer_size: int = 100,
        auto_flush_interval: int = 30,
    ) -> None:
        """Initialize audit logger.

        Args:
            log_file_path: Path to audit log file (None for no file logging)
            console_output: Whether to output to console
            structured_logging: Whether to use structured JSON logging
            buffer_size: Number of events to buffer before flushing
            auto_flush_interval: Seconds between automatic flushes
        """
        self.log_file_path = log_file_path
        self.console_output = console_output
        self.structured_logging = structured_logging
        self.buffer_size = buffer_size
        self.auto_flush_interval = auto_flush_interval

        # Thread safety
        self._lock = threading.RLock()
        self._buffer: List[AuditEvent] = []
        self._last_flush = time.time()

        # Setup loggers
        self._setup_loggers()

        # Default metadata
        self._default_metadata = {
            "system": "chromoforge_ocr_pipeline",
            "version": "1.0.0",
            "environment": "production",  # Should be set from config
        }

    def _setup_loggers(self) -> None:
        """Setup internal logging infrastructure."""
        # Create audit-specific logger
        self._audit_logger = logging.getLogger("chromoforge.audit")
        self._audit_logger.setLevel(logging.INFO)

        # Prevent duplicate logs
        if self._audit_logger.handlers:
            self._audit_logger.handlers.clear()

        # File handler
        if self.log_file_path:
            file_handler = logging.FileHandler(
                self.log_file_path, mode="a", encoding="utf-8"
            )

            if self.structured_logging:
                formatter = logging.Formatter("%(message)s")  # JSON will be the message
            else:
                formatter = logging.Formatter(
                    "%(asctime)s - %(levelname)s - %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S",
                )

            file_handler.setFormatter(formatter)
            self._audit_logger.addHandler(file_handler)

        # Console handler
        if self.console_output:
            console_handler = logging.StreamHandler()

            if self.structured_logging:
                formatter = logging.Formatter(
                    "%(asctime)s - AUDIT - %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
                )
            else:
                formatter = logging.Formatter(
                    "%(asctime)s - AUDIT - %(levelname)s - %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S",
                )

            console_handler.setFormatter(formatter)
            self._audit_logger.addHandler(console_handler)

    def log_event(
        self,
        event_type: AuditEventType,
        action: str,
        resource: str = "",
        severity: AuditSeverity = AuditSeverity.INFO,
        success: bool = True,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        source_ip: Optional[str] = None,
        resource_id: Optional[str] = None,
        error_code: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        pii_types: Optional[List[str]] = None,
        compliance_tags: Optional[List[str]] = None,
    ) -> str:
        """Log an audit event.

        Args:
            event_type: Type of audit event
            action: Specific action performed
            resource: Resource affected by the action
            severity: Severity level of the event
            success: Whether the operation was successful
            user_id: ID of the user who triggered the event
            session_id: Session ID associated with the event
            source_ip: IP address of the event source
            resource_id: Unique identifier of the affected resource
            error_code: Error code if operation failed
            metadata: Additional event-specific metadata
            pii_types: Types of PII involved
            compliance_tags: Compliance-related tags

        Returns:
            Unique event ID for the logged event

        Raises:
            AuditLoggingException: If logging fails
        """
        try:
            # Create audit event
            event = AuditEvent(
                event_id=str(uuid.uuid4()),
                event_type=event_type,
                timestamp=datetime.now(timezone.utc).isoformat(),
                severity=severity,
                user_id=user_id,
                session_id=session_id,
                source_ip=source_ip,
                action=action,
                resource=resource,
                resource_id=resource_id,
                success=success,
                error_code=error_code,
                metadata={**self._default_metadata, **(metadata or {})},
                pii_types=pii_types or [],
                compliance_tags=compliance_tags or [],
            )

            # Thread-safe logging
            with self._lock:
                self._buffer.append(event)

                # Check if we need to flush
                should_flush = (
                    len(self._buffer) >= self.buffer_size
                    or time.time() - self._last_flush >= self.auto_flush_interval
                    or severity in [AuditSeverity.ERROR, AuditSeverity.CRITICAL]
                )

                if should_flush:
                    self._flush_buffer()

            return event.event_id

        except Exception as e:
            raise AuditLoggingException(
                message=f"Failed to log audit event: {str(e)}",
                audit_event=event_type.value,
                context={"action": action, "resource": resource},
                original_exception=e,
            )

    def log_pii_detection(
        self,
        pii_types: List[str],
        pii_count: int,
        document_id: str,
        confidence_scores: Optional[Dict[str, float]] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
    ) -> str:
        """Log PII detection event.

        Args:
            pii_types: Types of PII detected
            pii_count: Number of PII items detected
            document_id: ID of the processed document
            confidence_scores: Confidence scores for PII types
            user_id: ID of the user who initiated the operation
            session_id: Session ID

        Returns:
            Event ID
        """
        return self.log_event(
            event_type=AuditEventType.PII_DETECTED,
            action="detect_pii",
            resource="document",
            resource_id=document_id,
            user_id=user_id,
            session_id=session_id,
            metadata={
                "pii_count": pii_count,
                "confidence_scores": confidence_scores or {},
            },
            pii_types=pii_types,
            compliance_tags=["pii_processing", "data_classification"],
        )

    def log_pii_obfuscation(
        self,
        document_id: str,
        obfuscation_method: str,
        pii_types: List[str],
        obfuscated_count: int,
        success: bool = True,
        error_code: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
    ) -> str:
        """Log PII obfuscation event.

        Args:
            document_id: ID of the processed document
            obfuscation_method: Method used for obfuscation
            pii_types: Types of PII obfuscated
            obfuscated_count: Number of PII items obfuscated
            success: Whether obfuscation was successful
            error_code: Error code if operation failed
            user_id: ID of the user who initiated the operation
            session_id: Session ID

        Returns:
            Event ID
        """
        return self.log_event(
            event_type=AuditEventType.PII_OBFUSCATED,
            action="obfuscate_pii",
            resource="document",
            resource_id=document_id,
            success=success,
            error_code=error_code,
            severity=AuditSeverity.INFO if success else AuditSeverity.ERROR,
            user_id=user_id,
            session_id=session_id,
            metadata={
                "obfuscation_method": obfuscation_method,
                "obfuscated_count": obfuscated_count,
            },
            pii_types=pii_types,
            compliance_tags=["pii_protection", "data_anonymization"],
        )

    def log_document_processing(
        self,
        document_id: str,
        processing_type: str,
        processing_time: float,
        success: bool = True,
        error_code: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        document_metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Log document processing event.

        Args:
            document_id: ID of the processed document
            processing_type: Type of processing performed
            processing_time: Time taken for processing
            success: Whether processing was successful
            error_code: Error code if operation failed
            user_id: ID of the user who initiated the operation
            session_id: Session ID
            document_metadata: Additional document metadata

        Returns:
            Event ID
        """
        return self.log_event(
            event_type=AuditEventType.DOCUMENT_PROCESSED,
            action=f"process_document_{processing_type}",
            resource="document",
            resource_id=document_id,
            success=success,
            error_code=error_code,
            severity=AuditSeverity.INFO if success else AuditSeverity.ERROR,
            user_id=user_id,
            session_id=session_id,
            metadata={
                "processing_type": processing_type,
                "processing_time_seconds": processing_time,
                "document_metadata": document_metadata or {},
            },
            compliance_tags=["document_processing", "data_processing"],
        )

    def log_access_event(
        self,
        resource: str,
        action: str,
        success: bool = True,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        source_ip: Optional[str] = None,
        resource_id: Optional[str] = None,
        access_reason: Optional[str] = None,
    ) -> str:
        """Log access event for sensitive resources.

        Args:
            resource: Resource being accessed
            action: Action being performed
            success: Whether access was granted
            user_id: ID of the user attempting access
            session_id: Session ID
            source_ip: IP address of the access attempt
            resource_id: ID of the specific resource
            access_reason: Reason for access

        Returns:
            Event ID
        """
        event_type = (
            AuditEventType.PERMISSION_GRANTED
            if success
            else AuditEventType.ACCESS_DENIED
        )
        severity = AuditSeverity.INFO if success else AuditSeverity.WARNING

        return self.log_event(
            event_type=event_type,
            action=action,
            resource=resource,
            resource_id=resource_id,
            success=success,
            severity=severity,
            user_id=user_id,
            session_id=session_id,
            source_ip=source_ip,
            metadata={"access_reason": access_reason},
            compliance_tags=["access_control", "authorization"],
        )

    def _flush_buffer(self) -> None:
        """Flush buffered audit events to persistent storage."""
        if not self._buffer:
            return

        try:
            # Get events to flush
            events_to_flush = self._buffer.copy()
            self._buffer.clear()
            self._last_flush = time.time()

            # Log each event
            for event in events_to_flush:
                if self.structured_logging:
                    message = event.to_json()
                else:
                    message = (
                        f"{event.event_type.value} - {event.action} - "
                        f"Resource: {event.resource} - Success: {event.success}"
                    )

                # Map severity to logging level
                level_map = {
                    AuditSeverity.INFO: logging.INFO,
                    AuditSeverity.WARNING: logging.WARNING,
                    AuditSeverity.ERROR: logging.ERROR,
                    AuditSeverity.CRITICAL: logging.CRITICAL,
                }

                self._audit_logger.log(
                    level_map[event.severity],
                    message,
                    extra={"audit_event": event.to_dict()},
                )

        except Exception as e:
            # Log to standard logger if audit logging fails
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to flush audit buffer: {str(e)}")

    def flush(self) -> None:
        """Manually flush all buffered audit events."""
        with self._lock:
            self._flush_buffer()

    def close(self) -> None:
        """Close audit logger and flush remaining events."""
        with self._lock:
            self._flush_buffer()

            # Close handlers
            for handler in self._audit_logger.handlers:
                handler.close()


# Decorator for automatic audit logging
def audit_log(
    event_type: AuditEventType,
    action: Optional[str] = None,
    resource: Optional[str] = None,
    pii_types: Optional[List[str]] = None,
    compliance_tags: Optional[List[str]] = None,
):
    """Decorator for automatic audit logging of function calls.

    Args:
        event_type: Type of audit event
        action: Action name (defaults to function name)
        resource: Resource being operated on
        pii_types: Types of PII involved
        compliance_tags: Compliance tags
    """

    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            audit_logger = get_audit_logger()

            # Determine action name
            actual_action = action or func.__name__

            # Extract metadata from function signature
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()

            metadata = {
                "function": func.__name__,
                "module": func.__module__,
                "arguments": {
                    k: (
                        "***REDACTED***"
                        if "pii" in k.lower() or "password" in k.lower()
                        else str(v)
                    )
                    for k, v in bound_args.arguments.items()
                },
            }

            try:
                # Execute function
                start_time = time.time()
                result = await func(*args, **kwargs)
                end_time = time.time()

                # Log successful execution
                metadata["execution_time"] = end_time - start_time

                audit_logger.log_event(
                    event_type=event_type,
                    action=actual_action,
                    resource=resource or "unknown",
                    success=True,
                    metadata=metadata,
                    pii_types=pii_types,
                    compliance_tags=compliance_tags,
                )

                return result

            except Exception as e:
                # Log failed execution
                metadata["error"] = str(e)

                audit_logger.log_event(
                    event_type=event_type,
                    action=actual_action,
                    resource=resource or "unknown",
                    success=False,
                    severity=AuditSeverity.ERROR,
                    error_code=getattr(e, "error_code", "UNKNOWN"),
                    metadata=metadata,
                    pii_types=pii_types,
                    compliance_tags=compliance_tags,
                )

                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # Similar logic for sync functions
            audit_logger = get_audit_logger()

            actual_action = action or func.__name__

            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()

            metadata = {
                "function": func.__name__,
                "module": func.__module__,
                "arguments": {
                    k: (
                        "***REDACTED***"
                        if "pii" in k.lower() or "password" in k.lower()
                        else str(v)
                    )
                    for k, v in bound_args.arguments.items()
                },
            }

            try:
                start_time = time.time()
                result = func(*args, **kwargs)
                end_time = time.time()

                metadata["execution_time"] = end_time - start_time

                audit_logger.log_event(
                    event_type=event_type,
                    action=actual_action,
                    resource=resource or "unknown",
                    success=True,
                    metadata=metadata,
                    pii_types=pii_types,
                    compliance_tags=compliance_tags,
                )

                return result

            except Exception as e:
                metadata["error"] = str(e)

                audit_logger.log_event(
                    event_type=event_type,
                    action=actual_action,
                    resource=resource or "unknown",
                    success=False,
                    severity=AuditSeverity.ERROR,
                    error_code=getattr(e, "error_code", "UNKNOWN"),
                    metadata=metadata,
                    pii_types=pii_types,
                    compliance_tags=compliance_tags,
                )

                raise

        # Return appropriate wrapper based on function type
        if inspect.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


# Global audit logger instance
_audit_logger: Optional[AuditLogger] = None


def initialize_audit_logger(
    log_file_path: Optional[Path] = None,
    console_output: bool = True,
    structured_logging: bool = True,
) -> AuditLogger:
    """Initialize global audit logger instance.

    Args:
        log_file_path: Path to audit log file
        console_output: Whether to output to console
        structured_logging: Whether to use structured JSON logging

    Returns:
        Initialized audit logger instance
    """
    global _audit_logger

    _audit_logger = AuditLogger(
        log_file_path=log_file_path,
        console_output=console_output,
        structured_logging=structured_logging,
    )

    return _audit_logger


def get_audit_logger() -> AuditLogger:
    """Get global audit logger instance.

    Returns:
        Global audit logger instance

    Raises:
        RuntimeError: If audit logger not initialized
    """
    global _audit_logger

    if _audit_logger is None:
        # Initialize with default settings
        _audit_logger = AuditLogger()

    return _audit_logger
