"""Main application entry point for ChromoForge OCR pipeline.

This module provides comprehensive error handling and recovery mechanisms:
- Graceful degradation when services are unavailable
- Circuit breaker patterns for external API calls
- Retry logic with exponential backoff
- Recovery strategies for common failure scenarios
- HIPAA-compliant error logging (no sensitive data in logs)
"""

import argparse
import asyncio
import hashlib
import json
import logging
import re
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from .core.config import settings
from .core.constants import (DEFAULT_FILE_PATTERN, EXIT_CODES,
                             FALLBACK_PII_CONFIDENCE_THRESHOLD)
from .core.exceptions import ConfigurationException
from .processing.batch_processor import BatchProcessor
from .processing.ocr_processor import GeminiOCRProcessor
from .processing.pdf_obfuscator import ObfuscationMethod, PDFObfuscator
from .processing.pii_detector import PIIDetector
from .utils.logging_config import OCRLogger, setup_logging


class MedicalDataExtractor:
    """Medical data extractor with structured field extraction."""

    def __init__(self) -> None:
        """Initialize the medical data extractor."""
        self.investigation_patterns = [
            r"K-TRACK(?:\s+MET)?",
            r"SPOT-MAS",
            r"K4CARE",
            r"K-TRACK\s*MET",
            r"TT\d{5}",
        ]

    def extract_patient_code(self, text: str) -> Optional[str]:
        """Extract patient code that begins with 'TT' somewhere."""
        patterns = [r"TT\d{5}", r"(?:Patient\s+Code|Code):\s*(TT\d+)", r"\bTT\d{4,6}\b"]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1) if match.groups() else match.group(0)
        return None

    def extract_sample_code(self, text: str) -> Optional[str]:
        """Extract sample code - random mix of 6 characters."""
        # Look for 6-character alphanumeric codes
        patterns = [
            r"\b[A-Z0-9]{6}\b",
            r"(?:Sample|Code):\s*([A-Z0-9]{6})",
            r"LN:\s*([A-Z0-9]{6})",
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match) == 6 and re.match(r"^[A-Z0-9]+$", match):
                    return match
        return None

    def extract_investigation(self, text: str) -> Optional[str]:
        """Extract investigation/test names."""
        for pattern in self.investigation_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(0)
        return None

    def extract_thai_name(self, text: str) -> Optional[str]:
        """Extract Thai patient name."""
        patterns = [
            r"(?:นาย|นาง|นางสาว|ด\.ช\.|ด\.ญ\.)\s*[\u0E01-\u0E5B\s]{2,50}",
            r"ชื่อผู้ป่วย:\s*([\u0E01-\u0E5B\s]+)",
            r"Patient.*?:\s*([\u0E01-\u0E5B\s]+)",
        ]

        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                name = match.group(1) if match.groups() else match.group(0)
                return name.strip()
        return None

    def extract_english_name(self, text: str) -> Optional[str]:
        """Extract English patient name."""
        patterns = [
            r"(?:Mr\.?|Mrs\.?|Ms\.?|Miss|Dr\.?)\s+[A-Z][a-z]+\s+[A-Z][a-z]+",
            r"Full\s+name:\s*([A-Z][a-z]+\s+[A-Z][a-z]+)",
            r"Patient.*?:\s*([A-Z][a-z]+\s+[A-Z][a-z]+)",
        ]

        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                name = match.group(1) if match.groups() else match.group(0)
                return name.strip()
        return None

    def extract_dob(self, text: str) -> tuple[Optional[str], Optional[str]]:
        """Extract date of birth in both Gregorian and BE formats."""
        # Gregorian format (YYYY-MM-DD)
        gregorian_patterns = [
            r"(\d{4})-(\d{2})-(\d{2})",
            r"DOB:\s*(\d{4})-(\d{2})-(\d{2})",
            r"Date\s+of\s+birth:\s*(\d{4})-(\d{2})-(\d{2})",
        ]

        # BE format (DD/MM/YYYY where YYYY is BE year)
        be_patterns = [
            r"(\d{2})/(\d{2})/(\d{4})",
            r"DOB.*?(\d{2})/(\d{2})/(\d{4})",
            r"วันเกิด.*?(\d{2})/(\d{2})/(\d{4})",
        ]

        gregorian_dob = None
        be_dob = None

        for pattern in gregorian_patterns:
            match = re.search(pattern, text)
            if match:
                year, month, day = match.groups()
                if int(year) < 2600:  # Gregorian year
                    gregorian_dob = f"{year}-{month}-{day}"
                    break

        for pattern in be_patterns:
            match = re.search(pattern, text)
            if match:
                day, month, year = match.groups()
                if int(year) > 2400:  # BE year
                    be_dob = f"{day}/{month}/{year}"
                    break

        return gregorian_dob, be_dob

    def extract_contact_info(self, text: str) -> Optional[str]:
        """Extract patient contact number."""
        patterns = [
            r"(?:Phone|Tel|โทร):\s*([\d\-\s\(\)]+)",
            r"(\d{3}-\d{3}-\d{4})",
            r"(\d{10})",
            r"Contact.*?(\d{3}-\d{3}-\d{4})",
        ]

        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1).strip()
        return None

    def extract_place_of_treatment(self, text: str) -> Optional[str]:
        """Extract healthcare center/place of treatment."""
        patterns = [
            r"(?:Hospital|Center|Clinic|โรงพยาบาล)[\s:]*([^\n]+)",
            r"Place\s+of\s+treatment:\s*([^\n]+)",
            r"Healthcare.*?:\s*([^\n]+)",
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        return None

    def extract_physician_info(
        self, text: str
    ) -> tuple[Optional[str], Optional[str], Optional[str], List[str]]:
        """Extract referring physician information."""
        # Thai physician name
        thai_physician = None
        thai_patterns = [
            r"แพทย์.*?:\s*([\u0E01-\u0E5B\s]+)",
            r"หมอ.*?:\s*([\u0E01-\u0E5B\s]+)",
        ]

        for pattern in thai_patterns:
            match = re.search(pattern, text)
            if match:
                thai_physician = match.group(1).strip()
                break

        # English physician name
        english_physician = None
        english_patterns = [
            r"(?:Dr\.?|Doctor)\s+([A-Z][a-z]+\s+[A-Z][a-z]+)",
            r"Physician:\s*([A-Z][a-z]+\s+[A-Z][a-z]+)",
            r"Referring.*?:\s*([A-Z][a-z]+\s+[A-Z][a-z]+)",
        ]

        for pattern in english_patterns:
            match = re.search(pattern, text)
            if match:
                english_physician = match.group(1).strip()
                break

        # MD Code
        md_code = None
        md_patterns = [
            r"MD\s*Code:\s*([A-Z0-9]+)",
            r"License:\s*([A-Z0-9]+)",
            r"รหัส.*?:\s*([A-Z0-9]+)",
        ]

        for pattern in md_patterns:
            match = re.search(pattern, text)
            if match:
                md_code = match.group(1).strip()
                break

        # Email addresses
        email_pattern = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
        emails = re.findall(email_pattern, text)

        return thai_physician, english_physician, md_code, emails

    def extract_all_fields(self, text: str) -> Dict[str, Any]:
        """Extract all medical fields from the OCR text."""
        gregorian_dob, be_dob = self.extract_dob(text)
        thai_physician, english_physician, md_code, emails = (
            self.extract_physician_info(text)
        )

        return {
            "patient_code": self.extract_patient_code(text),
            "sample_code": self.extract_sample_code(text),
            "investigation": self.extract_investigation(text),
            "patient_full_name_th": self.extract_thai_name(text),
            "patient_full_name_en": self.extract_english_name(text),
            "dob": gregorian_dob,
            "dob_be": be_dob,
            "patient_contact_no": self.extract_contact_info(text),
            "place_of_treatment": self.extract_place_of_treatment(text),
            "referring_physician_th": thai_physician,
            "referring_physician_en": english_physician,
            "referring_physician_md_code": md_code,
            "referring_physician_emails": emails if emails else None,
        }


class SupabaseIntegration:
    """Handle Supabase database operations for medical extractions."""

    def __init__(self) -> None:
        """Initialize Supabase client."""
        import os

        from supabase import create_client

        self.url = os.getenv("NEXT_PUBLIC_SUPABASE_URL")
        self.key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

        if not self.url or not self.key:
            raise ValueError("Supabase credentials not found in environment variables")

        self.client = create_client(
            self.url,
            self.key,
            options={
                "postgrest": {
                    "timeout": 30,
                    "verify": True
                }
            }
        )
        logger.info("Supabase client initialized")

    async def create_document_record(
        self, file_path: Path, file_size: int, checksum: str
    ) -> str:
        """Create a document record and return the document ID."""
        try:
            # Get or create organization
            org_result = (
                self.client.table("organizations")
                .select("id")
                .eq("name", "ChromoForge Demo Organization")
                .execute()
            )

            if not org_result.data:
                # Create organization if it doesn't exist
                org_result = (
                    self.client.table("organizations")
                    .insert(
                        {
                            "name": "ChromoForge Demo Organization",
                            "settings": {"demo": True},
                        }
                    )
                    .execute()
                )

            org_id = org_result.data[0]["id"]

            # Create document record
            document_data = {
                "organization_id": org_id,
                "file_name": file_path.name,
                "file_size": file_size,
                "mime_type": "application/pdf",
                "checksum": checksum,
                "original_path": str(file_path),
                "status": "processing",
                "processing_started_at": datetime.utcnow().isoformat(),
            }

            result = self.client.table("documents").insert(document_data).execute()
            document_id = result.data[0]["id"]

            logger.info(f"Created document record with ID: {document_id}")
            return document_id

        except Exception as e:
            logger.error(f"Failed to create document record: {str(e)}")
            raise

    async def save_medical_extraction(
        self,
        document_id: str,
        extracted_data: Dict[str, Any],
        raw_text: str,
        pii_matches: List[Dict],
        confidence: float,
    ) -> str:
        """Save medical extraction data to the database."""
        try:
            extraction_data = {
                "document_id": document_id,
                "raw_ocr_text": raw_text,
                "extraction_confidence": confidence,
                "pii_detected": pii_matches,
                **extracted_data,  # Spread the extracted medical fields
            }

            # Remove None values to keep database fields NULL
            extraction_data = {
                k: v for k, v in extraction_data.items() if v is not None
            }

            result = (
                self.client.table("medical_extractions")
                .insert(extraction_data)
                .execute()
            )
            extraction_id = result.data[0]["id"]

            logger.info(f"Saved medical extraction with ID: {extraction_id}")
            return extraction_id

        except Exception as e:
            logger.error(f"Failed to save medical extraction: {str(e)}")
            raise

    async def update_document_status(
        self, document_id: str, status: str, error: Optional[str] = None
    ) -> None:
        """Update document processing status."""
        try:
            update_data = {
                "status": status,
                "processing_completed_at": datetime.utcnow().isoformat(),
            }

            if error:
                update_data["processing_error"] = {"error": error}

            self.client.table("documents").update(update_data).eq(
                "id", document_id
            ).execute()
            logger.info(f"Updated document {document_id} status to: {status}")

        except Exception as e:
            logger.error(f"Failed to update document status: {str(e)}")


# Set up logging before other imports
setup_logging()
logger = logging.getLogger(__name__)
ocr_logger = OCRLogger(__name__)


# Circuit breaker is now imported from utils


class ChromoForgeApp:
    """Main application class for ChromoForge OCR pipeline.

    Provides comprehensive error handling and recovery mechanisms:
    - Circuit breaker patterns for external services
    - Graceful degradation when components fail
    - Retry logic with exponential backoff
    - HIPAA-compliant error logging
    - Recovery strategies for common failure scenarios
    """

    def __init__(
        self,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        enable_medical: bool = False,
        enable_database: bool = False,
    ):
        """Initialize the medical ChromoForge application with error handling.

        Args:
            user_id: User ID for audit logging
            session_id: Session ID for tracking
            enable_medical: Enable medical extraction features
            enable_database: Enable database integration

        Raises:
            ConfigurationException: If initialization fails due to configuration issues
            OCRProcessingException: If OCR processor initialization fails
        """
        self.user_id = user_id
        self.session_id = session_id
        self.enable_medical = enable_medical
        self.enable_database = enable_database
        self._initialization_errors = []

        # Initialize medical components
        if enable_medical:
            self.medical_extractor = MedicalDataExtractor()
            logger.info("Medical data extractor initialized")

        if enable_database:
            try:
                self.db_client = SupabaseIntegration()
                logger.info("Database integration enabled")
            except Exception as e:
                logger.warning(f"Database initialization failed: {str(e)}")
                self.db_client = None
                self.enable_database = False

        try:
            # Initialize components with error handling
            self._initialize_components()

            logger.info(
                "ChromoForge OCR pipeline initialized with comprehensive error handling",
                extra={
                    "user_id": user_id,
                    "session_id": session_id,
                    "medical": enable_medical,
                    "database": enable_database,
                },
            )

        except Exception as e:
            error_msg = f"Failed to initialize ChromoForge application: {str(e)}"
            logger.error(
                error_msg, extra={"user_id": user_id, "session_id": session_id}
            )
            raise ConfigurationException(
                message=error_msg,
                config_section="application_initialization",
                context={"user_id": user_id, "session_id": session_id},
                original_exception=e,
            )

    def _initialize_components(self) -> None:
        """Initialize application components with error handling and fallbacks.

        Raises:
            ConfigurationException: If critical components cannot be initialized
        """
        # Initialize OCR processor with error handling
        try:
            self.ocr_processor = GeminiOCRProcessor(
                user_id=self.user_id, session_id=self.session_id
            )
            logger.info("OCR processor initialized successfully")
        except Exception as e:
            self._initialization_errors.append(f"OCR processor: {str(e)}")
            logger.error(f"Failed to initialize OCR processor: {str(e)}")
            # OCR processor is critical - re-raise
            raise

        # Initialize PII detector with error handling
        try:
            self.pii_detector = PIIDetector(
                confidence_threshold=settings.confidence_threshold
            )
            logger.info("PII detector initialized successfully")
        except Exception as e:
            self._initialization_errors.append(f"PII detector: {str(e)}")
            logger.error(f"Failed to initialize PII detector: {str(e)}")
            # Use fallback PII detector with basic patterns
            try:
                self.pii_detector = PIIDetector(
                    confidence_threshold=FALLBACK_PII_CONFIDENCE_THRESHOLD
                )
                logger.warning(
                    "Using fallback PII detector with reduced confidence threshold"
                )
            except Exception as fallback_error:
                logger.critical(
                    f"Failed to initialize fallback PII detector: {str(fallback_error)}"
                )
                # PII detection is critical for medical documents - re-raise original error
                raise e

        # Initialize PDF obfuscator with error handling
        try:
            self.pdf_obfuscator = PDFObfuscator(
                method=ObfuscationMethod(settings.obfuscation_method)
            )
            logger.info("PDF obfuscator initialized successfully")
        except Exception as e:
            self._initialization_errors.append(f"PDF obfuscator: {str(e)}")
            logger.error(f"Failed to initialize PDF obfuscator: {str(e)}")
            # Use fallback obfuscation method
            try:
                self.pdf_obfuscator = PDFObfuscator(method=ObfuscationMethod.BLACK_BOX)
                logger.warning("Using fallback black box obfuscation method")
            except Exception as fallback_error:
                logger.critical(
                    f"Failed to initialize fallback PDF obfuscator: {str(fallback_error)}"
                )
                # PDF obfuscation is critical for PII protection - re-raise original error
                raise e

        # Log any non-critical initialization errors
        if self._initialization_errors:
            logger.warning(
                f"Some components had initialization issues: {'; '.join(self._initialization_errors)}"
            )

    async def process_single_file(
        self, input_path: Path, output_dir: Path, enable_obfuscation: bool = True
    ) -> dict:
        """Process a single PDF file.

        Args:
            input_path: Path to input PDF file
            output_dir: Output directory for results
            enable_obfuscation: Whether to perform PII obfuscation

        Returns:
            Processing results dictionary
        """
        logger.info(f"Processing single file: {input_path}")
        ocr_logger.log_ocr_start(str(input_path), input_path.stat().st_size)

        # Initialize variables for medical mode
        document_id = None
        extraction_id = None
        extracted_fields = {}

        try:
            # Ensure output directory exists
            output_dir.mkdir(parents=True, exist_ok=True)

            # Calculate file checksum for database if enabled
            if self.enable_database and self.db_client:
                with open(input_path, "rb") as f:
                    file_content = f.read()
                    checksum = hashlib.sha256(file_content).hexdigest()

                document_id = await self.db_client.create_document_record(
                    input_path, len(file_content), checksum
                )

            # Step 1: OCR Processing with ULTRA THINK
            if self.enable_medical:
                # Use medical prompt for better medical field extraction
                import os

                import google.genai as genai

                api_key = os.getenv("GOOGLE_API_KEY")
                client = genai.Client(api_key=api_key)

                with open(input_path, "rb") as f:
                    pdf_bytes = f.read()

                medical_prompt = """
                Extract all text and structured medical information from this Thai medical document.

                Please provide a comprehensive extraction including:

                1. Complete text extraction
                2. Patient identification:
                   - Patient codes (especially those starting with 'TT')
                   - Sample codes (6-character alphanumeric)
                   - Investigation types (K-TRACK, SPOT-MAS, K4CARE, etc.)
                3. Patient information:
                   - Thai name
                   - English name
                   - Date of birth (both Gregorian YYYY-MM-DD and BE DD/MM/YYYY formats)
                   - Contact numbers
                4. Medical facility information:
                   - Place of treatment
                   - Referring physician (Thai and English names)
                   - Physician MD codes
                   - Email addresses
                5. Medical data:
                   - Diagnoses
                   - Medications
                   - Test results

                Return the response as JSON with this structure:
                {
                  "full_text": "complete extracted text",
                  "structured_data": {
                    "patient_code": "TT code if found",
                    "sample_code": "6-char code if found",
                    "investigation": "test type",
                    "patient_name_th": "Thai name",
                    "patient_name_en": "English name",
                    "diagnoses": ["list of diagnoses"],
                    "medications": ["list of medications"]
                  },
                  "confidence_score": 0.95
                }
                """

                response = client.models.generate_content(
                    model="gemini-2.0-flash-exp",
                    contents=[
                        {
                            "parts": [
                                {"text": medical_prompt},
                                {
                                    "inline_data": {
                                        "mime_type": "application/pdf",
                                        "data": pdf_bytes,
                                    }
                                },
                            ]
                        }
                    ],
                )

                if not response or not response.text:
                    raise Exception("No response from Gemini API")

                # Parse medical OCR response
                try:
                    response_text = response.text.strip()
                    if "```json" in response_text:
                        json_start = response_text.find("```json") + 7
                        json_end = response_text.find("```", json_start)
                        json_text = response_text[json_start:json_end].strip()
                    else:
                        json_text = response_text

                    medical_result = json.loads(json_text)
                    full_text = medical_result.get("full_text", response.text)
                    confidence = medical_result.get("confidence_score", 0.8)

                    # Create OCR result object for compatibility
                    from .core.models import OCRResult

                    ocr_result = OCRResult(
                        full_text=full_text,
                        confidence_score=confidence,
                        processing_time=0.0,
                        structured_data=medical_result.get("structured_data", {}),
                        errors=[],
                    )
                except json.JSONDecodeError:
                    full_text = response.text
                    confidence = 0.8
                    from .core.models import OCRResult

                    ocr_result = OCRResult(
                        full_text=full_text,
                        confidence_score=confidence,
                        processing_time=0.0,
                        structured_data={},
                        errors=[],
                    )
            else:
                # Use standard OCR processing
                ocr_result = await self.ocr_processor.process_pdf_with_retry(input_path)

            # Record transaction in database if enabled
            if hasattr(self.ocr_processor, "record_processing_transaction"):
                transaction_id = await self.ocr_processor.record_processing_transaction(
                    input_path, ocr_result
                )
                if transaction_id:
                    logger.info(f"Recorded transaction: {transaction_id}")

            if ocr_result.errors:
                ocr_logger.log_ocr_error(str(input_path), "; ".join(ocr_result.errors))
                return {
                    "success": False,
                    "error": "OCR processing failed",
                    "details": ocr_result.errors,
                }

            # Step 2: Medical Data Extraction (if enabled)
            if self.enable_medical and hasattr(self, "medical_extractor"):
                extracted_fields = self.medical_extractor.extract_all_fields(
                    ocr_result.full_text
                )
                logger.info("Medical data extraction completed")

            # Step 3: PII Detection
            pii_matches = self.pii_detector.detect_pii(ocr_result.full_text)

            # Log PII detection results
            pii_types = {}
            for match in pii_matches:
                pii_type = match.pii_type.value
                pii_types[pii_type] = pii_types.get(pii_type, 0) + 1

            ocr_logger.log_pii_detection(str(input_path), pii_types, len(pii_matches))

            # Step 4: Save to Database (if enabled)
            if self.enable_database and self.db_client and document_id:
                pii_data = [
                    {
                        "type": match.pii_type.value,
                        "text": match.text,
                        "confidence": match.confidence,
                        "line_number": match.line_number,
                        "context": match.context,
                    }
                    for match in pii_matches
                ]

                extraction_id = await self.db_client.save_medical_extraction(
                    document_id,
                    extracted_fields,
                    ocr_result.full_text,
                    pii_data,
                    ocr_result.confidence_score,
                )

                await self.db_client.update_document_status(document_id, "completed")
                logger.info(f"Saved to database with extraction ID: {extraction_id}")

            # Step 5: Save OCR results
            results_filename = (
                f"{input_path.stem}_medical_results.json"
                if self.enable_medical
                else f"{input_path.stem}_ocr_results.json"
            )
            results_path = output_dir / results_filename

            results_data = {
                "file_path": str(input_path),
                "document_id": document_id,
                "extraction_id": extraction_id,
                "processing_time": getattr(ocr_result, "processing_time", 0.0),
                "ocr_result": ocr_result.dict(),
                "pii_matches": [
                    {
                        "type": match.pii_type.value,
                        "text": match.text,
                        "confidence": match.confidence,
                        "line_number": match.line_number,
                        "context": match.context,
                    }
                    for match in pii_matches
                ],
                "pii_count": len(pii_matches),
            }

            if self.enable_medical:
                results_data["extracted_fields"] = extracted_fields

            with open(results_path, "w", encoding="utf-8") as f:
                json.dump(results_data, f, indent=2, ensure_ascii=False, default=str)

            # Step 6: PDF Obfuscation (if enabled and PII found)
            obfuscation_result = None
            if enable_obfuscation and pii_matches:
                output_path = output_dir / f"obfuscated_{input_path.name}"
                obfuscation_result = await self.pdf_obfuscator.obfuscate_pdf(
                    input_path, output_path, pii_matches
                )

                if obfuscation_result.get("success"):
                    ocr_logger.log_obfuscation_complete(
                        str(input_path),
                        str(output_path),
                        obfuscation_result.get("pii_items_obfuscated", 0),
                        self.pdf_obfuscator.method.value,
                    )

            # Log completion
            ocr_logger.log_ocr_complete(
                str(input_path),
                ocr_result.processing_time,
                ocr_result.confidence_score,
                len(pii_matches),
            )

            result = {
                "success": True,
                "file_path": str(input_path),
                "results_path": str(results_path),
                "ocr_result": ocr_result.dict(),
                "pii_matches_count": len(pii_matches),
                "obfuscation_result": obfuscation_result,
                "processing_time": getattr(ocr_result, "processing_time", 0.0),
            }

            if self.enable_medical:
                result["extracted_fields"] = extracted_fields

            if self.enable_database:
                result["document_id"] = document_id
                result["extraction_id"] = extraction_id

            return result

        except Exception as e:
            error_msg = str(e)
            logger.error(f"Failed to process {input_path}: {error_msg}")
            ocr_logger.log_ocr_error(str(input_path), error_msg)

            # Update document status to failed if database is enabled
            if self.enable_database and self.db_client and document_id:
                try:
                    await self.db_client.update_document_status(
                        document_id, "failed", error_msg
                    )
                except Exception as db_error:
                    logger.error(f"Failed to update document status: {str(db_error)}")

            return {
                "success": False,
                "error": error_msg,
                "file_path": str(input_path),
                "document_id": document_id if self.enable_database else None,
            }

    async def process_batch(
        self, input_paths: list[Path], output_dir: Path, enable_obfuscation: bool = True
    ) -> dict:
        """Process multiple PDF files in batch.

        Args:
            input_paths: List of input PDF paths
            output_dir: Output directory for results
            enable_obfuscation: Whether to perform PII obfuscation

        Returns:
            Batch processing results
        """
        logger.info(f"Processing batch of {len(input_paths)} files")

        batch_processor = BatchProcessor(
            self.ocr_processor, self.pii_detector, self.pdf_obfuscator
        )

        results = await batch_processor.process_batch(
            input_paths, output_dir, enable_obfuscation=enable_obfuscation
        )

        # Log batch statistics
        stats = results["batch_stats"]
        ocr_logger.log_batch_stats(
            stats.total_files,
            stats.completed_files,
            stats.failed_files,
            stats.average_processing_time,
        )

        return results

    async def process_directory(
        self,
        input_dir: Path,
        output_dir: Path,
        pattern: str = "*.pdf",
        recursive: bool = True,
        enable_obfuscation: bool = True,
    ) -> dict:
        """Process all PDF files in a directory.

        Args:
            input_dir: Input directory to scan
            output_dir: Output directory for results
            pattern: File pattern for matching
            recursive: Whether to scan subdirectories
            enable_obfuscation: Whether to perform PII obfuscation

        Returns:
            Processing results
        """
        logger.info(f"Processing directory: {input_dir}")

        batch_processor = BatchProcessor(
            self.ocr_processor, self.pii_detector, self.pdf_obfuscator
        )

        return await batch_processor.process_directory(
            input_dir,
            output_dir,
            pattern=pattern,
            recursive=recursive,
            enable_obfuscation=enable_obfuscation,
        )


async def main():
    """Main entry point for the application."""
    parser = argparse.ArgumentParser(
        description="ChromoForge OCR Pipeline - Process medical PDFs with PII detection and obfuscation"
    )

    # Input/Output options
    parser.add_argument(
        "--input",
        "-i",
        type=str,
        required=True,
        help="Input PDF file or directory path",
    )

    parser.add_argument(
        "--output",
        "-o",
        type=str,
        required=True,
        help="Output directory for processed files",
    )

    # Processing options
    parser.add_argument(
        "--no-obfuscation",
        action="store_true",
        help="Disable PDF obfuscation (only perform OCR and PII detection)",
    )

    parser.add_argument(
        "--pattern",
        type=str,
        default=DEFAULT_FILE_PATTERN,
        help="File pattern for directory processing (default: *.pdf)",
    )

    parser.add_argument(
        "--no-recursive",
        action="store_true",
        help="Disable recursive directory scanning",
    )

    # Configuration overrides
    parser.add_argument(
        "--confidence-threshold",
        type=float,
        help=f"PII confidence threshold (default: {settings.confidence_threshold})",
    )

    parser.add_argument(
        "--obfuscation-method",
        type=str,
        choices=["black_box", "redact", "white_box", "blur", "hash_pattern"],
        help=f"Obfuscation method (default: {settings.obfuscation_method})",
    )

    parser.add_argument(
        "--max-concurrent",
        type=int,
        help=f"Maximum concurrent processing (default: {settings.max_concurrent_requests})",
    )

    # Medical features
    parser.add_argument(
        "--medical",
        action="store_true",
        help="Enable medical extraction features with structured field extraction",
    )

    parser.add_argument(
        "--database",
        "--save-to-db",
        action="store_true",
        help="Enable database integration to save results to Supabase",
    )

    parser.add_argument(
        "--disable-ultra-think",
        action="store_true",
        help="Disable ULTRA THINK mode for faster but less accurate processing",
    )

    parser.add_argument(
        "--organization-id",
        type=str,
        help="Organization ID for database transaction recording",
    )

    parser.add_argument("--user-id", type=str, help="User ID for audit logging")

    parser.add_argument("--session-id", type=str, help="Session ID for tracking")

    # Utility options
    from .core.constants import APP_DESCRIPTION, APP_NAME, APP_VERSION

    parser.add_argument(
        "--version",
        action="version",
        version=f"{APP_NAME} v{APP_VERSION} - {APP_DESCRIPTION}",
    )

    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose logging"
    )

    args = parser.parse_args()

    # Update log level if verbose
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("Verbose logging enabled")

    # Validate input path
    input_path = Path(args.input)
    if not input_path.exists():
        logger.error(f"Input path does not exist: {input_path}")
        sys.exit(EXIT_CODES["GENERAL_ERROR"])

    # Create output directory
    output_dir = Path(args.output)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Initialize medical application
    app = ChromoForgeApp(
        user_id=args.user_id,
        session_id=args.session_id,
        enable_medical=args.medical,
        enable_database=args.database,
    )

    # Override configuration if specified
    if args.confidence_threshold:
        app.pii_detector.confidence_threshold = args.confidence_threshold
        logger.info(f"Confidence threshold set to: {args.confidence_threshold}")

    if args.obfuscation_method:
        app.pdf_obfuscator.method = ObfuscationMethod(args.obfuscation_method)
        logger.info(f"Obfuscation method set to: {args.obfuscation_method}")

    try:
        # Process based on input type
        if input_path.is_file():
            logger.info("Processing single file")
            result = await app.process_single_file(
                input_path, output_dir, enable_obfuscation=not args.no_obfuscation
            )

            if result["success"]:
                print(f"✓ Successfully processed: {input_path}")
                print(f"  Results saved to: {result['results_path']}")
                if result.get("obfuscation_result"):
                    print(
                        f"  Obfuscated PDF: {result['obfuscation_result']['output_path']}"
                    )
                if args.medical and result.get("extracted_fields"):
                    fields = result["extracted_fields"]
                    print(f"  Medical extraction:")
                    print(
                        f"    - Patient code: {fields.get('patient_code', 'Not found')}"
                    )
                    print(
                        f"    - Sample code: {fields.get('sample_code', 'Not found')}"
                    )
                    print(
                        f"    - Investigation: {fields.get('investigation', 'Not found')}"
                    )
                    print(
                        f"    - Thai name: {fields.get('patient_full_name_th', 'Not found')}"
                    )
                    print(
                        f"    - English name: {fields.get('patient_full_name_en', 'Not found')}"
                    )
                if args.database:
                    print(f"  Database integration:")
                    print(f"    - Document ID: {result.get('document_id', 'N/A')}")
                    print(f"    - Extraction ID: {result.get('extraction_id', 'N/A')}")
            else:
                print(f"✗ Failed to process: {input_path}")
                print(f"  Error: {result.get('error', 'Unknown error')}")
                if args.database and result.get("document_id"):
                    print(f"  Document ID: {result['document_id']} (status: failed)")
                sys.exit(EXIT_CODES["GENERAL_ERROR"])

        elif input_path.is_dir():
            logger.info("Processing directory")
            results = await app.process_directory(
                input_path,
                output_dir,
                pattern=args.pattern,
                recursive=not args.no_recursive,
                enable_obfuscation=not args.no_obfuscation,
            )

            stats = results["batch_stats"]
            print(f"✓ Batch processing completed:")
            print(f"  Total files: {stats.total_files}")
            print(f"  Successful: {stats.completed_files}")
            print(f"  Failed: {stats.failed_files}")
            print(
                f"  Success rate: {stats.completed_files / stats.total_files * 100:.1f}%"
            )
            print(f"  Average processing time: {stats.average_processing_time:.2f}s")
            print(f"  Total PII detected: {stats.total_pii_detected}")
            print(f"  Total PII obfuscated: {stats.total_pii_obfuscated}")

        else:
            logger.error(f"Input path is neither file nor directory: {input_path}")
            sys.exit(EXIT_CODES["GENERAL_ERROR"])

    except KeyboardInterrupt:
        logger.info("Processing interrupted by user")
        sys.exit(EXIT_CODES["KEYBOARD_INTERRUPT"])

    except Exception as e:
        logger.error(f"Application error: {str(e)}")
        sys.exit(EXIT_CODES["GENERAL_ERROR"])


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
