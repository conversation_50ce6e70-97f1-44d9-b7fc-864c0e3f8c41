# ChromoForge Docker Cleanup & AgentOS Compliance Strategy

**Strategy Date**: 2025-08-06  
**Baseline Recovery Target**: 32.86GB → <7GB (85.4% reduction)  
**AgentOS Compliance Target**: 45 → 85+ (89% improvement)  
**Security Priority**: CRITICAL - Exposed credentials remediation

## Executive Summary

### Current State Analysis
- **Total Docker Space**: 36.55GB
- **Reclaimable Space**: 32.86GB (89.9%)
- **Critical Issues**: Exposed API credentials, excessive build cache, orphaned resources
- **AgentOS Compliance**: 45/100 (requires 40-point improvement)

### Cleanup Strategy Overview
This strategy implements a **6-phase approach** with safety validations:
1. **Security Remediation** (CRITICAL)
2. **Configuration Optimization**
3. **Build System Refactoring**
4. **Resource Cleanup**
5. **Production Patterns**
6. **Validation & Monitoring**

---

## Phase 1: Security Remediation (CRITICAL)

### 1.1 Credential Exposure Fix

**IMMEDIATE ACTION REQUIRED**: Exposed API credentials detected in baseline

#### Current Security Vulnerabilities
```yaml
exposed_credentials:
  - GOOGLE_API_KEY: "AIzaSyBEZFqOH2dMX-fJ1jKGHaKT2rXr8Q4P6wM"
  - SUPABASE_SERVICE_ROLE_KEY: exposed in docker-compose.yml
  - Multiple API keys in .env files
  
risk_level: CRITICAL
impact: API abuse, unauthorized access, billing attacks
```

#### Security Implementation Strategy

**Step 1: Create .dockerignore Security Template**
```dockerignore
# ChromoForge Security .dockerignore Template
# CRITICAL: Prevent credential leakage into Docker images

# Environment files (NEVER COPY TO IMAGES)
.env*
*.env
.env.local
.env.production
.env.staging
.env.development
.env.test
.env.backup
.env.bak

# Secret directories
secrets/
.secrets/
keys/
certificates/
certs/

# API credentials and tokens
*.key
*.pem
*.crt
credentials.json
auth.json
*.token

# Sensitive data directories
original-pdf-examples/
processed/
batch-results/
test-results/
patient-data/

# Development artifacts
.git/
.github/
docs/
*.md
README*
CHANGELOG*
```

**Step 2: Docker Secrets Implementation**
```yaml
# New docker-compose.secrets.yml
services:
  chromoforge:
    secrets:
      - google_api_key
      - supabase_url
      - supabase_anon_key
      - supabase_service_key
    environment:
      - GOOGLE_API_KEY_FILE=/run/secrets/google_api_key
      - SUPABASE_URL_FILE=/run/secrets/supabase_url
      
secrets:
  google_api_key:
    file: ./secrets/google_api_key.txt
  supabase_url:
    file: ./secrets/supabase_url.txt
  supabase_anon_key:
    file: ./secrets/supabase_anon_key.txt
  supabase_service_key:
    file: ./secrets/supabase_service_key.txt
```

**Step 3: Environment Sanitization**
```bash
# Security cleanup commands
echo "Creating secure environment structure..."

# Create secrets directory with proper permissions
mkdir -p secrets
chmod 700 secrets

# Move credentials to secure files
echo "$GOOGLE_API_KEY" > secrets/google_api_key.txt
echo "$NEXT_PUBLIC_SUPABASE_URL" > secrets/supabase_url.txt
echo "$NEXT_PUBLIC_SUPABASE_ANON_KEY" > secrets/supabase_anon_key.txt
echo "$SUPABASE_SERVICE_ROLE_KEY" > secrets/supabase_service_key.txt

# Set restrictive permissions
chmod 600 secrets/*.txt

# Clear environment variables from .env files
cat > .env.template << 'EOF'
# ChromoForge Configuration Template
# Copy to .env and configure with your values

# Security Notice: Never commit actual credentials to version control
# Use Docker secrets for production deployments

# Google API Configuration
GOOGLE_API_KEY=your_api_key_here

# Supabase Configuration  
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key

# Application Settings
ENABLE_ULTRA_THINK=true
CONFIDENCE_THRESHOLD=0.7
MAX_CONCURRENT_REQUESTS=10
EOF

# Backup and clear existing .env
mv .env .env.backup.$(date +%Y%m%d_%H%M%S)
cp .env.template .env
```

### 1.2 Container Security Hardening

**Security Dockerfile Improvements**
```dockerfile
# Security-hardened Dockerfile template
FROM python:3.9-slim

# Security: Update system packages
RUN apt-get update && apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
    poppler-utils \
    ghostscript \
    libmagickwand-dev \
    tesseract-ocr \
    tesseract-ocr-tha && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Security: Create non-root user early
RUN groupadd -r chromoforge && \
    useradd -r -g chromoforge -m -d /app -s /bin/bash chromoforge

WORKDIR /app

# Security: Copy requirements first, install as root, then switch
COPY requirements.txt ./
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    rm requirements.txt

# Security: Copy application as non-root
COPY --chown=chromoforge:chromoforge . .

# Security: Remove any sensitive files that might have been copied
RUN rm -rf .env* secrets/ keys/ *.key *.pem

# Security: Set secure file permissions
RUN chmod 755 /app && \
    chmod -R 644 /app/* && \
    chmod 755 /app/src/main.py

# Security: Switch to non-root user
USER chromoforge

# Security: Use specific port
EXPOSE 8000

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

CMD ["python", "-m", "src.main", "--help"]
```

---

## Phase 2: Configuration Optimization

### 2.1 Multi-Stage Dockerfile Refactoring

**Current Issues**:
- Single-stage build: 2.26GB image size
- No build optimization
- Unnecessary dependencies in runtime image

**Optimized Multi-Stage Dockerfile**:
```dockerfile
# ChromoForge Multi-Stage Optimized Dockerfile
# Target: <500MB final image (78% size reduction)

# Build Stage
FROM python:3.9-slim as builder

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Install Python dependencies
COPY requirements.txt .
RUN pip install --user --no-cache-dir -r requirements.txt

# Production Stage
FROM python:3.9-slim as production

# Install only runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    poppler-utils \
    ghostscript \
    libmagickwand-dev \
    tesseract-ocr \
    tesseract-ocr-tha \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r chromoforge && \
    useradd -r -g chromoforge -m -d /app -s /bin/bash chromoforge

WORKDIR /app

# Copy Python packages from builder
COPY --from=builder /root/.local /home/<USER>/.local

# Copy application code
COPY --chown=chromoforge:chromoforge src/ ./src/
COPY --chown=chromoforge:chromoforge *.py ./

# Set PATH for user-installed packages
ENV PATH=/home/<USER>/.local/bin:$PATH

USER chromoforge

EXPOSE 8000

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; import src.main; sys.exit(0)" || exit 1

CMD ["python", "-m", "src.main", "--help"]
```

### 2.2 Docker Compose Optimization

**Issues Identified**:
- 44 environment variables (maintenance overhead)
- Verbose configuration
- Missing resource constraints
- No health checks for main service

**Optimized docker-compose.yml**:
```yaml
version: '3.8'

services:
  chromoforge:
    build:
      context: .
      dockerfile: Dockerfile
      cache_from:
        - chromoforge:${CHROMOFORGE_VERSION:-1.0.0}
      args:
        BUILDKIT_INLINE_CACHE: 1
    image: "chromoforge:${CHROMOFORGE_VERSION:-1.0.0}"
    container_name: chromoforge-app
    
    # Resource constraints (AgentOS compliance)
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.25'
    
    # Environment from file + essential overrides
    env_file:
      - .env
    environment:
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - MAX_CONCURRENT_REQUESTS=${MAX_CONCURRENT_REQUESTS:-10}
    
    # Minimal volume mounts (security)
    volumes:
      - ./processed:/app/processed
      - ./temp:/app/temp
      # Development mounts (conditional)
      - type: bind
        source: ./src
        target: /app/src
        read_only: true
        bind:
          create_host_path: false
    
    working_dir: /app
    restart: unless-stopped
    
    # Health check implementation
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; import src.main; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    networks:
      - chromoforge
    
    # Security options
    security_opt:
      - no-new-privileges:true
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=100m

  chromoforge-dashboard:
    build:
      context: ./dashboard
      dockerfile: Dockerfile
      target: ${DASHBOARD_TARGET:-development}
      cache_from:
        - chromoforge-dashboard:${CHROMOFORGE_VERSION:-1.0.0}
      args:
        BUILDKIT_INLINE_CACHE: 1
    image: "chromoforge-dashboard:${CHROMOFORGE_VERSION:-1.0.0}"
    container_name: chromoforge-dashboard
    
    # Resource constraints
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.1'
    
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - VITE_API_URL=http://chromoforge:8000
    
    ports:
      - "3001:3001"
    
    # Conditional development volumes
    volumes:
      - type: bind
        source: ./dashboard/src
        target: /app/src
        read_only: true
        bind:
          create_host_path: false
      # Named volume for node_modules performance
      - chromoforge-dashboard-modules:/app/node_modules
    
    networks:
      - chromoforge
    
    depends_on:
      chromoforge:
        condition: service_healthy
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Security options
    security_opt:
      - no-new-privileges:true

networks:
  chromoforge:
    driver: bridge
    name: chromoforge-network

volumes:
  chromoforge-dashboard-modules:
    name: chromoforge-dashboard-modules
```

### 2.3 Build Script Enhancement

**Enhanced docker-run.sh Features**:
```bash
#!/bin/bash
# ChromoForge AgentOS-Compliant Docker Runner v2.0.0
# Enhanced with cleanup capabilities and security checks

set -euo pipefail  # Strict error handling

# Colors and logging
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Security check function
check_security() {
    if [ -f ".env" ]; then
        if grep -q "AIzaSy" .env 2>/dev/null; then
            error "Exposed API credentials detected in .env file!"
            error "Run './docker-run.sh secure-env' to fix credential exposure"
            return 1
        fi
    fi
    return 0
}

# Enhanced cleanup functions
cleanup_build_cache() {
    log "Cleaning Docker build cache..."
    docker builder prune --all --force
    success "Build cache cleaned (estimated ~15GB recovered)"
}

cleanup_images() {
    log "Removing unused Docker images..."
    docker image prune --all --force
    success "Unused images removed (estimated ~17GB recovered)"
}

cleanup_volumes() {
    log "Removing unused Docker volumes..."
    docker volume prune --force
    success "Unused volumes removed (estimated ~173MB recovered)"
}

cleanup_containers() {
    log "Removing stopped containers..."
    docker container prune --force
    success "Stopped containers removed"
}

cleanup_networks() {
    log "Removing unused networks..."
    docker network prune --force
    success "Unused networks removed"
}

# Comprehensive cleanup function
cleanup_comprehensive() {
    log "Starting comprehensive Docker cleanup..."
    
    # Pre-cleanup validation
    if ! docker info > /dev/null 2>&1; then
        error "Docker is not running"
        return 1
    fi
    
    # Show current usage
    log "Current Docker space usage:"
    docker system df
    
    # Confirm cleanup
    read -p "Proceed with comprehensive cleanup? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log "Cleanup cancelled"
        return 0
    fi
    
    # Execute cleanup sequence
    cleanup_containers
    cleanup_images
    cleanup_volumes
    cleanup_networks
    cleanup_build_cache
    
    # Show results
    log "Post-cleanup Docker space usage:"
    docker system df
    
    success "Comprehensive cleanup completed!"
}

# Security environment setup
secure_env() {
    log "Setting up secure environment configuration..."
    
    if [ ! -f ".env.template" ]; then
        error ".env.template not found. Run security remediation first."
        return 1
    fi
    
    # Create secrets directory
    mkdir -p secrets
    chmod 700 secrets
    
    if [ -f ".env" ]; then
        # Backup existing .env
        backup_name=".env.backup.$(date +%Y%m%d_%H%M%S)"
        mv .env "$backup_name"
        log "Backed up .env to $backup_name"
    fi
    
    # Copy template
    cp .env.template .env
    
    success "Secure environment template created"
    warn "Please edit .env with your actual credentials"
    warn "Never commit .env to version control"
}

# AgentOS compliance check
agentOS_check() {
    log "Checking AgentOS compliance..."
    
    local score=0
    local max_score=100
    
    # Docker security checks
    if check_security; then
        score=$((score + 20))
        success "Security: No exposed credentials"
    else
        error "Security: Exposed credentials detected"
    fi
    
    # Dockerfile best practices
    if grep -q "USER" Dockerfile; then
        score=$((score + 10))
        success "Security: Non-root user configured"
    fi
    
    if grep -q "HEALTHCHECK" Dockerfile; then
        score=$((score + 10))
        success "Reliability: Health check configured"
    fi
    
    # Multi-stage build check
    if grep -q "FROM.*as.*" Dockerfile; then
        score=$((score + 15))
        success "Optimization: Multi-stage build detected"
    fi
    
    # Resource constraints check
    if grep -q "resources:" docker-compose.yml; then
        score=$((score + 15))
        success "Resources: Constraints configured"
    fi
    
    # Documentation check
    if [ -f "README.md" ] && [ -f "CLAUDE.md" ]; then
        score=$((score + 10))
        success "Documentation: Complete"
    fi
    
    # Version management
    if [ -f "VERSION" ]; then
        score=$((score + 10))
        success "Versioning: VERSION file present"
    fi
    
    # Display results
    log "AgentOS Compliance Score: $score/$max_score"
    
    if [ $score -ge 85 ]; then
        success "AgentOS compliance achieved! (85+)"
    elif [ $score -ge 70 ]; then
        warn "Good compliance, room for improvement"
    else
        error "Low compliance score, immediate attention needed"
    fi
}

# Main command router with new functions
case "${1:-help}" in
    # Existing commands... (build, start, stop, etc.)
    
    # New cleanup commands
    cleanup-cache)
        cleanup_build_cache
        ;;
    cleanup-images)
        cleanup_images
        ;;
    cleanup-comprehensive)
        cleanup_comprehensive
        ;;
        
    # Security commands
    secure-env)
        secure_env
        ;;
    security-check)
        check_security && success "No security issues detected"
        ;;
        
    # AgentOS commands
    agentOS-check)
        agentOS_check
        ;;
        
    # System information
    system-info)
        log "Docker system information:"
        docker system df
        docker images
        docker ps -a
        ;;
        
    *)
        show_help
        ;;
esac
```

---

## Phase 3: Build System Refactoring

### 3.1 BuildKit Optimization

**Current Issues**:
- 15.18GB build cache (100% reclaimable)
- 173 cache entries for minimal changes
- Poor layer caching efficiency

**BuildKit Enhancement Strategy**:
```yaml
# .docker/buildkit.toml
[worker.oci]
  enabled = true
  
[worker.containerd]
  enabled = false

# Build cache optimization
[registry."docker.io"]
  mirrors = ["mirror.gcr.io"]

# Advanced caching
[worker.oci.gc]
  policy = [
    {keep = "10%", filter = "type==exec.cachemount"},
    {keep = "50%", filter = "type==source.local"}
  ]
```

**Optimized Build Commands**:
```bash
# Enhanced build process
export DOCKER_BUILDKIT=1
export BUILDKIT_PROGRESS=plain

# Build with advanced caching
docker buildx build \
  --platform linux/amd64 \
  --cache-from type=local,src=/tmp/.buildx-cache \
  --cache-to type=local,dest=/tmp/.buildx-cache-new,mode=max \
  --tag chromoforge:${VERSION} \
  --tag chromoforge:latest \
  .

# Rotate cache to prevent growth
rm -rf /tmp/.buildx-cache
mv /tmp/.buildx-cache-new /tmp/.buildx-cache
```

### 3.2 Layer Optimization

**Dockerfile Layer Strategy**:
```dockerfile
# Optimized layer structure for caching
FROM python:3.9-slim as base

# Layer 1: System packages (rarely changes)
RUN apt-get update && apt-get install -y --no-install-recommends \
    poppler-utils ghostscript libmagickwand-dev \
    tesseract-ocr tesseract-ocr-tha \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Layer 2: Python base packages (changes infrequently)
COPY requirements-base.txt .
RUN pip install --no-cache-dir -r requirements-base.txt

# Layer 3: Application-specific packages (changes more frequently)
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Layer 4: User creation (rarely changes)
RUN groupadd -r chromoforge && useradd -r -g chromoforge chromoforge

# Layer 5: Application code (changes frequently - should be last)
COPY --chown=chromoforge:chromoforge . .

USER chromoforge
```

### 3.3 Multi-Architecture Support

**Cross-Platform Build Configuration**:
```bash
# Setup buildx for multi-arch
docker buildx create --name chromoforge-builder --use
docker buildx inspect --bootstrap

# Multi-arch build
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  --tag chromoforge:${VERSION} \
  --push \
  .
```

---

## Phase 4: Resource Cleanup Execution

### 4.1 Safe Cleanup Sequence

**Pre-Cleanup Validation**:
```bash
#!/bin/bash
# ChromoForge Safe Cleanup Protocol

set -euo pipefail

# Pre-cleanup system check
pre_cleanup_check() {
    log "Performing pre-cleanup validation..."
    
    # Check Docker daemon
    if ! docker info > /dev/null 2>&1; then
        error "Docker daemon not running"
        exit 1
    fi
    
    # Check for running services
    if docker-compose ps -q | head -n 1 | read; then
        warn "ChromoForge services are running"
        read -p "Stop services before cleanup? (y/N): " confirm
        if [[ $confirm =~ ^[Yy]$ ]]; then
            docker-compose down
        else
            error "Cannot proceed with services running"
            exit 1
        fi
    fi
    
    # Create backup metadata
    mkdir -p .cleanup-backup
    docker images > .cleanup-backup/images-before.txt
    docker ps -a > .cleanup-backup/containers-before.txt
    docker system df > .cleanup-backup/usage-before.txt
    
    success "Pre-cleanup validation completed"
}

# Staged cleanup execution
execute_cleanup() {
    log "Executing staged cleanup..."
    
    # Stage 1: Containers (lowest risk)
    log "Stage 1: Cleaning containers..."
    docker container prune --force
    
    # Stage 2: Networks (low risk)
    log "Stage 2: Cleaning networks..."
    docker network prune --force
    
    # Stage 3: Volumes (medium risk - data loss possible)
    log "Stage 3: Cleaning volumes..."
    warn "This may remove persistent data"
    read -p "Continue with volume cleanup? (y/N): " confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        docker volume prune --force
    fi
    
    # Stage 4: Images (medium risk - rebuild required)
    log "Stage 4: Cleaning images..."
    docker image prune --all --force
    
    # Stage 5: Build cache (high impact, low risk)
    log "Stage 5: Cleaning build cache..."
    docker builder prune --all --force
    
    success "Staged cleanup completed"
}

# Post-cleanup validation
post_cleanup_check() {
    log "Performing post-cleanup validation..."
    
    # Capture post-cleanup state
    docker images > .cleanup-backup/images-after.txt
    docker ps -a > .cleanup-backup/containers-after.txt
    docker system df > .cleanup-backup/usage-after.txt
    
    # Calculate space recovered
    local before_size=$(docker system df --format "table {{.TotalCount}}\t{{.Size}}" | tail -n +2 | awk '{print $2}' | head -n 1)
    local after_size=$(docker system df --format "table {{.TotalCount}}\t{{.Size}}" | tail -n +2 | awk '{print $2}' | head -n 1)
    
    log "Space recovery analysis:"
    echo "Before: $before_size"
    echo "After: $after_size"
    
    # Test build capability
    log "Testing build capability..."
    if docker build -t chromoforge-test . > /dev/null 2>&1; then
        success "Build capability verified"
        docker rmi chromoforge-test > /dev/null 2>&1
    else
        error "Build capability compromised - investigate"
    fi
    
    success "Post-cleanup validation completed"
}

# Rollback procedure
rollback_cleanup() {
    error "Cleanup rollback requested"
    
    # Note: Full rollback not possible for cleaned resources
    # This is preventive documentation
    log "Rollback limitations:"
    echo "- Cleaned build cache cannot be restored"
    echo "- Removed images must be rebuilt"
    echo "- Deleted volumes cannot be recovered"
    
    # Restart services
    log "Restarting ChromoForge services..."
    docker-compose up -d
    
    warn "Manual intervention may be required"
}

# Main cleanup execution
main() {
    case "${1:-check}" in
        execute)
            pre_cleanup_check
            execute_cleanup
            post_cleanup_check
            ;;
        rollback)
            rollback_cleanup
            ;;
        check)
            pre_cleanup_check
            ;;
        *)
            echo "Usage: $0 {check|execute|rollback}"
            exit 1
            ;;
    esac
}

main "$@"
```

### 4.2 Cleanup Command Reference

**Space Recovery Commands**:
```bash
# Quick cleanup (safe)
./docker-run.sh cleanup-cache        # ~15GB recovery
./docker-run.sh cleanup-containers   # ~9MB recovery

# Comprehensive cleanup (requires rebuild)
./docker-run.sh cleanup-comprehensive # ~32GB recovery

# Selective cleanup
docker system prune --volumes --all --force # Everything except secrets

# Build cache only (safest)
docker builder prune --all --force

# Images only (requires rebuild)
docker image prune --all --force

# Volumes only (data loss risk)
docker volume prune --force
```

### 4.3 Recovery Verification

**Post-Cleanup Testing**:
```bash
# Functional test sequence
test_cleanup_success() {
    log "Testing cleanup success..."
    
    # Test 1: Build capability
    if docker build -t chromoforge-test .; then
        success "Build test passed"
        docker rmi chromoforge-test
    else
        error "Build test failed"
        return 1
    fi
    
    # Test 2: Service startup
    if docker-compose up -d; then
        success "Service startup test passed"
        
        # Test 3: Health checks
        sleep 30
        if docker-compose ps | grep -q "healthy"; then
            success "Health check test passed"
        else
            warn "Health check test failed - check logs"
        fi
        
        docker-compose down
    else
        error "Service startup test failed"
        return 1
    fi
    
    # Test 4: Space recovery verification
    local current_usage=$(docker system df --format "table {{.Size}}" | tail -n +2 | head -n 1)
    log "Current Docker usage: $current_usage"
    
    success "Cleanup verification completed"
}
```

---

## Phase 5: Production-Ready Patterns

### 5.1 Semantic Versioning Implementation

**Version Management Strategy**:
```bash
# VERSION file format
echo "1.0.0" > VERSION

# Git tag alignment
git tag -a v1.0.0 -m "ChromoForge v1.0.0 - Production Ready"

# Docker image versioning
export CHROMOFORGE_VERSION=$(cat VERSION)
docker build -t chromoforge:${CHROMOFORGE_VERSION} .
docker tag chromoforge:${CHROMOFORGE_VERSION} chromoforge:latest

# Semantic version bumping
bump_version() {
    local type=${1:-patch}  # major, minor, patch
    local current=$(cat VERSION)
    
    case $type in
        major)
            new_version=$(echo $current | awk -F. '{print $1+1".0.0"}')
            ;;
        minor)
            new_version=$(echo $current | awk -F. '{print $1"."$2+1".0"}')
            ;;
        patch)
            new_version=$(echo $current | awk -F. '{print $1"."$2"."$3+1}')
            ;;
    esac
    
    echo $new_version > VERSION
    git add VERSION
    git commit -m "Bump version to $new_version"
    git tag -a v$new_version -m "ChromoForge v$new_version"
}
```

### 5.2 Resource Constraints & Limits

**Production Resource Configuration**:
```yaml
# docker-compose.prod.yml
services:
  chromoforge:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.25'
      restart_policy:
        condition: unless-stopped
        delay: 5s
        max_attempts: 3
        window: 120s
    
    # Production health checks
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Security hardening
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    cap_drop:
      - ALL
    cap_add:
      - SETUID
      - SETGID
```

### 5.3 Health Monitoring Implementation

**Health Check Endpoints**:
```python
# src/health.py - Health check implementation
from fastapi import FastAPI, HTTPException
import psutil
import os

app = FastAPI()

@app.get("/health")
async def health_check():
    """Comprehensive health check endpoint"""
    try:
        # Check system resources
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        health_status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": os.getenv("CHROMOFORGE_VERSION", "unknown"),
            "resources": {
                "memory_percent": memory.percent,
                "disk_percent": disk.percent,
                "cpu_percent": psutil.cpu_percent()
            }
        }
        
        # Check critical thresholds
        if memory.percent > 90:
            health_status["status"] = "degraded"
            health_status["warnings"] = ["High memory usage"]
        
        if disk.percent > 85:
            health_status["status"] = "degraded" 
            health_status["warnings"] = health_status.get("warnings", []) + ["Low disk space"]
        
        return health_status
        
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Health check failed: {str(e)}")

@app.get("/ready")
async def readiness_check():
    """Readiness probe for Kubernetes/container orchestration"""
    # Check database connectivity
    # Check API key availability
    # Check required directories
    return {"status": "ready"}
```

---

## Phase 6: Validation & Monitoring

### 6.1 Continuous Monitoring Setup

**Docker Monitoring Script**:
```bash
#!/bin/bash
# ChromoForge Docker Monitoring

monitor_docker_health() {
    local threshold_gb=10
    local current_usage_gb
    
    # Get current usage in GB
    current_usage_gb=$(docker system df --format "{{.Size}}" | head -n1 | sed 's/GB.*//' | sed 's/MB.*/0.1/')
    
    # Check if usage exceeds threshold
    if (( $(echo "$current_usage_gb > $threshold_gb" | bc -l) )); then
        warn "Docker space usage high: ${current_usage_gb}GB"
        
        # Auto cleanup if enabled
        if [[ "${AUTO_CLEANUP:-false}" == "true" ]]; then
            log "Auto-cleanup enabled, cleaning build cache..."
            docker builder prune --force --filter until=24h
        else
            log "Consider running: ./docker-run.sh cleanup-cache"
        fi
    fi
    
    # Check for zombie containers
    local zombie_count=$(docker ps -aq -f status=exited | wc -l)
    if [[ $zombie_count -gt 0 ]]; then
        warn "$zombie_count zombie containers detected"
        log "Run: docker container prune"
    fi
    
    # Check for unused volumes
    local unused_volumes=$(docker volume ls -qf dangling=true | wc -l)
    if [[ $unused_volumes -gt 0 ]]; then
        warn "$unused_volumes unused volumes detected"
        log "Run: docker volume prune"
    fi
}

# AgentOS compliance monitoring
monitor_compliance() {
    local score=0
    
    # Security checks
    if ! grep -q "AIzaSy" .env 2>/dev/null; then
        score=$((score + 20))
    fi
    
    # Resource constraints
    if grep -q "resources:" docker-compose.yml; then
        score=$((score + 15))
    fi
    
    # Health checks
    if grep -q "healthcheck:" docker-compose.yml; then
        score=$((score + 15))
    fi
    
    # Multi-stage builds
    if grep -q "FROM.*as.*" Dockerfile; then
        score=$((score + 15))
    fi
    
    # Version management
    if [[ -f "VERSION" ]]; then
        score=$((score + 10))
    fi
    
    # Documentation
    if [[ -f "README.md" && -f "CLAUDE.md" ]]; then
        score=$((score + 10))
    fi
    
    # Non-root user
    if grep -q "USER" Dockerfile; then
        score=$((score + 10))
    fi
    
    # Security hardening
    if grep -q "no-new-privileges" docker-compose.yml; then
        score=$((score + 5))
    fi
    
    log "AgentOS Compliance Score: $score/100"
    
    if [[ $score -lt 85 ]]; then
        warn "Compliance below target (85+)"
    else
        success "Compliance target achieved!"
    fi
}

# Main monitoring loop
while true; do
    log "Running Docker health monitoring..."
    monitor_docker_health
    monitor_compliance
    sleep 3600  # Check hourly
done
```

### 6.2 Success Metrics Tracking

**Metrics Collection Dashboard**:
```yaml
# metrics.yml - Success tracking
baseline_metrics:
  docker_space_gb: 36.55
  reclaimable_gb: 32.86
  image_count: 10
  unused_images: 7
  build_cache_entries: 173
  agentOS_score: 45

target_metrics:
  docker_space_gb: 7
  reclaimable_gb: 1
  image_count: 4
  unused_images: 0
  build_cache_entries: 20
  agentOS_score: 85

current_metrics:
  docker_space_gb: # Updated by monitoring
  reclaimable_gb: # Updated by monitoring
  image_count: # Updated by monitoring
  unused_images: # Updated by monitoring
  build_cache_entries: # Updated by monitoring
  agentOS_score: # Updated by monitoring

success_criteria:
  space_recovery_percent: 80  # 32.86GB -> 6.57GB recovery target
  compliance_improvement: 40  # 45 -> 85 point improvement
  security_issues_resolved: 100  # 0 exposed credentials
  build_performance_improvement: 30  # 30% faster builds
```

---

## Implementation Timeline

### Week 1: Security & Foundation
- **Day 1-2**: Security remediation (exposed credentials)
- **Day 3-4**: .dockerignore implementation
- **Day 5-7**: Docker secrets setup and testing

### Week 2: Configuration Optimization  
- **Day 8-10**: Multi-stage Dockerfile implementation
- **Day 11-12**: Docker Compose optimization
- **Day 13-14**: Build script enhancement

### Week 3: Build System & Cleanup
- **Day 15-17**: BuildKit optimization
- **Day 18-19**: Safe cleanup execution
- **Day 20-21**: Validation and testing

### Week 4: Production Patterns
- **Day 22-24**: Resource constraints and health checks
- **Day 25-26**: Monitoring implementation
- **Day 27-28**: AgentOS compliance validation

---

## Risk Mitigation

### High-Risk Mitigation
- **Data Loss**: Volume backup before cleanup
- **Service Disruption**: Staged cleanup with rollback plan
- **Security Exposure**: Immediate credential rotation
- **Build Failure**: Multi-stage fallback strategies

### Rollback Procedures
- **Emergency Rollback**: Original configuration restoration
- **Partial Rollback**: Stage-specific reversal
- **Data Recovery**: Volume snapshot restoration
- **Service Recovery**: Health check validation

---

## Success Validation

### Primary Success Criteria
- ✅ **Space Recovery**: 32.86GB → <7GB (>80% reduction)
- ✅ **Security**: Zero exposed credentials
- ✅ **AgentOS Compliance**: 45 → 85+ points
- ✅ **Build Performance**: 30% improvement

### Secondary Success Criteria  
- ✅ **Image Optimization**: 2.26GB → <500MB per image
- ✅ **Resource Efficiency**: Defined limits and constraints
- ✅ **Development Experience**: Enhanced CLI and workflows
- ✅ **Documentation**: Complete security and deployment guides

---

**Strategy Status**: READY FOR IMPLEMENTATION  
**Risk Level**: MEDIUM (with proper staging and validation)  
**Expected Outcome**: 85%+ space recovery, 89% compliance improvement, zero security exposures