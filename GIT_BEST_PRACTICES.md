# Git Best Practices v1.0.0

## 🎯 Overview

ChromoForge is a medical document processing application handling sensitive healthcare data. These Git practices ensure code quality, security, and compliance while maintaining development velocity.

## 🔒 Core Principles

### Medical Software Standards
- **Zero tolerance** for security vulnerabilities in commits
- **Audit trail** for all changes affecting medical data processing
- **Compliance** with healthcare data protection requirements
- **Reproducible builds** for regulatory validation

### Quality Gates
- All commits must pass automated tests
- Code review required for all changes
- Security scanning on every push
- Documentation updates with feature changes

## 🌳 Branching Strategy

### Branch Structure
```
main (production)
├── staging (pre-production testing)
├── dev (integration branch)
│   ├── feature/medical-extraction-v2
│   ├── feature/pii-detection-enhancement
│   ├── hotfix/security-patch-001
│   └── release/v1.1.0
```

### Branch Types

#### 1. Main Branches
- **`main`**: Production-ready code only
- **`staging`**: Pre-production testing environment
- **`dev`**: Integration branch for feature development

#### 2. Supporting Branches
- **`feature/*`**: New features and enhancements
- **`hotfix/*`**: Critical production fixes
- **`release/*`**: Release preparation
- **`security/*`**: Security-related changes

### Workflow Process

#### Feature Development
```bash
# 1. Start from dev branch
git checkout dev
git pull origin dev

# 2. Create feature branch
git checkout -b feature/medical-extraction-enhancement

# 3. Develop with frequent commits
git add .
git commit -m "feat(extraction): add Thai ID validation"

# 4. Push and create PR
git push origin feature/medical-extraction-enhancement
```

#### Hotfix Process
```bash
# 1. Start from main for critical fixes
git checkout main
git pull origin main

# 2. Create hotfix branch
git checkout -b hotfix/security-patch-001

# 3. Fix and test
git commit -m "fix(security): patch PII detection vulnerability"

# 4. Merge to main AND dev
git checkout main
git merge hotfix/security-patch-001
git checkout dev
git merge hotfix/security-patch-001
```

## 📝 Commit Standards

### Conventional Commits Format
```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

### Commit Types
- **feat**: New features
- **fix**: Bug fixes
- **docs**: Documentation changes
- **style**: Code formatting (no logic changes)
- **refactor**: Code restructuring
- **test**: Adding or updating tests
- **chore**: Maintenance tasks
- **security**: Security-related changes
- **perf**: Performance improvements

### Medical Context Scopes
- **extraction**: OCR and data extraction
- **pii**: PII detection and obfuscation
- **database**: Supabase integration
- **security**: Security features
- **audit**: Audit logging
- **validation**: Data validation
- **api**: API endpoints
- **ui**: User interface

### Examples
```bash
# Feature commits
git commit -m "feat(extraction): add Gemini 2.5 Pro integration"
git commit -m "feat(pii): implement coordinate-based obfuscation"

# Bug fixes
git commit -m "fix(database): resolve connection timeout issues"
git commit -m "fix(security): patch authentication bypass"

# Documentation
git commit -m "docs(api): update medical extraction endpoints"

# Security
git commit -m "security(pii): enhance detection patterns for Thai IDs"
```

## 🏷️ Version Management

### Semantic Versioning
ChromoForge uses semantic versioning (MAJOR.MINOR.PATCH):
- **MAJOR**: Breaking changes or major feature releases
- **MINOR**: New features, backward compatible
- **PATCH**: Bug fixes, security patches

### Version Commands
```bash
# Check current version
./scripts/version.sh show

# Bump version
./scripts/version.sh bump patch    # 1.0.0 → 1.0.1
./scripts/version.sh bump minor    # 1.0.1 → 1.1.0
./scripts/version.sh bump major    # 1.1.0 → 2.0.0

# Set specific version
./scripts/version.sh set 1.2.3

# Build with version
./scripts/version.sh build
```

### Release Process
```bash
# 1. Create release branch
git checkout dev
git checkout -b release/v1.1.0

# 2. Update version
./scripts/version.sh set 1.1.0

# 3. Update changelog and docs
git add VERSION CHANGELOG.md
git commit -m "chore(release): prepare v1.1.0"

# 4. Merge to staging for testing
git checkout staging
git merge release/v1.1.0

# 5. After testing, merge to main
git checkout main
git merge release/v1.1.0
git tag v1.1.0

# 6. Merge back to dev
git checkout dev
git merge main
```

## 🔐 Security Practices

### Sensitive Data Protection
```bash
# Verify .gitignore before commits
git status --ignored

# Check for secrets before commit
git diff --cached | grep -E "(api_key|password|secret|token)"

# Use git-secrets (recommended)
git secrets --scan
```

### Required .gitignore Patterns
```gitignore
# Environment files
.env
.env.local
.env.*.local

# Secrets and keys
secrets/
keys/
certificates/
*.pem
*.key

# Medical data
processed/
batch-results/
test-results/
*.pdf
*.jpg
*.jpeg
*.png
*.tiff

# Database files
*.db
*.sqlite
*.sqlite3
```

### Pre-commit Security Checks
```bash
# Install pre-commit hooks
pip install pre-commit
pre-commit install

# Manual security scan
bandit -r src/
safety check
```

## 👥 Code Review Process

### Review Requirements
- **Mandatory** for all changes to main/staging/dev
- **Two approvals** required for security-related changes
- **Medical expert review** for extraction algorithm changes
- **Security review** for PII handling modifications

### Review Checklist
- [ ] Code follows style guidelines
- [ ] Tests added/updated for new functionality
- [ ] Documentation updated
- [ ] No hardcoded secrets or credentials
- [ ] PII handling follows security standards
- [ ] Performance impact assessed
- [ ] Error handling implemented
- [ ] Logging added for audit trail

### PR Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Security fix
- [ ] Documentation update
- [ ] Performance improvement

## Medical Data Impact
- [ ] No medical data handling changes
- [ ] New PII detection patterns
- [ ] Modified extraction algorithms
- [ ] Database schema changes

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Security tests pass
- [ ] Manual testing completed

## Security Review
- [ ] No new secrets in code
- [ ] PII handling reviewed
- [ ] Access controls verified
- [ ] Audit logging implemented
```

## 🚀 CI/CD Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/ci.yml
name: ChromoForge CI/CD

on:
  push:
    branches: [main, staging, dev]
  pull_request:
    branches: [main, staging, dev]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Build Docker image
        run: docker-compose build
      - name: Run tests
        run: docker-compose run chromoforge pytest
      - name: Security scan
        run: docker-compose run chromoforge bandit -r src/
      - name: Check secrets
        run: docker-compose run chromoforge git-secrets --scan
```

### Quality Gates
1. **Code formatting** (black, isort)
2. **Linting** (flake8, pylint)
3. **Type checking** (mypy)
4. **Security scanning** (bandit, safety)
5. **Test coverage** (>80% required)
6. **Integration tests** (real API calls)
7. **Performance tests** (benchmark validation)

## 🔧 Git Hooks Setup

### Pre-commit Hook
```bash
#!/bin/sh
# .git/hooks/pre-commit

# Format code
docker-compose run chromoforge black src/ tests/
docker-compose run chromoforge isort src/ tests/

# Run linting
docker-compose run chromoforge flake8 src/ tests/

# Security scan
docker-compose run chromoforge bandit -r src/

# Check for secrets
docker-compose run chromoforge git-secrets --scan

# Run tests
docker-compose run chromoforge pytest tests/
```

### Pre-push Hook
```bash
#!/bin/sh
# .git/hooks/pre-push

# Run full test suite
docker-compose run chromoforge pytest

# Security validation
docker-compose run chromoforge safety check

# Performance tests
docker-compose run chromoforge pytest -m performance
```

## 🚨 Emergency Procedures

### Hotfix Deployment
```bash
# 1. Immediate fix
git checkout main
git checkout -b hotfix/critical-security-fix

# 2. Apply fix and test
# ... make changes ...
git commit -m "security(critical): fix authentication bypass"

# 3. Fast-track review and merge
git push origin hotfix/critical-security-fix
# Create PR with "URGENT" label

# 4. Deploy immediately after merge
git checkout main
git pull origin main
./scripts/version.sh bump patch
git tag v1.0.1
```

### Rollback Procedure
```bash
# 1. Identify last known good version
git log --oneline

# 2. Create rollback branch
git checkout -b rollback/to-v1.0.0

# 3. Reset to previous version
git reset --hard v1.0.0

# 4. Force push (with caution)
git push origin rollback/to-v1.0.0 --force
```

## 📊 Monitoring & Metrics

### Git Metrics to Track
- Commit frequency and size
- Code review turnaround time
- Security scan results
- Test coverage trends
- Deployment frequency
- Rollback frequency

### Audit Requirements
- All commits signed with GPG
- Detailed commit messages
- PR approval trail
- Security scan results
- Test execution logs

## 🤝 Collaboration Guidelines

### Team Workflows
- **Daily**: Sync dev branch with latest changes
- **Weekly**: Review and merge feature branches
- **Monthly**: Security audit and dependency updates
- **Quarterly**: Major version planning

### Communication
- Use PR comments for technical discussions
- Tag relevant team members for reviews
- Update project board with progress
- Document decisions in commit messages

---

**Version**: 1.0.0  
**Last Updated**: 2025-01-05  
**Next Review**: 2025-04-05
