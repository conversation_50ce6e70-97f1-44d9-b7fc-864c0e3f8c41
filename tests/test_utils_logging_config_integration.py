"""Comprehensive unit tests for utils logging_config module using real data and integrations.

This test suite validates all functions in src/utils/logging_config.py using:
- Real log file operations and directory creation
- Real JSON and structured logging with Thai Unicode
- Real hospital logging scenarios with multiple components
- Real logging performance and rotation testing
- Real structlog integration and configuration
"""

import json
import logging
import logging.config
import tempfile
import time
from pathlib import Path
from typing import Any, Dict
from unittest.mock import MagicMock, patch

import pytest
import structlog
from pythonjsonlogger.json import JsonFormatter

from src.core.config import Settings
from src.utils.logging_config import (OCRLogger, get_json_logging_config,
                                      get_standard_logging_config,
                                      setup_logging)


class TestSetupLogging:
    """Test setup_logging function with real hospital scenarios."""

    def test_setup_logging_json_format_integration(self):
        """Test logging setup with JSON format for real hospital deployment."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Change to temp directory to create logs there
            original_cwd = Path.cwd()

            try:
                import os

                os.chdir(temp_dir)

                # Mock settings for JSON logging
                with patch("src.utils.logging_config.settings") as mock_settings:
                    mock_settings.log_format = "json"
                    mock_settings.log_level = "INFO"

                    # Setup logging
                    setup_logging()

                    # Verify logs directory was created
                    logs_dir = Path(temp_dir) / "logs"
                    assert logs_dir.exists()
                    assert logs_dir.is_dir()

                    # Test actual logging with Thai hospital data
                    logger = logging.getLogger("test_hospital_logger")

                    # Log Thai hospital events
                    logger.info("เริ่มต้นการประมวลผล OCR สำหรับผู้ป่วย HN-2024-001234")
                    logger.warning("พบข้อมูล PII ในเอกสารผลตรวจ: นายสมชาย ใจดี")
                    logger.error(
                        "ข้อผิดพลาดในการเชื่อมต่อ Gemini API",
                        extra={
                            "patient_id": "HN-2024-001234",
                            "hospital_dept": "cardiology",
                            "error_code": "API_TIMEOUT",
                        },
                    )

                    # Verify log files were created
                    expected_files = [
                        "chromoforge.jsonl",
                        "errors.jsonl",
                        "ocr_processing.jsonl",
                    ]
                    for log_file in expected_files:
                        log_path = logs_dir / log_file
                        assert log_path.exists(), f"Log file {log_file} not created"

            finally:
                os.chdir(original_cwd)

    def test_setup_logging_standard_format_integration(self):
        """Test logging setup with standard format for real development."""
        with tempfile.TemporaryDirectory() as temp_dir:
            original_cwd = Path.cwd()

            try:
                import os

                os.chdir(temp_dir)

                with patch("src.utils.logging_config.settings") as mock_settings:
                    mock_settings.log_format = "standard"
                    mock_settings.log_level = "DEBUG"

                    setup_logging()

                    # Verify logs directory
                    logs_dir = Path(temp_dir) / "logs"
                    assert logs_dir.exists()

                    # Test logging with real medical scenarios
                    logger = logging.getLogger("test_medical_processor")

                    logger.debug("Starting PDF analysis for cardiac report")
                    logger.info("PII detection completed: 5 items found")
                    logger.warning("Obfuscation method BLACK_BOX applied")
                    logger.error("Failed to process page 3 of medical record")

            finally:
                os.chdir(original_cwd)

    def test_setup_logging_external_library_levels_integration(self):
        """Test that external library log levels are properly configured."""
        with tempfile.TemporaryDirectory() as temp_dir:
            original_cwd = Path.cwd()

            try:
                import os

                os.chdir(temp_dir)

                with patch("src.utils.logging_config.settings") as mock_settings:
                    mock_settings.log_format = "json"
                    mock_settings.log_level = "DEBUG"

                    setup_logging()

                    # Verify external library log levels
                    assert logging.getLogger("httpx").level == logging.WARNING
                    assert logging.getLogger("google").level == logging.WARNING
                    assert logging.getLogger("urllib3").level == logging.WARNING

                    # Test that these loggers don't flood with debug messages
                    httpx_logger = logging.getLogger("httpx")
                    google_logger = logging.getLogger("google")
                    urllib3_logger = logging.getLogger("urllib3")

                    # These should not appear in logs due to WARNING level
                    httpx_logger.debug("HTTP request debug info")
                    google_logger.debug("Google API debug info")
                    urllib3_logger.debug("urllib3 debug info")

                    # These should appear in logs
                    httpx_logger.warning("HTTP connection warning")
                    google_logger.error("Google API error")
                    urllib3_logger.warning("urllib3 connection warning")

            finally:
                os.chdir(original_cwd)


class TestJSONLoggingConfig:
    """Test JSON logging configuration with real hospital data."""

    def test_get_json_logging_config_structure_integration(self):
        """Test JSON logging configuration structure for hospital deployment."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir) / "hospital_logs"

            # Mock settings
            with patch("src.utils.logging_config.settings") as mock_settings:
                mock_settings.log_level = "INFO"

                config = get_json_logging_config(log_dir)

                # Verify configuration structure
                assert config["version"] == 1
                assert config["disable_existing_loggers"] is False

                # Verify formatters
                assert "json" in config["formatters"]
                assert "simple" in config["formatters"]

                # Verify JSON formatter
                json_formatter = config["formatters"]["json"]
                assert json_formatter["()"] == JsonFormatter
                assert "%(asctime)s" in json_formatter["format"]
                assert "%(message)s" in json_formatter["format"]

                # Verify handlers
                expected_handlers = ["console", "file_json", "error_file", "ocr_file"]
                for handler in expected_handlers:
                    assert handler in config["handlers"]

                # Verify file paths
                assert (
                    str(log_dir / "chromoforge.jsonl")
                    == config["handlers"]["file_json"]["filename"]
                )
                assert (
                    str(log_dir / "errors.jsonl")
                    == config["handlers"]["error_file"]["filename"]
                )
                assert (
                    str(log_dir / "ocr_processing.jsonl")
                    == config["handlers"]["ocr_file"]["filename"]
                )

    def test_json_logging_integration_hospital_data(self):
        """Test JSON logging with real Thai hospital data."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir) / "test_logs"
            log_dir.mkdir()

            with patch("src.utils.logging_config.settings") as mock_settings:
                mock_settings.log_level = "DEBUG"

                config = get_json_logging_config(log_dir)
                logging.config.dictConfig(config)

                # Test with real hospital scenarios
                logger = logging.getLogger("src.ocr_processor")

                # Log Thai hospital events
                logger.info(
                    "ประมวลผลเอกสารผู้ป่วย",
                    extra={
                        "patient_hn": "HN-2024-001234",
                        "document_type": "lab_results",
                        "hospital_dept": "โรงพยาบาลจุฬาลงกรณ์",
                        "doctor_name": "นายแพทย์สมชาย ใจดี",
                        "pii_detected": ["thai_national_id", "patient_name"],
                        "confidence_scores": {
                            "thai_national_id": 0.95,
                            "patient_name": 0.87,
                        },
                    },
                )

                logger.error(
                    "ข้อผิดพลาดในการอ่าน PDF",
                    extra={
                        "file_path": "/hospital/records/cardiac_report.pdf",
                        "error_type": "PDF_CORRUPTED",
                        "page_number": 3,
                        "hospital_system": "HIS_integration",
                    },
                )

                # Verify log files were created and contain JSON
                json_log_file = log_dir / "chromoforge.jsonl"
                ocr_log_file = log_dir / "ocr_processing.jsonl"

                assert json_log_file.exists()
                assert ocr_log_file.exists()

                # Read and verify JSON format
                with open(json_log_file, "r", encoding="utf-8") as f:
                    log_lines = f.readlines()

                for line in log_lines:
                    if line.strip():
                        log_entry = json.loads(line.strip())
                        assert "asctime" in log_entry
                        assert "name" in log_entry
                        assert "levelname" in log_entry
                        assert "message" in log_entry


class TestStandardLoggingConfig:
    """Test standard logging configuration with real scenarios."""

    def test_get_standard_logging_config_structure_integration(self):
        """Test standard logging configuration for development environment."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir) / "dev_logs"

            with patch("src.utils.logging_config.settings") as mock_settings:
                mock_settings.log_level = "DEBUG"

                config = get_standard_logging_config(log_dir)

                # Verify configuration structure
                assert config["version"] == 1
                assert config["disable_existing_loggers"] is False

                # Verify formatters
                assert "detailed" in config["formatters"]
                assert "simple" in config["formatters"]

                # Verify detailed formatter includes file info
                detailed_format = config["formatters"]["detailed"]["format"]
                assert "%(filename)s" in detailed_format
                assert "%(lineno)d" in detailed_format
                assert "%(asctime)s" in detailed_format

                # Verify handlers
                expected_handlers = ["console", "file", "error_file"]
                for handler in expected_handlers:
                    assert handler in config["handlers"]

                # Verify file paths
                assert (
                    str(log_dir / "chromoforge.log")
                    == config["handlers"]["file"]["filename"]
                )
                assert (
                    str(log_dir / "errors.log")
                    == config["handlers"]["error_file"]["filename"]
                )

    def test_standard_logging_integration_medical_scenarios(self):
        """Test standard logging with real medical processing scenarios."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir) / "medical_logs"
            log_dir.mkdir()

            with patch("src.utils.logging_config.settings") as mock_settings:
                mock_settings.log_level = "INFO"

                config = get_standard_logging_config(log_dir)
                logging.config.dictConfig(config)

                # Test with real medical processing
                logger = logging.getLogger("medical_document_processor")

                logger.info("Starting batch processing of 50 cardiac reports")
                logger.warning(
                    "High PII confidence detected in patient record HN-2024-001234"
                )
                logger.error("Failed to connect to hospital database", exc_info=True)

                # Verify log files
                main_log = log_dir / "chromoforge.log"
                error_log = log_dir / "errors.log"

                assert main_log.exists()
                assert error_log.exists()

                # Verify content format
                with open(main_log, "r", encoding="utf-8") as f:
                    content = f.read()
                    assert "Starting batch processing" in content
                    assert "High PII confidence" in content

                # Error log should contain the database connection error
                with open(error_log, "r", encoding="utf-8") as f:
                    error_content = f.read()
                    assert "Failed to connect to hospital database" in error_content


class TestOCRLogger:
    """Test OCRLogger class with real hospital OCR operations."""

    def test_ocr_logger_initialization_integration(self):
        """Test OCR logger initialization with real hospital context."""
        # Setup structlog first
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.processors.TimeStamper(fmt="ISO"),
                structlog.processors.JSONRenderer(),
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )

        ocr_logger = OCRLogger("hospital_ocr_processor")

        assert ocr_logger.logger is not None
        assert hasattr(ocr_logger.logger, "info")
        assert hasattr(ocr_logger.logger, "error")

    def test_ocr_logger_start_integration(self):
        """Test OCR logger start event with real medical document."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "ocr_test.log"

            # Setup basic logging to capture output
            logging.basicConfig(
                level=logging.INFO,
                format="%(message)s",
                handlers=[logging.FileHandler(log_file)],
            )

            # Configure structlog to use standard logging
            structlog.configure(
                processors=[
                    structlog.stdlib.filter_by_level,
                    structlog.stdlib.add_logger_name,
                    structlog.stdlib.add_log_level,
                    structlog.processors.JSONRenderer(),
                ],
                logger_factory=structlog.stdlib.LoggerFactory(),
                wrapper_class=structlog.stdlib.BoundLogger,
                cache_logger_on_first_use=True,
            )

            ocr_logger = OCRLogger("cardiac_report_processor")

            # Log real hospital OCR start
            ocr_logger.log_ocr_start(
                file_path="/hospital/cardiology/reports/cardiac_cath_HN001234.pdf",
                file_size=15728640,  # 15MB
            )

            # Verify log was written
            assert log_file.exists()

            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()
                assert "OCR processing started" in log_content
                assert "cardiac_cath_HN001234.pdf" in log_content
                assert "15.0" in log_content  # File size in MB
                assert "ocr_start" in log_content

    def test_ocr_logger_complete_integration(self):
        """Test OCR logger completion with real processing results."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "ocr_complete_test.log"

            logging.basicConfig(
                level=logging.INFO,
                format="%(message)s",
                handlers=[logging.FileHandler(log_file)],
            )

            structlog.configure(
                processors=[structlog.processors.JSONRenderer()],
                logger_factory=structlog.stdlib.LoggerFactory(),
                wrapper_class=structlog.stdlib.BoundLogger,
                cache_logger_on_first_use=True,
            )

            ocr_logger = OCRLogger("lab_results_processor")

            # Log real OCR completion
            ocr_logger.log_ocr_complete(
                file_path="/hospital/lab/hematology/CBC_results_HN001234.pdf",
                processing_time=45.67,
                confidence=0.92,
                pii_count=7,
            )

            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()
                assert "OCR processing completed" in log_content
                assert "CBC_results_HN001234.pdf" in log_content
                assert "45.67" in log_content
                assert "0.92" in log_content
                assert "7" in log_content
                assert "ocr_complete" in log_content

    def test_ocr_logger_error_integration(self):
        """Test OCR logger error with real failure scenario."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "ocr_error_test.log"

            logging.basicConfig(
                level=logging.ERROR,
                format="%(message)s",
                handlers=[logging.FileHandler(log_file)],
            )

            structlog.configure(
                processors=[structlog.processors.JSONRenderer()],
                logger_factory=structlog.stdlib.LoggerFactory(),
                wrapper_class=structlog.stdlib.BoundLogger,
                cache_logger_on_first_use=True,
            )

            ocr_logger = OCRLogger("radiology_processor")

            # Log real OCR error
            ocr_logger.log_ocr_error(
                file_path="/hospital/radiology/ct_scans/corrupted_scan_HN001234.pdf",
                error_message="Gemini API quota exceeded during peak hospital hours",
                retry_count=3,
            )

            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()
                assert "OCR processing failed" in log_content
                assert "corrupted_scan_HN001234.pdf" in log_content
                assert "quota exceeded" in log_content
                assert "3" in log_content  # retry_count
                assert "ocr_error" in log_content

    def test_ocr_logger_pii_detection_integration(self):
        """Test PII detection logging with real Thai medical data."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "pii_detection_test.log"

            logging.basicConfig(
                level=logging.INFO,
                format="%(message)s",
                handlers=[logging.FileHandler(log_file)],
            )

            structlog.configure(
                processors=[structlog.processors.JSONRenderer()],
                logger_factory=structlog.stdlib.LoggerFactory(),
                wrapper_class=structlog.stdlib.BoundLogger,
                cache_logger_on_first_use=True,
            )

            ocr_logger = OCRLogger("pii_detection_processor")

            # Log real PII detection results
            pii_types = {
                "thai_national_id": 2,
                "thai_patient_name": 1,
                "hospital_number": 1,
                "phone_number": 1,
                "doctor_name": 2,
            }

            ocr_logger.log_pii_detection(
                file_path="/hospital/emergency/admission_forms/emergency_HN001234.pdf",
                pii_types=pii_types,
                total_matches=7,
            )

            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()
                assert "PII detection completed" in log_content
                assert "emergency_HN001234.pdf" in log_content
                assert "thai_national_id" in log_content
                assert "thai_patient_name" in log_content
                assert "7" in log_content  # total matches
                assert "pii_detection" in log_content

    def test_ocr_logger_obfuscation_complete_integration(self):
        """Test obfuscation completion logging with real privacy protection."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "obfuscation_test.log"

            logging.basicConfig(
                level=logging.INFO,
                format="%(message)s",
                handlers=[logging.FileHandler(log_file)],
            )

            structlog.configure(
                processors=[structlog.processors.JSONRenderer()],
                logger_factory=structlog.stdlib.LoggerFactory(),
                wrapper_class=structlog.stdlib.BoundLogger,
                cache_logger_on_first_use=True,
            )

            ocr_logger = OCRLogger("pdf_privacy_processor")

            # Log real obfuscation completion
            ocr_logger.log_obfuscation_complete(
                file_path="/hospital/psychiatry/evaluations/mental_health_HN001234.pdf",
                output_path="/hospital/processed/psychiatry/mental_health_HN001234_obfuscated.pdf",
                pii_obfuscated=12,
                method="BLACK_BOX",
            )

            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()
                assert "PDF obfuscation completed" in log_content
                assert "mental_health_HN001234.pdf" in log_content
                assert "obfuscated.pdf" in log_content
                assert "12" in log_content
                assert "BLACK_BOX" in log_content
                assert "obfuscation_complete" in log_content

    def test_ocr_logger_batch_stats_integration(self):
        """Test batch processing statistics logging with real hospital volumes."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "batch_stats_test.log"

            logging.basicConfig(
                level=logging.INFO,
                format="%(message)s",
                handlers=[logging.FileHandler(log_file)],
            )

            structlog.configure(
                processors=[structlog.processors.JSONRenderer()],
                logger_factory=structlog.stdlib.LoggerFactory(),
                wrapper_class=structlog.stdlib.BoundLogger,
                cache_logger_on_first_use=True,
            )

            ocr_logger = OCRLogger("hospital_batch_processor")

            # Log real hospital batch statistics
            ocr_logger.log_batch_stats(
                total_files=150, completed=142, failed=8, avg_time=32.5
            )

            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()
                assert "Batch processing statistics" in log_content
                assert "150" in log_content  # total files
                assert "142" in log_content  # completed
                assert "8" in log_content  # failed
                assert "94.7" in log_content  # success rate (142/150 * 100)
                assert "32.5" in log_content  # avg time
                assert "batch_stats" in log_content


class TestLoggingFileRotation:
    """Test logging file rotation with real hospital data volumes."""

    def test_log_rotation_json_format_integration(self):
        """Test log file rotation with real hospital JSON logging volume."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir) / "rotation_test"
            log_dir.mkdir()

            with patch("src.utils.logging_config.settings") as mock_settings:
                mock_settings.log_level = "INFO"

                # Create config with small file size for testing
                config = get_json_logging_config(log_dir)
                config["handlers"]["file_json"]["maxBytes"] = 1024  # 1KB for testing
                config["handlers"]["file_json"]["backupCount"] = 3

                logging.config.dictConfig(config)
                logger = logging.getLogger("rotation_test")

                # Generate enough log data to trigger rotation
                for i in range(100):
                    logger.info(
                        f"Processing patient record {i:04d}",
                        extra={
                            "patient_hn": f"HN-2024-{i:06d}",
                            "document_type": "lab_results",
                            "hospital_dept": "pathology",
                            "pii_detected": ["thai_national_id", "patient_name"],
                            "processing_time": 25.5 + i * 0.1,
                            "confidence_score": 0.85 + (i % 10) * 0.01,
                        },
                    )

                # Verify rotation occurred
                main_log = log_dir / "chromoforge.jsonl"
                backup_log = log_dir / "chromoforge.jsonl.1"

                assert main_log.exists()
                # Backup should exist if rotation occurred
                if backup_log.exists():
                    assert backup_log.stat().st_size > 0

    def test_error_log_separation_integration(self):
        """Test that error logs are properly separated in real hospital scenarios."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir) / "error_separation_test"
            log_dir.mkdir()

            with patch("src.utils.logging_config.settings") as mock_settings:
                mock_settings.log_level = "DEBUG"

                config = get_json_logging_config(log_dir)
                logging.config.dictConfig(config)

                logger = logging.getLogger("hospital_error_test")

                # Log various levels including errors
                logger.info("Normal patient processing started")
                logger.warning("High confidence PII detected")
                logger.error("Critical: Database connection failed")
                logger.error("Emergency: Patient data encryption failed")
                logger.info("Normal processing resumed")

                # Verify main log contains all levels
                main_log = log_dir / "chromoforge.jsonl"
                error_log = log_dir / "errors.jsonl"

                assert main_log.exists()
                assert error_log.exists()

                # Verify error log only contains errors
                with open(error_log, "r", encoding="utf-8") as f:
                    error_content = f.read()
                    assert "Database connection failed" in error_content
                    assert "Patient data encryption failed" in error_content
                    assert "Normal patient processing" not in error_content
                    assert "High confidence PII" not in error_content


class TestLoggingPerformance:
    """Test logging performance with real hospital data volumes."""

    @pytest.mark.performance
    def test_json_logging_performance_integration(self):
        """Test JSON logging performance with real hospital message volumes."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir) / "performance_test"
            log_dir.mkdir()

            with patch("src.utils.logging_config.settings") as mock_settings:
                mock_settings.log_level = "INFO"

                config = get_json_logging_config(log_dir)
                logging.config.dictConfig(config)

                logger = logging.getLogger("performance_test_logger")

                # Measure logging performance
                start_time = time.time()

                # Log 1000 realistic hospital messages
                for i in range(1000):
                    logger.info(
                        f"Patient record processed",
                        extra={
                            "patient_hn": f"HN-2024-{i:06d}",
                            "document_type": [
                                "lab_results",
                                "xray_report",
                                "discharge_summary",
                            ][i % 3],
                            "hospital_dept": [
                                "emergency",
                                "icu",
                                "cardiology",
                                "neurology",
                            ][i % 4],
                            "pii_detected_count": i % 10,
                            "processing_time_seconds": 25.5 + (i % 50),
                            "confidence_score": 0.75 + (i % 25) * 0.01,
                            "gemini_model": "gemini-2.0-flash-exp",
                            "thai_text_detected": True if i % 3 == 0 else False,
                        },
                    )

                end_time = time.time()
                total_time = end_time - start_time
                messages_per_second = 1000 / total_time

                # Performance requirement: > 100 messages per second
                assert (
                    messages_per_second > 100
                ), f"Logging too slow: {messages_per_second:.0f} msg/sec"
                print(
                    f"JSON logging performance: {messages_per_second:.0f} messages/second"
                )

    @pytest.mark.performance
    def test_structured_logging_performance_integration(self):
        """Test structlog performance with real OCR operation logging."""
        # Configure structlog
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.processors.TimeStamper(fmt="ISO"),
                structlog.processors.JSONRenderer(),
            ],
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )

        ocr_logger = OCRLogger("performance_test_ocr")

        # Measure OCR logging performance
        start_time = time.time()

        for i in range(500):  # 500 OCR operations
            ocr_logger.log_ocr_start(
                file_path=f"/hospital/documents/medical_report_{i:04d}.pdf",
                file_size=1024 * 1024 * (5 + i % 10),  # 5-15MB files
            )

            ocr_logger.log_pii_detection(
                file_path=f"/hospital/documents/medical_report_{i:04d}.pdf",
                pii_types={
                    "thai_national_id": i % 3,
                    "thai_patient_name": 1,
                    "hospital_number": 1,
                    "phone_number": i % 2,
                },
                total_matches=3 + i % 5,
            )

            ocr_logger.log_ocr_complete(
                file_path=f"/hospital/documents/medical_report_{i:04d}.pdf",
                processing_time=20.0 + i % 40,
                confidence=0.80 + (i % 20) * 0.01,
                pii_count=3 + i % 5,
            )

        end_time = time.time()
        total_time = end_time - start_time
        operations_per_second = 1500 / total_time  # 500 operations * 3 log calls each

        # Performance requirement: > 200 structured log operations per second
        assert (
            operations_per_second > 200
        ), f"Structured logging too slow: {operations_per_second:.0f} ops/sec"
        print(
            f"Structured logging performance: {operations_per_second:.0f} operations/second"
        )


class TestLoggingIntegration:
    """Test logging integration with real hospital systems."""

    @pytest.mark.integration
    def test_logging_with_integration_settings_integration(self):
        """Test logging integration with real settings configuration."""
        # Test with various real environment configurations
        test_configs = [
            {"log_format": "json", "log_level": "INFO"},
            {"log_format": "standard", "log_level": "DEBUG"},
            {"log_format": "json", "log_level": "WARNING"},
            {"log_format": "standard", "log_level": "ERROR"},
        ]

        for config in test_configs:
            with tempfile.TemporaryDirectory() as temp_dir:
                original_cwd = Path.cwd()

                try:
                    import os

                    os.chdir(temp_dir)

                    with patch("src.utils.logging_config.settings") as mock_settings:
                        mock_settings.log_format = config["log_format"]
                        mock_settings.log_level = config["log_level"]

                        # Should not raise any exceptions
                        setup_logging()

                        # Test that logging works
                        logger = logging.getLogger("integration_test")
                        logger.info("Integration test message")
                        logger.error("Integration test error")

                        # Verify logs directory was created
                        logs_dir = Path(temp_dir) / "logs"
                        assert logs_dir.exists()

                finally:
                    os.chdir(original_cwd)

    def test_logging_unicode_support_integration(self):
        """Test logging with real Thai Unicode content."""
        with tempfile.TemporaryDirectory() as temp_dir:
            original_cwd = Path.cwd()

            try:
                import os

                os.chdir(temp_dir)

                with patch("src.utils.logging_config.settings") as mock_settings:
                    mock_settings.log_format = "json"
                    mock_settings.log_level = "INFO"

                    setup_logging()

                    logger = logging.getLogger("unicode_test")

                    # Log real Thai hospital content
                    thai_messages = [
                        "เริ่มการประมวลผลเอกสารผู้ป่วย: นายสมชาย ใจดี",
                        "พบข้อมูลผู้ป่วยในแผนก: โรงพยาบาลจุฬาลงกรณ์",
                        "การวินิจฉัย: โรคหัวใจขาดเลือด",
                        "แพทย์ผู้รักษา: นายแพทย์ประสิทธิ์ วิทยาคม",
                        "ผลการตรวจ: ค่าน้ำตาลในเลือด ๑๒๐ มก./ดล.",
                    ]

                    for i, message in enumerate(thai_messages):
                        logger.info(
                            message,
                            extra={
                                "patient_id": f"HN-2024-{i+1:06d}",
                                "thai_department": "แผนกอายุรกรรม",
                                "thai_doctor": "นายแพทย์สมชาย",
                                "thai_diagnosis": "โรคเบาหวาน",
                            },
                        )

                    # Verify Thai content was logged correctly
                    logs_dir = Path(temp_dir) / "logs"
                    json_log = logs_dir / "chromoforge.jsonl"

                    assert json_log.exists()

                    with open(json_log, "r", encoding="utf-8") as f:
                        content = f.read()
                        assert "นายสมชาย ใจดี" in content
                        assert "โรงพยาบาลจุฬาลงกรณ์" in content
                        assert "แผนกอายุรกรรม" in content
                        assert "โรคเบาหวาน" in content

            finally:
                os.chdir(original_cwd)


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
