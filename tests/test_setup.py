#!/usr/bin/env python3
"""Test script to verify ChromoForge setup is working correctly."""

import os
import sys
from pathlib import Path

# Add parent directory to path to import src modules
sys.path.insert(0, str(Path(__file__).parent.parent))


def test_environment_variables():
    """Test that all required environment variables are set."""
    print("🔧 Testing Environment Variables...")

    required_vars = [
        "GOOGLE_API_KEY",
        "NEXT_PUBLIC_SUPABASE_URL",
        "NEXT_PUBLIC_SUPABASE_ANON_KEY",
        "SUPABASE_SERVICE_ROLE_KEY",
    ]

    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            print(f"  ✓ {var}: Set")

    if missing_vars:
        print(f"  ✗ Missing variables: {', '.join(missing_vars)}")
        return False

    print("  ✓ All environment variables are set")
    return True


def test_imports():
    """Test that all required packages can be imported."""
    print("\n📦 Testing Package Imports...")

    try:
        import google.genai as genai

        print("  ✓ google-genai imported successfully")
    except ImportError as e:
        print(f"  ✗ Failed to import google-genai: {e}")
        return False

    try:
        from google.genai.types import HarmCategory, SafetySetting

        print("  ✓ SafetySetting and HarmCategory imported successfully")
    except ImportError as e:
        print(f"  ✗ Failed to import safety types: {e}")
        return False

    try:
        import supabase

        print("  ✓ supabase imported successfully")
    except ImportError as e:
        print(f"  ✗ Failed to import supabase: {e}")
        return False

    try:
        import fitz  # PyMuPDF

        print("  ✓ PyMuPDF imported successfully")
    except ImportError as e:
        print(f"  ✗ Failed to import PyMuPDF: {e}")
        return False

    try:
        from src.core.config import settings

        print("  ✓ ChromoForge config imported successfully")
    except ImportError as e:
        print(f"  ✗ Failed to import ChromoForge config: {e}")
        return False

    print("  ✓ All packages imported successfully")
    return True


def test_sample_files():
    """Test that sample PDF files exist."""
    print("\n📄 Testing Sample Files...")

    sample_dir = Path(__file__).parent.parent / "original-pdf-examples"
    if not sample_dir.exists():
        print(f"  ✗ Sample directory not found: {sample_dir}")
        return False

    pdf_files = list(sample_dir.glob("*.pdf"))
    if not pdf_files:
        print(f"  ✗ No PDF files found in {sample_dir}")
        return False

    print(f"  ✓ Found {len(pdf_files)} sample PDF files")
    for pdf_file in pdf_files[:3]:  # Show first 3
        print(f"    - {pdf_file.name}")
    if len(pdf_files) > 3:
        print(f"    ... and {len(pdf_files) - 3} more")

    return True


def test_api_connection():
    """Test API connections (if environment variables are set)."""
    print("\n🌐 Testing API Connections...")

    # Test Google Gemini API
    try:
        import google.genai as genai

        api_key = os.getenv("GOOGLE_API_KEY")
        if api_key and api_key != "your-google-api-key-here":
            client = genai.Client(api_key=api_key)
            # Try to list models to test connection
            models = client.models.list()
            print("  ✓ Google Gemini API connection successful")
        else:
            print("  ⚠ Google API key not configured - skipping connection test")
    except Exception as e:
        print(f"  ✗ Google Gemini API connection failed: {e}")
        return False

    # Test Supabase connection
    try:
        from supabase import create_client

        url = os.getenv("NEXT_PUBLIC_SUPABASE_URL")
        key = os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY")

        if url and key and url != "your-supabase-project-url":
            supabase_client = create_client(url, key)
            # Try a simple query to test connection
            result = (
                supabase_client.table("_integrationtime_schema_migrations")
                .select("*")
                .limit(1)
                .execute()
            )
            print("  ✓ Supabase connection successful")
        else:
            print("  ⚠ Supabase credentials not configured - skipping connection test")
    except Exception as e:
        print(f"  ✗ Supabase connection failed: {e}")
        return False

    return True


def main():
    """Run all setup tests."""
    print("🚀 ChromoForge Setup Test\n")

    # Load environment variables
    try:
        from dotenv import load_dotenv

        load_dotenv()
        print("✓ Environment variables loaded from .env file\n")
    except ImportError:
        print("⚠ python-dotenv not available, using system environment variables\n")

    tests = [
        test_environment_variables,
        test_imports,
        test_sample_files,
        test_api_connection,
    ]

    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"  ✗ Test failed with exception: {e}")
            results.append(False)

    print(f"\n📊 Test Results: {sum(results)}/{len(results)} passed")

    if all(results):
        print("🎉 All tests passed! ChromoForge is ready to run.")
        return 0
    else:
        print("❌ Some tests failed. Please check the setup guide.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
