"""Real-world tests for core models with actual data validation.

This module tests the core data models including OCRResult, OCRResult,
ExtractedField, and ConfidenceLevel with real medical document data patterns.
"""

import json
from datetime import datetime
from typing import Any, Dict

import pytest

from src.core.models import (ConfidenceLevel, OCRResult,
                             ExtractedField, OCRResult)


class TestExtractedField:
    """Test ExtractedField model with real medical data patterns."""

    def test_extracted_field_creation_with_high_confidence(self):
        """Test creating ExtractedField with high confidence medical data."""
        field = ExtractedField(
            value="TT04035",
            confidence=ConfidenceLevel.HIGH,
            reasoning="Clear patient code format with TT prefix followed by 5 digits",
            alternatives=["TT04036", "TT04034"],
        )

        assert field.value == "TT04035"
        assert field.confidence == ConfidenceLevel.HIGH
        assert "TT prefix" in field.reasoning
        assert len(field.alternatives) == 2

    def test_extracted_field_blank_value(self):
        """Test ExtractedField with blank value for missing data."""
        field = ExtractedField(
            value="[Blank]",
            confidence=ConfidenceLevel.LOW,
            reasoning="No patient code found in document",
            alternatives=[],
        )

        assert field.value == "[Blank]"
        assert field.confidence == ConfidenceLevel.LOW
        assert field.alternatives == []

    def test_extracted_field_thai_medical_data(self):
        """Test ExtractedField with Thai medical data."""
        field = ExtractedField(
            value="นายสมชาย ใจดี",
            confidence=ConfidenceLevel.MEDIUM,
            reasoning="Thai name detected with title prefix",
            alternatives=["นายสมชาย ใจดี", "สมชาย ใจดี"],
        )

        assert "นาย" in field.value
        assert field.confidence == ConfidenceLevel.MEDIUM


class TestConfidenceLevel:
    """Test ConfidenceLevel enum values."""

    def test_confidence_level_values(self):
        """Test all confidence level enum values."""
        assert ConfidenceLevel.LOW == "Low"
        assert ConfidenceLevel.MEDIUM == "Medium"
        assert ConfidenceLevel.HIGH == "High"

    def test_confidence_level_comparison(self):
        """Test confidence level string comparison."""
        assert ConfidenceLevel.HIGH != ConfidenceLevel.LOW
        assert ConfidenceLevel.MEDIUM == "Medium"


class TestOCRResult:
    """Test OCRResult model with real medical document data."""

    def test_ocr_result_creation_with_complete_data(self):
        """Test creating OCRResult with complete medical document data."""
        result = OCRResult(
            full_text="Patient: TT04035 Sample: ABC123 Investigation: K-TRACK",
            patient_code="TT04035",
            sample_code="ABC123",
            investigation="K-TRACK",
            patient_name_th="นายสมชาย ใจดี",
            patient_name_en="Mr. Somchai Jaidee",
            dob_gregorian="1985-03-15",
            dob_buddhist="2528-03-15",
            age="38",
            gender="Male",
            contact_info="************",
            referring_physician="Dr. Smith",
            referring_physician_email=["<EMAIL>"],
            confidence_score=0.95,
            processing_time=2.5,
            page_count=1,
            detected_languages=["th", "en"],
            errors=[],
            warnings=[],
            document_id="doc_123",
            timestamp=datetime.now().isoformat(),
        )

        assert result.patient_code == "TT04035"
        assert result.sample_code == "ABC123"
        assert result.investigation == "K-TRACK"
        assert result.confidence_score == 0.95
        assert result.page_count == 1
        assert "th" in result.detected_languages
        assert "en" in result.detected_languages

    def test_ocr_result_with_minimal_data(self):
        """Test OCRResult with minimal required data."""
        result = OCRResult(full_text="Minimal document text")

        assert result.full_text == "Minimal document text"
        assert result.patient_code is None
        assert result.sample_code is None
        assert result.confidence_score == 0.0
        assert result.page_count == 1
        assert result.detected_languages == []

    def test_ocr_result_validation_constraints(self):
        """Test OCRResult field validation constraints."""
        # Test patient_code max length
        result = OCRResult(
            full_text="Test",
            patient_code="TT" + "0" * 50,  # Should be within 50 char limit
        )
        assert len(result.patient_code) <= 50

        # Test confidence score bounds
        result = OCRResult(full_text="Test", confidence_score=1.0)
        assert 0.0 <= result.confidence_score <= 1.0

    def test_ocr_result_thai_medical_patterns(self):
        """Test OCRResult with real Thai medical document patterns."""
        result = OCRResult(
            full_text="ผู้ป่วย: TT04035 ตัวอย่าง: XYZ789 การตรวจ: SPOT-MAS",
            patient_code="TT04035",
            sample_code="XYZ789",
            investigation="SPOT-MAS",
            patient_name_th="นางสาวมาลี สวยงาม",
            dob_buddhist="2540-12-25",
            gender="หญิง",
            referring_physician="นพ.สมิท โรงพยาบาล",
        )

        assert "TT" in result.patient_code
        assert result.investigation == "SPOT-MAS"
        assert "นางสาว" in result.patient_name_th
        assert "2540" in result.dob_buddhist


class TestOCRResult:
    """Test OCRResult model with 14-field medical extraction."""

    def test_medical_ocr_result_creation(self):
        """Test creating OCRResult with 14 medical fields."""
        patient_code_field = ExtractedField(
            value="TT04035",
            confidence=ConfidenceLevel.HIGH,
            reasoning="Clear TT prefix pattern",
            alternatives=[],
        )

        sample_code_field = ExtractedField(
            value="ABC123",
            confidence=ConfidenceLevel.HIGH,
            reasoning="6-character alphanumeric code",
            alternatives=["ABC124"],
        )

        result = OCRResult(
            patient_code=patient_code_field,
            sample_code=sample_code_field,
            investigation=ExtractedField(
                value="K-TRACK", confidence=ConfidenceLevel.HIGH
            ),
            patient_name_th=ExtractedField(
                value="นายสมชาย", confidence=ConfidenceLevel.MEDIUM
            ),
            patient_name_en=ExtractedField(
                value="Mr. Somchai", confidence=ConfidenceLevel.MEDIUM
            ),
            dob_gregorian=ExtractedField(
                value="1985-03-15", confidence=ConfidenceLevel.HIGH
            ),
            dob_buddhist=ExtractedField(
                value="2528-03-15", confidence=ConfidenceLevel.HIGH
            ),
            age=ExtractedField(value="38", confidence=ConfidenceLevel.HIGH),
            gender=ExtractedField(value="Male", confidence=ConfidenceLevel.HIGH),
            contact_info=ExtractedField(
                value="************", confidence=ConfidenceLevel.MEDIUM
            ),
            referring_physician=ExtractedField(
                value="Dr. Smith", confidence=ConfidenceLevel.HIGH
            ),
            referring_physician_email=ExtractedField(
                value="<EMAIL>", confidence=ConfidenceLevel.HIGH
            ),
            full_text="Complete medical document text",
            confidence_score=0.92,
            processing_time=3.2,
            page_count=2,
            detected_languages=["th", "en"],
            errors=[],
            warnings=["Minor formatting inconsistency"],
            document_id="medical_doc_123",
            timestamp=datetime.now().isoformat(),
        )

        assert result.patient_code.value == "TT04035"
        assert result.sample_code.value == "ABC123"
        assert result.investigation.value == "K-TRACK"
        assert result.confidence_score == 0.92
        assert len(result.warnings) == 1

    def test_medical_ocr_result_with_blank_fields(self):
        """Test OCRResult with some blank fields."""
        result = OCRResult(
            patient_code=ExtractedField(
                value="TT04035", confidence=ConfidenceLevel.HIGH
            ),
            sample_code=ExtractedField(value="[Blank]", confidence=ConfidenceLevel.LOW),
            investigation=ExtractedField(
                value="[Blank]", confidence=ConfidenceLevel.LOW
            ),
            patient_name_th=ExtractedField(
                value="นายสมชาย", confidence=ConfidenceLevel.MEDIUM
            ),
            patient_name_en=ExtractedField(
                value="[Blank]", confidence=ConfidenceLevel.LOW
            ),
            dob_gregorian=ExtractedField(
                value="1985-03-15", confidence=ConfidenceLevel.HIGH
            ),
            dob_buddhist=ExtractedField(
                value="2528-03-15", confidence=ConfidenceLevel.HIGH
            ),
            age=ExtractedField(value="38", confidence=ConfidenceLevel.HIGH),
            gender=ExtractedField(value="Male", confidence=ConfidenceLevel.HIGH),
            contact_info=ExtractedField(
                value="[Blank]", confidence=ConfidenceLevel.LOW
            ),
            referring_physician=ExtractedField(
                value="Dr. Smith", confidence=ConfidenceLevel.HIGH
            ),
            referring_physician_email=ExtractedField(
                value="[Blank]", confidence=ConfidenceLevel.LOW
            ),
            full_text="Partial medical document",
            confidence_score=0.65,
            processing_time=1.8,
            page_count=1,
        )

        assert result.patient_code.value == "TT04035"
        assert result.sample_code.value == "[Blank]"
        assert result.investigation.value == "[Blank]"
        assert result.confidence_score == 0.65


@pytest.mark.integration
class TestModelIntegration:
    """Integration tests for model interactions."""

    def test_model_json_serialization(self):
        """Test that models can be serialized to JSON."""
        result = OCRResult(
            full_text="Test document", patient_code="TT04035", confidence_score=0.85
        )

        # Test JSON serialization
        json_data = result.model_dump()
        assert json_data["patient_code"] == "TT04035"
        assert json_data["confidence_score"] == 0.85

        # Test JSON deserialization
        new_result = OCRResult(**json_data)
        assert new_result.patient_code == "TT04035"
        assert new_result.confidence_score == 0.85

    def test_medical_model_json_serialization(self):
        """Test OCRResult JSON serialization."""
        field = ExtractedField(
            value="TT04035", confidence=ConfidenceLevel.HIGH, reasoning="Clear pattern"
        )

        result = OCRResult(
            patient_code=field,
            sample_code=field,
            investigation=field,
            patient_name_th=field,
            patient_name_en=field,
            dob_gregorian=field,
            dob_buddhist=field,
            age=field,
            gender=field,
            contact_info=field,
            referring_physician=field,
            referring_physician_email=field,
            full_text="Test",
        )

        json_data = result.model_dump()
        assert json_data["patient_code"]["value"] == "TT04035"
        assert json_data["patient_code"]["confidence"] == "High"
