"""Comprehensive unit tests for audit logger module using real data and integrations.

This test suite validates all functions in src/security/audit_logger.py using:
- Real audit events with Thai medical scenarios
- Real file system logging operations
- Real threading and concurrency scenarios
- Real performance benchmarks with hospital volumes
- Real compliance validation with HIPAA-like requirements
"""

import asyncio
import json
import logging
import os
import tempfile
import threading
import time
from pathlib import Path
from typing import Any, Dict, List
from unittest.mock import patch

import pytest

from src.core.exceptions import AuditLoggingException
from src.security.audit_logger import (AuditEvent, AuditEventType, AuditLogger,
                                       AuditSeverity, audit_log,
                                       get_audit_logger,
                                       initialize_audit_logger)


class TestAuditEventType:
    """Test AuditEventType enum with real medical scenarios."""

    def test_audit_event_types_complete(self):
        """Test all audit event types are properly defined."""
        expected_types = [
            "pii_detected",
            "pii_encrypted",
            "pii_decrypted",
            "pii_obfuscated",
            "pii_access",
            "document_processed",
            "document_uploaded",
            "document_downloaded",
            "document_deleted",
            "user_login",
            "user_logout",
            "access_denied",
            "permission_granted",
            "system_error",
            "config_changed",
            "batch_started",
            "batch_completed",
            "data_retention_applied",
            "audit_log_accessed",
            "compliance_violation",
        ]

        actual_types = [event_type.value for event_type in AuditEventType]

        for expected_type in expected_types:
            assert (
                expected_type in actual_types
            ), f"Missing audit event type: {expected_type}"

        # Verify we have all expected types
        assert len(actual_types) >= len(expected_types)

    def test_audit_event_types_medical_scenarios(self):
        """Test audit event types in real medical scenarios."""
        # PII operations in Thai hospital
        assert AuditEventType.PII_DETECTED.value == "pii_detected"
        assert AuditEventType.PII_ENCRYPTED.value == "pii_encrypted"
        assert AuditEventType.PII_DECRYPTED.value == "pii_decrypted"

        # Document processing for medical records
        assert AuditEventType.DOCUMENT_PROCESSED.value == "document_processed"
        assert AuditEventType.DOCUMENT_UPLOADED.value == "document_uploaded"

        # Compliance events for HIPAA-like requirements
        assert AuditEventType.COMPLIANCE_VIOLATION.value == "compliance_violation"
        assert AuditEventType.AUDIT_LOG_ACCESSED.value == "audit_log_accessed"


class TestAuditSeverity:
    """Test AuditSeverity enum with real severity scenarios."""

    def test_audit_severity_levels(self):
        """Test all severity levels are properly defined."""
        assert AuditSeverity.INFO.value == "info"
        assert AuditSeverity.WARNING.value == "warning"
        assert AuditSeverity.ERROR.value == "error"
        assert AuditSeverity.CRITICAL.value == "critical"

    def test_severity_medical_context(self):
        """Test severity levels in medical context."""
        # Info: Normal operations
        normal_pii_detection = AuditSeverity.INFO
        assert normal_pii_detection.value == "info"

        # Warning: Suspicious activity
        unusual_access_pattern = AuditSeverity.WARNING
        assert unusual_access_pattern.value == "warning"

        # Error: Failed operations
        encryption_failure = AuditSeverity.ERROR
        assert encryption_failure.value == "error"

        # Critical: Security breaches
        unauthorized_pii_access = AuditSeverity.CRITICAL
        assert unauthorized_pii_access.value == "critical"


class TestAuditEvent:
    """Test AuditEvent class with real medical audit scenarios."""

    def test_audit_event_creation_integration_medical(self):
        """Test audit event creation with real Thai medical scenario."""
        # Real scenario: Doctor accessing patient PII
        event = AuditEvent(
            event_id="audit-2024-08-05-001234",
            event_type=AuditEventType.PII_DECRYPTED,
            timestamp="2024-08-05T14:30:00.123456Z",
            severity=AuditSeverity.INFO,
            user_id="doctor_somchai_001",
            session_id="medical_session_2024_08_05_143000",
            source_ip="*********",
            component="chromoforge_ocr",
            action="decrypt_patient_pii",
            resource="patient_medical_record",
            resource_id="patient_12345_record_20240805",
            success=True,
            metadata={
                "hospital_department": "cardiology",
                "patient_hn": "HN-2024-001234",
                "doctor_license": "MD-TH-001234",
                "diagnosis_code": "ICD-10-I25.9",
                "access_purpose": "treatment_planning",
            },
            pii_types=[
                "thai_national_id",
                "thai_patient_name",
                "medical_record_number",
            ],
            compliance_tags=["hipaa_equivalent", "pii_access", "medical_treatment"],
        )

        assert event.event_id == "audit-2024-08-05-001234"
        assert event.event_type == AuditEventType.PII_DECRYPTED
        assert event.user_id == "doctor_somchai_001"
        assert event.metadata["hospital_department"] == "cardiology"
        assert "thai_national_id" in event.pii_types
        assert "hipaa_equivalent" in event.compliance_tags

    def test_audit_event_serialization_integration(self):
        """Test audit event serialization with real Thai Unicode data."""
        # Event with Thai Unicode content
        event = AuditEvent(
            event_id="thai-unicode-test-001",
            event_type=AuditEventType.PII_DETECTED,
            timestamp="2024-08-05T15:00:00Z",
            severity=AuditSeverity.INFO,
            user_id="พยาบาล_001",
            metadata={
                "hospital_name": "โรงพยาบาลจุฬาลงกรณ์",
                "department": "แผนกหัวใจ",
                "patient_name_detected": "นายสมชาย ใจดี",
                "detection_confidence": 0.95,
                "thai_text_sample": "ผู้ป่วยมีอาการปวดหน้าอก",
            },
            pii_types=["thai_patient_name"],
            compliance_tags=["thai_medical_data", "unicode_support"],
        )

        # Test dictionary conversion
        event_dict = event.to_dict()
        assert event_dict["user_id"] == "พยาบาล_001"
        assert event_dict["metadata"]["hospital_name"] == "โรงพยาบาลจุฬาลงกรณ์"
        assert event_dict["metadata"]["thai_text_sample"] == "ผู้ป่วยมีอาการปวดหน้าอก"

        # Test JSON serialization preserves Thai characters
        json_str = event.to_json()
        parsed_event = json.loads(json_str)
        assert parsed_event["metadata"]["department"] == "แผนกหัวใจ"
        assert parsed_event["metadata"]["patient_name_detected"] == "นายสมชาย ใจดี"

    def test_audit_event_default_values_integration(self):
        """Test audit event default value initialization."""
        # Minimal event creation
        event = AuditEvent(
            event_id="minimal-test-001",
            event_type=AuditEventType.SYSTEM_ERROR,
            timestamp="2024-08-05T16:00:00Z",
            severity=AuditSeverity.ERROR,
        )

        # Verify defaults are set
        assert event.metadata == {}
        assert event.pii_types == []
        assert event.compliance_tags == []
        assert event.component == "chromoforge_ocr"
        assert event.success is True

        # Test post_init behavior
        event2 = AuditEvent(
            event_id="post-init-test",
            event_type=AuditEventType.PII_ENCRYPTED,
            timestamp="2024-08-05T16:01:00Z",
            severity=AuditSeverity.INFO,
            metadata=None,
            pii_types=None,
            compliance_tags=None,
        )

        assert event2.metadata == {}
        assert event2.pii_types == []
        assert event2.compliance_tags == []


class TestAuditLogger:
    """Test AuditLogger class with real hospital logging scenarios."""

    def test_audit_logger_initialization_integration(self):
        """Test audit logger initialization with real hospital configuration."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "hospital_audit.log"

            logger = AuditLogger(
                log_file_path=log_file,
                console_output=True,
                structured_logging=True,
                buffer_size=50,
                auto_flush_interval=10,
            )

            assert logger.log_file_path == log_file
            assert logger.console_output is True
            assert logger.structured_logging is True
            assert logger.buffer_size == 50
            assert logger.auto_flush_interval == 10
            assert len(logger._buffer) == 0

    def test_log_event_integration_pii_detection(self):
        """Test logging real PII detection event."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "pii_detection.log"
            logger = AuditLogger(log_file_path=log_file, buffer_size=1)

            # Real PII detection scenario
            event_id = logger.log_event(
                event_type=AuditEventType.PII_DETECTED,
                action="detect_thai_national_id",
                resource="medical_document",
                resource_id="CSF_report_20240805_001.pdf",
                user_id="ocr_system_001",
                session_id="batch_processing_20240805_143000",
                metadata={
                    "detected_pii_count": 3,
                    "pii_locations": [
                        "page_1_line_15",
                        "page_2_line_8",
                        "page_3_line_22",
                    ],
                    "confidence_scores": {
                        "thai_national_id": 0.95,
                        "thai_patient_name": 0.87,
                        "hospital_number": 0.92,
                    },
                    "document_type": "cerebrospinal_fluid_report",
                    "hospital_department": "neurology",
                },
                pii_types=["thai_national_id", "thai_patient_name", "hospital_number"],
                compliance_tags=[
                    "pii_detection",
                    "medical_document_processing",
                    "thai_medical_data",
                ],
            )

            # Verify event was logged
            assert event_id is not None
            assert log_file.exists()

            # Read and verify log content
            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()

            assert "detect_thai_national_id" in log_content
            assert "cerebrospinal_fluid_report" in log_content
            assert "neurology" in log_content

    def test_log_pii_detection_helper_integration(self):
        """Test PII detection logging helper with real data."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "pii_helper_test.log"
            logger = AuditLogger(log_file_path=log_file, buffer_size=1)

            # Real Thai hospital PII detection
            event_id = logger.log_pii_detection(
                pii_types=["thai_national_id", "thai_patient_name", "hospital_number"],
                pii_count=5,
                document_id="patient_chart_HN2024001234.pdf",
                confidence_scores={
                    "thai_national_id": 0.98,
                    "thai_patient_name": 0.89,
                    "hospital_number": 0.94,
                },
                user_id="dr_pranee_cardiology",
                session_id="patient_review_20240805_150000",
            )

            assert event_id is not None

            # Verify log content
            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()

            assert "thai_national_id" in log_content
            assert "patient_chart_HN2024001234.pdf" in log_content
            assert "dr_pranee_cardiology" in log_content

    def test_log_pii_obfuscation_integration(self):
        """Test PII obfuscation logging with real medical document."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "obfuscation_test.log"
            logger = AuditLogger(log_file_path=log_file, buffer_size=1)

            # Successful obfuscation
            success_event = logger.log_pii_obfuscation(
                document_id="lab_results_VN2024005678.pdf",
                obfuscation_method="BLACK_BOX",
                pii_types=["thai_national_id", "lab_number", "physician_name"],
                obfuscated_count=7,
                success=True,
                user_id="lab_tech_siriwan",
                session_id="lab_processing_20240805_160000",
            )

            # Failed obfuscation
            failure_event = logger.log_pii_obfuscation(
                document_id="corrupted_xray_report.pdf",
                obfuscation_method="BLUR",
                pii_types=["thai_patient_name"],
                obfuscated_count=0,
                success=False,
                error_code="PDF_COORDINATE_MAPPING_FAILED",
                user_id="radiology_tech_001",
                session_id="xray_processing_20240805_161500",
            )

            assert success_event is not None
            assert failure_event is not None

            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()

            assert "BLACK_BOX" in log_content
            assert "lab_results_VN2024005678.pdf" in log_content
            assert "PDF_COORDINATE_MAPPING_FAILED" in log_content

    def test_log_document_processing_integration(self):
        """Test document processing logging with real medical documents."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "document_processing.log"
            logger = AuditLogger(log_file_path=log_file, buffer_size=1)

            # Successful OCR processing
            event_id = logger.log_document_processing(
                document_id="cardiac_catheterization_report_20240805.pdf",
                processing_type="gemini_ocr_with_pii_detection",
                processing_time=45.67,
                success=True,
                user_id="cardiologist_dr_wichai",
                session_id="cardiac_review_20240805_170000",
                document_metadata={
                    "file_size_mb": 2.3,
                    "page_count": 8,
                    "document_type": "cardiac_catheterization_report",
                    "patient_hn": "HN-2024-001234",
                    "procedure_date": "2024-08-05",
                    "physician": "Dr. Wichai Jaidee",
                    "hospital_ward": "CCU",
                    "language": "thai_english_mixed",
                },
            )

            assert event_id is not None

            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()

            assert "cardiac_catheterization_report" in log_content
            assert "45.67" in log_content
            assert "CCU" in log_content

    def test_log_access_event_integration(self):
        """Test access event logging with real hospital scenarios."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "access_events.log"
            logger = AuditLogger(log_file_path=log_file, buffer_size=1)

            # Authorized access
            authorized_event = logger.log_access_event(
                resource="patient_medical_records",
                action="view_encrypted_pii",
                success=True,
                user_id="nurse_siriporn_icu",
                session_id="night_shift_20240805_200000",
                source_ip="*********",
                resource_id="patient_HN2024001234_records",
                access_reason="patient_medication_administration",
            )

            # Unauthorized access attempt
            denied_event = logger.log_access_event(
                resource="sensitive_psychiatric_records",
                action="attempt_view_restricted_pii",
                success=False,
                user_id="intern_medical_student_001",
                session_id="training_session_20240805_143000",
                source_ip="**********",
                resource_id="psychiatric_patient_records_ward_7",
                access_reason="educational_purpose",
            )

            assert authorized_event is not None
            assert denied_event is not None

            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()

            assert "nurse_siriporn_icu" in log_content
            assert "patient_medication_administration" in log_content
            assert "intern_medical_student_001" in log_content
            assert "psychiatric_patient_records" in log_content

    def test_audit_logger_buffering_integration(self):
        """Test audit logger buffering with real hospital event volume."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "buffering_test.log"
            logger = AuditLogger(
                log_file_path=log_file, buffer_size=10, auto_flush_interval=5
            )

            # Generate typical hospital events
            for i in range(8):  # Below buffer limit
                logger.log_event(
                    event_type=AuditEventType.PII_DETECTED,
                    action=f"process_patient_document_{i:03d}",
                    resource="patient_document",
                    resource_id=f"HN-2024-{i+1:06d}",
                    user_id=f"doctor_{i%3+1:03d}",
                    metadata={"patient_count": i + 1},
                )

            # File should not exist yet (buffered)
            assert not log_file.exists()

            # Add critical event - should trigger immediate flush
            logger.log_event(
                event_type=AuditEventType.COMPLIANCE_VIOLATION,
                action="unauthorized_pii_access_attempt",
                resource="restricted_records",
                severity=AuditSeverity.CRITICAL,
                user_id="suspicious_user_001",
            )

            # Now file should exist
            assert log_file.exists()

            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()

            assert "process_patient_document_007" in log_content
            assert "unauthorized_pii_access_attempt" in log_content

    def test_audit_logger_concurrent_integration(self):
        """Test concurrent audit logging in real hospital environment."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "concurrent_test.log"
            logger = AuditLogger(log_file_path=log_file, buffer_size=1)

            logged_events = []
            errors = []

            def medical_worker(worker_id, department):
                """Simulate medical staff generating audit events."""
                try:
                    for i in range(20):
                        event_id = logger.log_event(
                            event_type=AuditEventType.PII_DECRYPTED,
                            action=f"access_patient_record_{i:03d}",
                            resource="patient_medical_record",
                            resource_id=f"patient_{worker_id}_{i:03d}",
                            user_id=f"{department}_staff_{worker_id:03d}",
                            session_id=f"shift_{worker_id}_{i:03d}",
                            metadata={
                                "department": department,
                                "shift_time": f"2024-08-05T{8+worker_id:02d}:{i*3:02d}:00Z",
                                "patient_acuity": "high" if i % 3 == 0 else "normal",
                            },
                            pii_types=["thai_national_id", "medical_record"],
                            compliance_tags=[f"{department}_access", "patient_care"],
                        )
                        logged_events.append((worker_id, department, event_id))
                        time.sleep(0.01)  # Simulate processing time
                except Exception as e:
                    errors.append(f"Worker {worker_id} ({department}): {str(e)}")

            # Start workers from different departments
            departments = ["emergency", "icu", "cardiology", "neurology", "orthopedics"]
            threads = []

            for worker_id, department in enumerate(departments):
                thread = threading.Thread(
                    target=medical_worker, args=(worker_id, department)
                )
                threads.append(thread)
                thread.start()

            # Wait for all workers to complete
            for thread in threads:
                thread.join()

            # Verify no errors occurred
            assert len(errors) == 0, f"Concurrent logging errors: {errors}"
            assert len(logged_events) == 100  # 5 workers * 20 events each

            # Verify all events were logged
            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()

            for department in departments:
                assert f"{department}_access" in log_content
                assert f"{department}_staff" in log_content

    def test_audit_logger_flush_operations_integration(self):
        """Test manual flush operations with real data."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "flush_test.log"
            logger = AuditLogger(
                log_file_path=log_file, buffer_size=100  # Large buffer
            )

            # Add events without triggering auto-flush
            for i in range(5):
                logger.log_event(
                    event_type=AuditEventType.DOCUMENT_PROCESSED,
                    action=f"ocr_processing_{i:03d}",
                    resource="medical_document",
                    metadata={"processing_stage": "initial"},
                )

            # File should not exist yet
            assert not log_file.exists()

            # Manual flush
            logger.flush()

            # Now file should exist
            assert log_file.exists()

            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()

            assert "ocr_processing_004" in log_content
            assert "initial" in log_content

    def test_audit_logger_close_integration(self):
        """Test proper audit logger closure with pending events."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "close_test.log"
            logger = AuditLogger(log_file_path=log_file, buffer_size=100)

            # Add events
            for i in range(3):
                logger.log_event(
                    event_type=AuditEventType.USER_LOGOUT,
                    action=f"doctor_logout_{i:03d}",
                    resource="hospital_system",
                    user_id=f"doctor_{i:03d}",
                )

            # Close logger - should flush pending events
            logger.close()

            assert log_file.exists()

            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()

            assert "doctor_logout_002" in log_content


class TestAuditLogDecorator:
    """Test audit log decorator with real function scenarios."""

    def test_sync_function_audit_integration(self):
        """Test audit decorator on synchronous functions with real medical operations."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "decorator_sync.log"
            logger = AuditLogger(log_file_path=log_file, buffer_size=1)

            # Mock get_audit_logger
            with patch(
                "src.security.audit_logger.get_audit_logger", return_value=logger
            ):

                @audit_log(
                    event_type=AuditEventType.PII_ENCRYPTED,
                    action="encrypt_patient_data",
                    resource="patient_pii",
                    pii_types=["thai_national_id"],
                    compliance_tags=["encryption", "pii_protection"],
                )
                def encrypt_patient_data(patient_id: str, pii_data: str) -> str:
                    """Mock function for encrypting patient PII."""
                    time.sleep(0.1)  # Simulate encryption time
                    return f"encrypted_{pii_data}"

                # Call decorated function
                result = encrypt_patient_data("HN-2024-001234", "1234567890123")

                assert result == "encrypted_1234567890123"

                # Verify audit log
                with open(log_file, "r", encoding="utf-8") as f:
                    log_content = f.read()

                assert "encrypt_patient_data" in log_content
                assert "execution_time" in log_content
                assert "HN-2024-001234" in log_content

    def test_async_function_audit_integration(self):
        """Test audit decorator on async functions with real operations."""

        async def run_async_test():
            with tempfile.TemporaryDirectory() as temp_dir:
                log_file = Path(temp_dir) / "decorator_async.log"
                logger = AuditLogger(log_file_path=log_file, buffer_size=1)

                # Mock get_audit_logger
                with patch(
                    "src.security.audit_logger.get_audit_logger", return_value=logger
                ):

                    @audit_log(
                        event_type=AuditEventType.DOCUMENT_PROCESSED,
                        action="async_ocr_processing",
                        resource="medical_document",
                        compliance_tags=["ocr_processing", "document_analysis"],
                    )
                    async def async_process_document(
                        doc_id: str, doc_type: str
                    ) -> dict:
                        """Mock async OCR processing function."""
                        await asyncio.sleep(0.05)  # Simulate async processing
                        return {
                            "document_id": doc_id,
                            "document_type": doc_type,
                            "pages_processed": 5,
                            "pii_detected": True,
                        }

                    # Call decorated async function
                    result = await async_process_document(
                        "CSF_report_001.pdf", "cerebrospinal_fluid"
                    )

                    assert result["document_id"] == "CSF_report_001.pdf"
                    assert result["pii_detected"] is True

                    # Small delay to ensure logging completes
                    await asyncio.sleep(0.01)

                    # Verify audit log
                    with open(log_file, "r", encoding="utf-8") as f:
                        log_content = f.read()

                    assert "async_ocr_processing" in log_content
                    assert "CSF_report_001.pdf" in log_content
                    assert "cerebrospinal_fluid" in log_content

        # Run async test
        asyncio.run(run_async_test())

    def test_function_error_audit_integration(self):
        """Test audit decorator handling function errors."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "decorator_error.log"
            logger = AuditLogger(log_file_path=log_file, buffer_size=1)

            with patch(
                "src.security.audit_logger.get_audit_logger", return_value=logger
            ):

                @audit_log(
                    event_type=AuditEventType.PII_DECRYPTED,
                    action="failed_decryption_test",
                    resource="encrypted_pii",
                )
                def failing_decrypt_function(encrypted_data: str) -> str:
                    """Mock function that fails during decryption."""
                    if "invalid" in encrypted_data:
                        raise ValueError("Invalid encryption format detected")
                    return f"decrypted_{encrypted_data}"

                # Test successful call
                result = failing_decrypt_function("valid_encrypted_data")
                assert result == "decrypted_valid_encrypted_data"

                # Test failing call
                with pytest.raises(ValueError) as exc_info:
                    failing_decrypt_function("invalid_encrypted_data")

                assert "Invalid encryption format" in str(exc_info.value)

                # Verify both success and failure were logged
                with open(log_file, "r", encoding="utf-8") as f:
                    log_content = f.read()

                assert "failed_decryption_test" in log_content
                assert "valid_encrypted_data" in log_content
                assert "Invalid encryption format" in log_content

    def test_decorator_pii_redaction_integration(self):
        """Test that decorator properly redacts PII from audit logs."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "pii_redaction.log"
            logger = AuditLogger(log_file_path=log_file, buffer_size=1)

            with patch(
                "src.security.audit_logger.get_audit_logger", return_value=logger
            ):

                @audit_log(
                    event_type=AuditEventType.PII_ENCRYPTED,
                    action="process_sensitive_data",
                )
                def process_sensitive_function(
                    patient_pii: str, password: str, normal_data: str
                ) -> str:
                    """Function with sensitive parameters."""
                    return f"processed_{normal_data}"

                result = process_sensitive_function(
                    patient_pii="นายสมชาย ใจดี",
                    password="secret123",
                    normal_data="public_info",
                )

                assert result == "processed_public_info"

                # Verify PII and passwords are redacted
                with open(log_file, "r", encoding="utf-8") as f:
                    log_content = f.read()

                assert "***REDACTED***" in log_content
                assert "นายสมชาย ใจดี" not in log_content
                assert "secret123" not in log_content
                assert "public_info" in log_content


class TestGlobalAuditLogger:
    """Test global audit logger functions with real scenarios."""

    def test_initialize_audit_logger_integration(self):
        """Test global audit logger initialization."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "global_audit.log"

            logger = initialize_audit_logger(
                log_file_path=log_file, console_output=False, structured_logging=True
            )

            assert logger is not None
            assert logger.log_file_path == log_file
            assert logger.console_output is False
            assert logger.structured_logging is True

            # Test that global logger is accessible
            global_logger = get_audit_logger()
            assert global_logger == logger

    def test_get_audit_logger_default_integration(self):
        """Test getting audit logger with default initialization."""
        # Reset global instance
        import src.security.audit_logger as audit_module

        audit_module._audit_logger = None

        # Should auto-initialize
        logger = get_audit_logger()
        assert logger is not None

        # Test with real audit event
        event_id = logger.log_event(
            event_type=AuditEventType.SYSTEM_ERROR,
            action="test_default_logger",
            resource="test_system",
        )
        assert event_id is not None


@pytest.mark.integration
@pytest.mark.security
class TestAuditLoggerSecurity:
    """Security-focused tests with real threat scenarios."""

    def test_audit_log_tamper_resistance_integration(self):
        """Test audit log tamper resistance with real attack scenarios."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "tamper_test.log"
            logger = AuditLogger(log_file_path=log_file, buffer_size=1)

            # Log sensitive events
            sensitive_events = [
                ("unauthorized_access_attempt", "admin_account_breach"),
                ("privilege_escalation", "user_to_admin_escalation"),
                ("data_exfiltration", "patient_data_download"),
                ("system_compromise", "malware_detected"),
            ]

            original_content = ""
            for action, description in sensitive_events:
                logger.log_event(
                    event_type=AuditEventType.COMPLIANCE_VIOLATION,
                    action=action,
                    resource="security_system",
                    severity=AuditSeverity.CRITICAL,
                    metadata={"description": description, "threat_level": "high"},
                )

            # Read original content
            with open(log_file, "r", encoding="utf-8") as f:
                original_content = f.read()

            # Verify all events are logged
            for action, description in sensitive_events:
                assert action in original_content
                assert description in original_content

            # Verify file permissions are restrictive (if on Unix-like system)
            if os.name == "posix":
                file_stat = log_file.stat()
                file_mode = file_stat.st_mode & 0o777
                # Should not be world-writable
                assert (
                    file_mode & 0o002
                ) == 0, f"Log file is world-writable: {oct(file_mode)}"

    def test_audit_log_injection_prevention_integration(self):
        """Test prevention of log injection attacks."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "injection_test.log"
            logger = AuditLogger(log_file_path=log_file, buffer_size=1)

            # Attempt log injection attacks
            malicious_inputs = [
                "normal_user\n2024-08-05 - FAKE LOG ENTRY - admin_access_granted",
                "user_id\r\nFAKE: CRITICAL - system_compromised",
                "test\x00null_byte_injection",
                "user\t\tFAKE_TAB_INJECTION",
                'user"; DROP TABLE audit_logs; --',
                "user<script>alert('xss')</script>",
                "user\u202e\u0041\u044f\u043e\u0441\u0441\u0438\u0439\u0441\u043a\u0438\u0435",  # Unicode injection
            ]

            for malicious_input in malicious_inputs:
                logger.log_event(
                    event_type=AuditEventType.USER_LOGIN,
                    action="login_attempt",
                    resource="authentication_system",
                    user_id=malicious_input,
                    metadata={"injection_test": True},
                )

            # Read log content
            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()

            # Verify malicious content is properly escaped/handled
            assert "FAKE LOG ENTRY" not in log_content
            assert "FAKE: CRITICAL" not in log_content
            assert "DROP TABLE" not in log_content
            assert "<script>" not in log_content

            # Verify legitimate content is present
            assert "login_attempt" in log_content
            assert "injection_test" in log_content

    def test_audit_log_information_disclosure_integration(self):
        """Test prevention of sensitive information disclosure in logs."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "disclosure_test.log"
            logger = AuditLogger(log_file_path=log_file, buffer_size=1)

            # Log events with potentially sensitive data
            logger.log_event(
                event_type=AuditEventType.PII_ENCRYPTED,
                action="encrypt_patient_data",
                resource="patient_record",
                metadata={
                    "patient_id": "HN-2024-001234",  # OK to log
                    "department": "cardiology",  # OK to log
                    "encryption_key_id": "cardiac_key_2024",  # OK to log
                    "processing_time": 0.156,  # OK to log
                    # Should NOT include actual PII data
                },
            )

            # Read log content
            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()

            # Verify appropriate metadata is logged
            assert "HN-2024-001234" in log_content
            assert "cardiology" in log_content
            assert "cardiac_key_2024" in log_content

            # Verify sensitive data patterns are not present
            thai_id_pattern = r"\d{13}"  # Thai National ID pattern
            thai_name_pattern = r"นาย.+\s.+"  # Thai name pattern

            import re

            assert not re.search(thai_id_pattern, log_content)
            assert not re.search(thai_name_pattern, log_content)


@pytest.mark.performance
class TestAuditLoggerPerformance:
    """Performance tests with real hospital audit volumes."""

    def test_high_volume_logging_performance_integration(self):
        """Test performance with real hospital daily audit volume."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "performance_test.log"
            logger = AuditLogger(
                log_file_path=log_file, buffer_size=100, auto_flush_interval=10
            )

            # Simulate typical hospital daily audit volume (10,000+ events)
            event_count = 5000
            start_time = time.time()

            for i in range(event_count):
                event_type = [
                    AuditEventType.PII_DETECTED,
                    AuditEventType.PII_ENCRYPTED,
                    AuditEventType.DOCUMENT_PROCESSED,
                    AuditEventType.USER_LOGIN,
                    AuditEventType.PII_ACCESS,
                ][i % 5]

                logger.log_event(
                    event_type=event_type,
                    action=f"hospital_operation_{i:05d}",
                    resource="medical_system",
                    resource_id=f"resource_{i:05d}",
                    user_id=f"staff_{i%100:03d}",
                    metadata={
                        "department": ["emergency", "icu", "cardiology", "neurology"][
                            i % 4
                        ],
                        "shift": "day" if i % 2 == 0 else "night",
                        "patient_acuity": "high" if i % 3 == 0 else "normal",
                    },
                )

                if i % 1000 == 0 and i > 0:
                    elapsed = time.time() - start_time
                    rate = i / elapsed
                    print(f"Logged {i} events at {rate:.0f} events/sec")

            # Final flush
            logger.flush()

            total_time = time.time() - start_time
            final_rate = event_count / total_time

            # Performance requirement: > 500 events/second
            assert (
                final_rate > 500
            ), f"Logging rate too slow: {final_rate:.0f} events/sec"

            # Verify all events were logged
            with open(log_file, "r", encoding="utf-8") as f:
                log_lines = f.readlines()

            assert len(log_lines) >= event_count
            print(f"Final logging rate: {final_rate:.0f} events/second")

    def test_concurrent_logging_performance_integration(self):
        """Test concurrent logging performance with multiple hospital departments."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "concurrent_performance.log"
            logger = AuditLogger(log_file_path=log_file, buffer_size=50)

            total_events = 0
            start_time = time.time()

            def department_worker(dept_id, dept_name, event_count):
                """Simulate department generating audit events."""
                nonlocal total_events
                for i in range(event_count):
                    logger.log_event(
                        event_type=AuditEventType.PII_DECRYPTED,
                        action=f"{dept_name}_patient_access_{i:04d}",
                        resource="patient_record",
                        user_id=f"{dept_name}_staff_{i%10:02d}",
                        metadata={
                            "department": dept_name,
                            "department_id": dept_id,
                            "access_time": f"2024-08-05T{8+dept_id:02d}:{i%60:02d}:00Z",
                        },
                    )
                    total_events += 1

            # Start workers for different departments
            departments = [
                (0, "emergency", 500),
                (1, "icu", 300),
                (2, "cardiology", 400),
                (3, "neurology", 250),
                (4, "orthopedics", 350),
            ]

            threads = []
            for dept_id, dept_name, event_count in departments:
                thread = threading.Thread(
                    target=department_worker, args=(dept_id, dept_name, event_count)
                )
                threads.append(thread)
                thread.start()

            # Wait for all threads
            for thread in threads:
                thread.join()

            logger.flush()

            total_time = time.time() - start_time
            concurrent_rate = total_events / total_time

            # Concurrent performance should still be good
            assert (
                concurrent_rate > 300
            ), f"Concurrent logging rate too slow: {concurrent_rate:.0f} events/sec"

            # Verify all departments logged events
            with open(log_file, "r", encoding="utf-8") as f:
                log_content = f.read()

            for _, dept_name, _ in departments:
                assert f"{dept_name}_patient_access" in log_content
                assert f"{dept_name}_staff" in log_content

            print(f"Concurrent logging rate: {concurrent_rate:.0f} events/second")

    def test_memory_usage_audit_logging_integration(self):
        """Test memory usage with large audit log volumes."""
        import gc

        import psutil

        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = Path(temp_dir) / "memory_test.log"
            logger = AuditLogger(
                log_file_path=log_file, buffer_size=1  # Force frequent flushes
            )

            # Generate large volume of realistic audit events
            for i in range(2000):
                logger.log_event(
                    event_type=AuditEventType.PII_DETECTED,
                    action=f"process_large_medical_document_{i:04d}",
                    resource="medical_document",
                    resource_id=f"document_{i:04d}.pdf",
                    metadata={
                        "document_size_mb": 2.5 + (i % 10) * 0.5,
                        "page_count": 5 + (i % 15),
                        "pii_detected_count": i % 25,
                        "processing_department": ["emergency", "icu", "cardiology"][
                            i % 3
                        ],
                        "physician_id": f"doctor_{i%50:03d}",
                        "patient_hn": f"HN-2024-{i:06d}",
                        "document_type": (
                            "lab_report" if i % 2 == 0 else "imaging_report"
                        ),
                        "confidentiality_level": "high" if i % 3 == 0 else "standard",
                    },
                    pii_types=["thai_national_id", "patient_name", "medical_record"],
                    compliance_tags=[
                        "hipaa_equivalent",
                        "thai_medical_data",
                        "pii_detection",
                    ],
                )

        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - initial_memory

        # Clean up
        logger.close()
        del logger
        gc.collect()

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_after_cleanup = final_memory - initial_memory

        # Memory usage should be reasonable
        assert memory_increase < 50, f"Memory usage too high: {memory_increase:.1f}MB"

        # Memory should be mostly freed
        assert (
            memory_after_cleanup < 10
        ), f"Potential memory leak: {memory_after_cleanup:.1f}MB"

        print(
            f"Memory usage: {memory_increase:.1f}MB peak, {memory_after_cleanup:.1f}MB after cleanup"
        )


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
