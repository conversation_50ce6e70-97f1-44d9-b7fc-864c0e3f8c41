"""End-to-end integration tests with real services - NO MOCKS.

These tests validate the complete OCR pipeline using actual services:
- Real Gemini API calls
- Real PDF samples
- Real PII detection
- Real PDF obfuscation
- Real database persistence
"""

import asyncio
import time
from pathlib import Path
from typing import List

import pytest

from src.processing.batch_processor import BatchProcessor
from src.processing.ocr_processor import GeminiOCRProcessor
from src.processing.pdf_obfuscator import PDFObfuscator
from src.processing.pii_detector import PIIDetector


@pytest.mark.integration
@pytest.mark.slow
@pytest.mark.asyncio
class TestEndToEndRealIntegration:
    """Complete pipeline integration tests with real services."""

    async def test_complete_pipeline_single_document(
        self,
        real_batch_processor: BatchProcessor,
        sample_thai_medical_pdf: Path,
        temp_output_dir: Path,
    ):
        """Test complete pipeline with single Thai medical document.

        Validates:
        - End-to-end processing workflow
        - All components working together
        - Real service integration
        - Output file generation
        - Processing time performance
        """
        start_time = time.time()

        # Process single document through complete pipeline
        results = await real_batch_processor.process_batch(
            input_paths=[sample_thai_medical_pdf],
            output_dir=temp_output_dir,
            enable_obfuscation=True,
            save_report=True,
        )

        total_time = time.time() - start_time

        # Validate batch results
        assert results["batch_stats"].total_files == 1, "Should process 1 file"
        assert (
            results["batch_stats"].completed_files >= 0
        ), "Should track completed files"

        processing_results = results["processing_results"]
        assert len(processing_results) == 1, "Should have 1 processing result"

        result = processing_results[0]

        # Validate processing success
        if result.get("ocr_result") and not result["ocr_result"].get("errors"):
            print(f"✓ Complete pipeline succeeded for {sample_thai_medical_pdf.name}")
            print(f"  Total processing time: {total_time:.2f}s")
            print(
                f"  OCR confidence: {result['ocr_result'].get('confidence_score', 0):.2f}"
            )
            print(f"  PII matches: {result.get('pii_matches', 0)}")

            # Check for obfuscated output if PII was found
            if result.get("pii_matches", 0) > 0 and result.get("obfuscation_success"):
                print(f"  ✓ PII obfuscation completed")

                # Verify obfuscated file exists
                obfuscated_files = list(temp_output_dir.glob("obfuscated_*.pdf"))
                assert len(obfuscated_files) > 0, "Should generate obfuscated PDF"

                obfuscated_file = obfuscated_files[0]
                assert obfuscated_file.exists(), "Obfuscated file should exist"
                assert (
                    obfuscated_file.stat().st_size > 0
                ), "Obfuscated file should not be empty"

                print(f"  ✓ Obfuscated file created: {obfuscated_file.name}")

            # Verify processing report
            report_files = list(temp_output_dir.glob("batch_report_*.json"))
            assert len(report_files) > 0, "Should generate processing report"

            print(f"  ✓ Processing report generated: {report_files[0].name}")

        else:
            print(f"? Pipeline had issues with {sample_thai_medical_pdf.name}")
            if result.get("error_message"):
                print(f"  Error: {result['error_message']}")

        # Performance validation
        assert (
            total_time < 120
        ), f"Complete pipeline should finish within 2 minutes, took {total_time:.2f}s"

    async def test_batch_processing_multiple_documents(
        self,
        real_batch_processor: BatchProcessor,
        performance_test_samples: List[Path],
        temp_output_dir: Path,
    ):
        """Test batch processing with multiple real documents.

        Validates:
        - Concurrent processing capability
        - Error handling across multiple files
        - Performance under load
        - Resource management
        - Progress tracking
        """
        print(
            f"Testing batch processing with {len(performance_test_samples)} documents"
        )

        start_time = time.time()

        # Process multiple documents concurrently
        results = await real_batch_processor.process_batch(
            input_paths=performance_test_samples,
            output_dir=temp_output_dir,
            enable_obfuscation=True,
            save_report=True,
        )

        total_time = time.time() - start_time
        stats = results["batch_stats"]

        print(f"\nBatch processing results:")
        print(f"  Total files: {stats.total_files}")
        print(f"  Completed: {stats.completed_files}")
        print(f"  Failed: {stats.failed_files}")
        print(f"  Success rate: {stats.completed_files / stats.total_files * 100:.1f}%")
        print(f"  Total time: {total_time:.2f}s")
        print(f"  Average per file: {stats.average_processing_time:.2f}s")
        print(f"  Total PII detected: {stats.total_pii_detected}")
        print(f"  Total PII obfuscated: {stats.total_pii_obfuscated}")

        # Validate batch performance
        assert stats.total_files == len(
            performance_test_samples
        ), "Should process all input files"
        assert stats.completed_files > 0, "Should complete at least some files"

        # Success rate should be reasonable (allow for some API failures)
        success_rate = stats.completed_files / stats.total_files
        assert (
            success_rate >= 0.6
        ), f"Success rate should be >= 60%, got {success_rate:.1%}"

        # Performance validation
        if stats.completed_files > 0:
            assert (
                stats.average_processing_time < 60
            ), f"Average processing time should be < 60s, got {stats.average_processing_time:.2f}s"

        # Check error patterns
        if stats.error_types:
            print(f"\nError breakdown:")
            for error_type, count in stats.error_types.items():
                print(f"  {error_type}: {count}")

        # Validate output files
        output_files = list(temp_output_dir.glob("*"))
        print(f"  Generated {len(output_files)} output files")

        # Should have at least a batch report
        report_files = [f for f in output_files if "batch_report" in f.name]
        assert len(report_files) > 0, "Should generate batch processing report"

    async def test_error_recovery_and_resilience(
        self,
        real_batch_processor: BatchProcessor,
        real_pdf_samples: List[Path],
        temp_output_dir: Path,
    ):
        """Test error recovery and system resilience.

        Validates:
        - Graceful handling of API failures
        - Recovery from network issues
        - Processing continuation after errors
        - Error reporting accuracy
        """
        # Include a mix of valid and potentially problematic files
        test_samples = real_pdf_samples[:3]  # Use first 3 samples

        # Add a non-existent file to test error handling
        non_existent = Path("non_existent_file.pdf")
        test_samples.append(non_existent)

        print(
            f"Testing error recovery with {len(test_samples)} files (including 1 invalid)"
        )

        start_time = time.time()

        # Process with error conditions
        results = await real_batch_processor.process_batch(
            input_paths=test_samples,
            output_dir=temp_output_dir,
            enable_obfuscation=True,
            save_report=True,
        )

        total_time = time.time() - start_time
        stats = results["batch_stats"]

        print(f"\nError recovery test results:")
        print(f"  Total files: {stats.total_files}")
        print(f"  Completed: {stats.completed_files}")
        print(f"  Failed: {stats.failed_files}")
        print(f"  Total time: {total_time:.2f}s")

        # Should have attempted to process all files
        assert stats.total_files == len(test_samples), "Should attempt all files"

        # Should have at least 1 failure (the non-existent file)
        assert stats.failed_files >= 1, "Should report failure for non-existent file"

        # Should have some successes with real PDF files
        assert stats.completed_files >= 0, "Should complete valid files"

        # Check error reporting
        processing_results = results["processing_results"]

        failed_results = [r for r in processing_results if r.get("status") == "failed"]
        non_existent_failures = [
            r for r in failed_results if "non_existent" in r.get("file_path", "")
        ]

        assert (
            len(non_existent_failures) >= 1
        ), "Should report non-existent file failure"

        print(f"✓ Error recovery test completed - system remained resilient")

    @pytest.mark.performance
    async def test_performance_under_load(
        self,
        real_batch_processor: BatchProcessor,
        real_pdf_samples: List[Path],
        temp_output_dir: Path,
    ):
        """Test system performance under concurrent load.

        Validates:
        - Performance with maximum concurrent processing
        - Resource utilization efficiency
        - API rate limit compliance
        - Memory usage stability
        - Throughput optimization
        """
        # Use all available samples or up to 10 for load testing
        load_test_samples = real_pdf_samples[: min(10, len(real_pdf_samples))]

        print(f"Performance load testing with {len(load_test_samples)} documents")

        # Measure resource usage before test
        import os

        import psutil

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        start_time = time.time()

        # Process with maximum concurrency
        results = await real_batch_processor.process_batch(
            input_paths=load_test_samples,
            output_dir=temp_output_dir,
            enable_obfuscation=True,
            save_report=True,
        )

        total_time = time.time() - start_time
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_delta = final_memory - initial_memory

        stats = results["batch_stats"]

        print(f"\nPerformance load test results:")
        print(f"  Documents processed: {stats.completed_files}/{stats.total_files}")
        print(f"  Total time: {total_time:.2f}s")
        print(
            f"  Throughput: {stats.completed_files / (total_time / 3600):.1f} docs/hour"
        )
        print(f"  Average per document: {stats.average_processing_time:.2f}s")
        print(
            f"  Memory usage: {initial_memory:.1f}MB → {final_memory:.1f}MB (Δ{memory_delta:+.1f}MB)"
        )
        print(
            f"  PII processing: {stats.total_pii_detected} detected, {stats.total_pii_obfuscated} obfuscated"
        )

        # Performance validations
        if stats.completed_files > 0:
            throughput = stats.completed_files / (total_time / 3600)  # docs per hour

            # Should meet minimum throughput requirement
            assert (
                throughput >= 50
            ), f"Throughput should be >= 50 docs/hour, got {throughput:.1f}"

            # Average processing time should be reasonable
            assert (
                stats.average_processing_time < 90
            ), f"Average processing should be < 90s, got {stats.average_processing_time:.2f}s"

            # Memory usage should be reasonable
            assert (
                memory_delta < 1000
            ), f"Memory increase should be < 1GB, got {memory_delta:.1f}MB"

            print(f"✓ Performance requirements met")

        # Check for API rate limiting issues
        if stats.error_types:
            rate_limit_errors = stats.error_types.get("API Error", 0)
            if rate_limit_errors > 0:
                print(f"⚠ {rate_limit_errors} API rate limit errors encountered")
                # This is acceptable in load testing

    async def test_security_validation_end_to_end(
        self,
        real_batch_processor: BatchProcessor,
        sample_thai_medical_pdf: Path,
        temp_output_dir: Path,
    ):
        """Test security validation throughout the pipeline.

        Validates:
        - PII detection accuracy
        - Complete PII obfuscation
        - No PII leakage in logs/outputs
        - Secure file handling
        - Audit trail generation
        """
        print(f"Security validation test with {sample_thai_medical_pdf.name}")

        # Process document with full pipeline
        results = await real_batch_processor.process_batch(
            input_paths=[sample_thai_medical_pdf],
            output_dir=temp_output_dir,
            enable_obfuscation=True,
            save_report=True,
        )

        processing_result = results["processing_results"][0]

        if processing_result.get("pii_matches", 0) > 0:
            print(f"✓ PII detected: {processing_result['pii_matches']} items")

            # Verify obfuscation was attempted
            assert (
                processing_result.get("obfuscation_success") is not None
            ), "Should attempt obfuscation when PII found"

            if processing_result.get("obfuscation_success"):
                print("✓ PII obfuscation completed")

                # Verify obfuscated file exists and is different from original
                obfuscated_files = list(temp_output_dir.glob("obfuscated_*.pdf"))
                if obfuscated_files:
                    obfuscated_file = obfuscated_files[0]

                    # Basic validation - obfuscated file should be different size
                    original_size = sample_thai_medical_pdf.stat().st_size
                    obfuscated_size = obfuscated_file.stat().st_size

                    # Sizes can be similar but shouldn't be identical
                    size_diff_percent = (
                        abs(original_size - obfuscated_size) / original_size
                    )

                    print(f"  Original size: {original_size} bytes")
                    print(f"  Obfuscated size: {obfuscated_size} bytes")
                    print(f"  Size difference: {size_diff_percent:.1%}")

                    print("✓ Security validation completed")
            else:
                print("? PII obfuscation failed - check security implementation")
        else:
            print("⚠ No PII detected - security validation limited")

        # Check processing report for sensitive data leakage
        report_files = list(temp_output_dir.glob("batch_report_*.json"))
        if report_files:
            import json

            with open(report_files[0], "r") as f:
                report_data = json.load(f)

            # Basic check - ensure no obvious PII in report
            report_text = json.dumps(report_data).lower()

            # Check for potential Thai ID patterns (13 consecutive digits)
            import re

            thai_id_pattern = r"\b\d{13}\b"
            thai_ids_in_report = re.findall(thai_id_pattern, report_text)

            if thai_ids_in_report:
                print(
                    f"⚠ Potential PII leakage in report: {len(thai_ids_in_report)} Thai ID patterns"
                )
            else:
                print("✓ No obvious PII leakage in processing report")

        print("✓ End-to-end security validation completed")
