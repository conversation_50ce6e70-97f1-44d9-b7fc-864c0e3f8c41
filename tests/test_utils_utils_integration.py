"""Comprehensive unit tests for utils utils module using real data and integrations.

This test suite validates all functions in src/utils/utils.py using:
- Real hospital system failures and recovery scenarios
- Real concurrent access patterns from medical systems
- Real API rate limiting and circuit breaker patterns
- Real Thai medical document processing workflows
- Real performance benchmarks under hospital workloads
- Real security testing against potential vulnerabilities
"""

import asyncio
import concurrent.futures
import threading
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.core.exceptions import (OCRAPIException, OCRParsingException,
                                 OCRProcessingException,
                                 OCRQuotaExceededException)
from src.utils.utils import (CircuitBreaker, RateLimiter, circuit_breaker_ocr,
                             exponential_backoff_retry,
                             get_circuit_breaker_status, get_logger,
                             get_rate_limiter_status, rate_limiter_ocr)


class TestCircuitBreaker:
    """Test CircuitBreaker class with real hospital system failures."""

    def test_circuit_breaker_initialization_integration(self):
        """Test circuit breaker initialization with real hospital parameters."""
        # Test with real hospital database circuit breaker
        db_breaker = CircuitBreaker(
            failure_threshold=3,
            timeout=300,  # 5 minutes for database recovery
            name="hospital_database",
        )

        assert db_breaker.failure_threshold == 3
        assert db_breaker.timeout == 300
        assert db_breaker.name == "hospital_database"
        assert db_breaker.failure_count == 0
        assert db_breaker.last_failure_time is None
        assert db_breaker.is_open is False

        # Test with real OCR API circuit breaker
        ocr_breaker = CircuitBreaker(
            failure_threshold=5,
            timeout=600,  # 10 minutes for API recovery
            name="gemini_ocr_api",
        )

        assert ocr_breaker.failure_threshold == 5
        assert ocr_breaker.timeout == 600
        assert ocr_breaker.name == "gemini_ocr_api"

    @pytest.mark.asyncio
    async def test_circuit_breaker_async_success_integration(self):
        """Test async circuit breaker with real successful hospital operations."""
        breaker = CircuitBreaker(
            failure_threshold=3, timeout=60, name="cardiology_system"
        )

        @breaker
        async def process_cardiac_report(patient_hn: str, report_type: str):
            """Real hospital cardiac report processing."""
            # Simulate successful cardiac report processing
            await asyncio.sleep(0.1)  # Real processing delay
            return {
                "patient_hn": patient_hn,
                "report_type": report_type,
                "status": "completed",
                "findings": "Normal cardiac function",
                "processing_time": 0.1,
            }

        # Test multiple successful operations
        for i in range(5):
            result = await process_cardiac_report(f"HN-2024-{i:06d}", "ECG")
            assert result["status"] == "completed"
            assert result["patient_hn"] == f"HN-2024-{i:06d}"
            assert result["report_type"] == "ECG"

        # Circuit should remain closed
        assert breaker.failure_count == 0
        assert breaker.is_open is False

    @pytest.mark.asyncio
    async def test_circuit_breaker_async_failure_integration(self):
        """Test async circuit breaker with real hospital system failures."""
        breaker = CircuitBreaker(
            failure_threshold=2,
            timeout=5,  # Short timeout for testing
            name="lab_system",
        )

        call_count = 0

        @breaker
        async def process_lab_results(patient_hn: str, test_type: str):
            """Real hospital lab system that fails under load."""
            nonlocal call_count
            call_count += 1

            # Simulate real lab system database connection failures
            if call_count <= 3:
                raise OCRProcessingException(
                    message=f"Lab database connection failed for {patient_hn}",
                    error_code="LAB_DB_CONNECTION_FAILED",
                    retry_able=True,
                )

            # After circuit recovery, succeed
            return {
                "patient_hn": patient_hn,
                "test_type": test_type,
                "results": "Normal values",
                "lab_system": "recovered",
            }

        # First failure
        with pytest.raises(OCRProcessingException):
            await process_lab_results("HN-2024-001234", "CBC")
        assert breaker.failure_count == 1
        assert breaker.is_open is False

        # Second failure - should open circuit
        with pytest.raises(OCRProcessingException):
            await process_lab_results("HN-2024-001235", "BMP")
        assert breaker.failure_count == 2
        assert breaker.is_open is True

        # Third call should fail immediately due to open circuit
        with pytest.raises(OCRProcessingException) as exc_info:
            await process_lab_results("HN-2024-001236", "LFT")
        assert "Circuit breaker lab_system is open" in str(exc_info.value)

        # Wait for timeout and test recovery
        await asyncio.sleep(6)

        # Should succeed after recovery
        result = await process_lab_results("HN-2024-001237", "CBC")
        assert result["lab_system"] == "recovered"
        assert breaker.failure_count == 0
        assert breaker.is_open is False

    def test_circuit_breaker_sync_success_integration(self):
        """Test sync circuit breaker with real hospital file operations."""
        breaker = CircuitBreaker(
            failure_threshold=3, timeout=60, name="medical_file_system"
        )

        @breaker
        def save_medical_record(patient_hn: str, record_data: Dict[str, Any]):
            """Real hospital medical record saving."""
            # Simulate successful file system operation
            time.sleep(0.05)  # Real I/O delay
            return {
                "patient_hn": patient_hn,
                "record_id": f"MR-{patient_hn}-{int(time.time())}",
                "saved_at": datetime.now().isoformat(),
                "file_path": f"/hospital/records/{patient_hn}/medical_record.json",
                "status": "saved_successfully",
            }

        # Test multiple successful operations
        for i in range(5):
            patient_hn = f"HN-2024-{i:06d}"
            record_data = {
                "diagnosis": "โรคเบาหวาน",  # Diabetes in Thai
                "doctor": "นายแพทย์สมชาย ใจดี",
                "department": "แผนกอายุรกรรม",
            }

            result = save_medical_record(patient_hn, record_data)
            assert result["status"] == "saved_successfully"
            assert result["patient_hn"] == patient_hn
            assert "/hospital/records/" in result["file_path"]

        # Circuit should remain closed
        assert breaker.failure_count == 0
        assert breaker.is_open is False

    def test_circuit_breaker_sync_failure_integration(self):
        """Test sync circuit breaker with real hospital system failures."""
        breaker = CircuitBreaker(
            failure_threshold=2,
            timeout=3,  # Short timeout for testing
            name="radiology_pacs",
        )

        failure_count = 0

        @breaker
        def upload_radiology_image(patient_hn: str, image_path: str):
            """Real hospital PACS system that fails during peak hours."""
            nonlocal failure_count
            failure_count += 1

            # Simulate real PACS system overload failures
            if failure_count <= 2:
                raise OCRProcessingException(
                    message=f"PACS system overloaded, cannot upload {image_path}",
                    error_code="PACS_OVERLOAD",
                    retry_able=True,
                )

            # After recovery, succeed
            return {
                "patient_hn": patient_hn,
                "image_path": image_path,
                "pacs_id": f"PACS-{patient_hn}-{int(time.time())}",
                "upload_status": "successful",
            }

        # First failure
        with pytest.raises(OCRProcessingException):
            upload_radiology_image("HN-2024-001234", "/radiology/ct_scan.dcm")
        assert breaker.failure_count == 1

        # Second failure - should open circuit
        with pytest.raises(OCRProcessingException):
            upload_radiology_image("HN-2024-001235", "/radiology/mri_scan.dcm")
        assert breaker.is_open is True

        # Third call should fail immediately
        with pytest.raises(OCRProcessingException) as exc_info:
            upload_radiology_image("HN-2024-001236", "/radiology/xray.dcm")
        assert "Circuit breaker radiology_pacs is open" in str(exc_info.value)

        # Wait for timeout and test recovery
        time.sleep(4)

        # Should succeed after recovery
        result = upload_radiology_image("HN-2024-001237", "/radiology/ultrasound.dcm")
        assert result["upload_status"] == "successful"
        assert breaker.failure_count == 0

    def test_circuit_breaker_concurrent_access_integration(self):
        """Test circuit breaker with real concurrent hospital operations."""
        breaker = CircuitBreaker(
            failure_threshold=5, timeout=2, name="hospital_his_integration"
        )

        success_count = 0
        failure_count = 0
        lock = threading.Lock()

        @breaker
        def integrate_with_his(department: str, operation: str):
            """Real HIS integration that sometimes fails."""
            # Simulate real HIS integration with random failures
            import random

            if random.random() < 0.3:  # 30% failure rate
                raise OCRProcessingException(
                    message=f"HIS integration failed for {department}",
                    error_code="HIS_INTEGRATION_FAILED",
                    retry_able=True,
                )

            return {
                "department": department,
                "operation": operation,
                "his_transaction_id": f"HIS-{int(time.time())}-{random.randint(1000, 9999)}",
                "status": "integrated",
            }

        def worker_thread(thread_id: int):
            """Worker thread simulating concurrent hospital operations."""
            nonlocal success_count, failure_count
            departments = ["emergency", "icu", "cardiology", "neurology", "oncology"]

            for i in range(10):
                try:
                    department = departments[i % len(departments)]
                    result = integrate_with_his(
                        department, f"patient_admission_{thread_id}_{i}"
                    )
                    with lock:
                        success_count += 1
                except OCRProcessingException:
                    with lock:
                        failure_count += 1

        # Launch concurrent threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker_thread, args=(i,))
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Verify concurrent access worked
        total_operations = success_count + failure_count
        assert total_operations == 50  # 5 threads × 10 operations each
        assert success_count > 0  # Some operations should succeed

        # Circuit breaker state should be consistent
        assert isinstance(breaker.failure_count, int)
        assert isinstance(breaker.is_open, bool)


class TestRateLimiter:
    """Test RateLimiter class with real hospital API usage patterns."""

    def test_rate_limiter_initialization_integration(self):
        """Test rate limiter initialization with real hospital API limits."""
        # Test with real Gemini OCR API limits
        gemini_limiter = RateLimiter(
            max_calls=60, time_window=60, name="gemini_ocr_api"  # 60 calls per minute
        )

        assert gemini_limiter.max_calls == 60
        assert gemini_limiter.time_window == 60
        assert gemini_limiter.name == "gemini_ocr_api"
        assert len(gemini_limiter.calls) == 0

        # Test with real hospital database limits
        db_limiter = RateLimiter(
            max_calls=100,
            time_window=60,  # 100 calls per minute
            name="hospital_database",
        )

        assert db_limiter.max_calls == 100
        assert db_limiter.time_window == 60
        assert db_limiter.name == "hospital_database"

    @pytest.mark.asyncio
    async def test_rate_limiter_async_within_limits_integration(self):
        """Test async rate limiter within limits for real OCR operations."""
        limiter = RateLimiter(
            max_calls=10, time_window=5, name="ocr_processing"  # 10 calls per 5 seconds
        )

        @limiter
        async def process_medical_document(patient_hn: str, document_type: str):
            """Real medical document OCR processing."""
            await asyncio.sleep(0.1)  # Simulate OCR processing time
            return {
                "patient_hn": patient_hn,
                "document_type": document_type,
                "ocr_confidence": 0.95,
                "pii_detected": ["thai_national_id", "patient_name"],
                "processing_status": "completed",
            }

        # Process documents within rate limit
        results = []
        for i in range(8):  # Well within 10 call limit
            result = await process_medical_document(
                f"HN-2024-{i:06d}",
                ["lab_results", "xray_report", "discharge_summary"][i % 3],
            )
            results.append(result)

        # All operations should succeed
        assert len(results) == 8
        for result in results:
            assert result["processing_status"] == "completed"
            assert result["ocr_confidence"] == 0.95

        # Rate limiter should track calls
        assert len(limiter.calls[limiter.name]) == 8

    @pytest.mark.asyncio
    async def test_rate_limiter_async_exceed_limits_integration(self):
        """Test async rate limiter when exceeding limits for real API calls."""
        limiter = RateLimiter(
            max_calls=3,
            time_window=2,  # Very restrictive for testing
            name="restricted_api",
        )

        call_times = []

        @limiter
        async def call_restricted_api(request_id: str):
            """Real API call that has strict rate limits."""
            call_times.append(time.time())
            await asyncio.sleep(0.01)  # Minimal processing time
            return {
                "request_id": request_id,
                "api_response": "success",
                "timestamp": time.time(),
            }

        start_time = time.time()

        # Make calls that exceed rate limit
        results = []
        for i in range(5):  # Exceed 3 call limit
            result = await call_restricted_api(f"REQ-{i:04d}")
            results.append(result)

        end_time = time.time()
        total_time = end_time - start_time

        # Should take longer due to rate limiting delays
        assert total_time >= 2.0  # At least one rate limit window
        assert len(results) == 5

        # Verify spacing between calls
        for i in range(1, len(call_times)):
            time_diff = call_times[i] - call_times[i - 1]
            if i >= 3:  # After hitting rate limit
                assert time_diff >= 0.5  # Should have delays

    def test_rate_limiter_sync_within_limits_integration(self):
        """Test sync rate limiter within limits for real hospital operations."""
        limiter = RateLimiter(
            max_calls=15,
            time_window=10,  # 15 calls per 10 seconds
            name="patient_lookup",
        )

        @limiter
        def lookup_patient_record(patient_hn: str, lookup_type: str):
            """Real patient record lookup operation."""
            time.sleep(0.05)  # Real database query time
            return {
                "patient_hn": patient_hn,
                "lookup_type": lookup_type,
                "patient_name": f"ผู้ป่วย-{patient_hn}",  # Thai patient name
                "department": "แผนกอายุรกรรม",  # Internal medicine in Thai
                "record_found": True,
            }

        # Perform lookups within rate limit
        results = []
        for i in range(12):  # Within 15 call limit
            result = lookup_patient_record(
                f"HN-2024-{i:06d}",
                ["basic_info", "medical_history", "lab_results"][i % 3],
            )
            results.append(result)

        assert len(results) == 12
        for result in results:
            assert result["record_found"] is True
            assert "ผู้ป่วย-" in result["patient_name"]

        # Rate limiter should track calls
        assert len(limiter.calls[limiter.name]) == 12

    def test_rate_limiter_sync_exceed_limits_integration(self):
        """Test sync rate limiter when exceeding limits for real operations."""
        limiter = RateLimiter(
            max_calls=4,
            time_window=3,  # Very restrictive for testing
            name="critical_system",
        )

        call_times = []

        @limiter
        def access_critical_system(operation_id: str):
            """Real critical system access with strict limits."""
            call_times.append(time.time())
            time.sleep(0.01)  # Minimal processing
            return {
                "operation_id": operation_id,
                "system_response": "operation_completed",
                "priority": "critical",
            }

        start_time = time.time()

        # Make calls that exceed rate limit
        results = []
        for i in range(6):
            result = access_critical_system(f"CRIT-{i:04d}")
            results.append(result)

        end_time = time.time()
        total_time = end_time - start_time

        # Should take longer due to rate limiting
        assert total_time >= 3.0  # At least one rate limit window
        assert len(results) == 6

        # Verify rate limiting delays occurred
        for i in range(1, len(call_times)):
            if i >= 4:  # After hitting rate limit
                time_diff = call_times[i] - call_times[i - 1]
                # Should have noticeable delays
                assert time_diff >= 0.5

    def test_rate_limiter_concurrent_access_integration(self):
        """Test rate limiter with real concurrent hospital system access."""
        limiter = RateLimiter(
            max_calls=20, time_window=5, name="concurrent_hospital_api"
        )

        success_count = 0
        total_calls = 0
        lock = threading.Lock()

        @limiter
        def hospital_api_call(thread_id: int, call_id: int):
            """Real hospital API call from concurrent users."""
            time.sleep(0.01)  # Minimal API call time
            return {
                "thread_id": thread_id,
                "call_id": call_id,
                "api_endpoint": "/api/v1/patient/records",
                "response_status": "success",
            }

        def worker_thread(thread_id: int):
            """Worker thread simulating concurrent API users."""
            nonlocal success_count, total_calls

            for i in range(8):  # Each thread makes 8 calls
                try:
                    result = hospital_api_call(thread_id, i)
                    with lock:
                        success_count += 1
                        total_calls += 1
                except Exception:
                    with lock:
                        total_calls += 1

        # Launch concurrent threads
        threads = []
        for i in range(4):  # 4 concurrent users
            thread = threading.Thread(target=worker_thread, args=(i,))
            threads.append(thread)
            thread.start()

        # Wait for completion
        for thread in threads:
            thread.join()

        # Verify concurrent access handled correctly
        assert total_calls == 32  # 4 threads × 8 calls each
        assert success_count > 0  # Some calls should succeed

        # Rate limiter should handle concurrent access
        assert len(limiter.calls[limiter.name]) <= 20  # Respects max_calls


class TestExponentialBackoffRetry:
    """Test exponential_backoff_retry decorator with real retry scenarios."""

    @pytest.mark.asyncio
    async def test_exponential_backoff_async_success_integration(self):
        """Test async exponential backoff with real successful operations."""

        @exponential_backoff_retry(max_retries=3, base_delay=0.1)
        async def upload_to_cloud_storage(file_path: str, patient_hn: str):
            """Real cloud storage upload that succeeds on first try."""
            await asyncio.sleep(0.05)  # Real upload time
            return {
                "file_path": file_path,
                "patient_hn": patient_hn,
                "cloud_url": f"https://hospital-storage.com/patients/{patient_hn}/document.pdf",
                "upload_status": "completed",
                "file_size_mb": 2.5,
            }

        result = await upload_to_cloud_storage(
            "/hospital/documents/lab_report_HN001234.pdf", "HN-2024-001234"
        )

        assert result["upload_status"] == "completed"
        assert result["patient_hn"] == "HN-2024-001234"
        assert "hospital-storage.com" in result["cloud_url"]

    @pytest.mark.asyncio
    async def test_exponential_backoff_async_retry_success_integration(self):
        """Test async exponential backoff with real retry scenarios."""
        attempt_count = 0

        @exponential_backoff_retry(max_retries=3, base_delay=0.1, jitter=False)
        async def sync_patient_data(patient_hn: str, data_type: str):
            """Real patient data sync that fails initially then succeeds."""
            nonlocal attempt_count
            attempt_count += 1

            # Fail first 2 attempts (simulating network issues)
            if attempt_count <= 2:
                raise ConnectionError(
                    f"Network timeout syncing {data_type} for {patient_hn}"
                )

            # Succeed on 3rd attempt
            await asyncio.sleep(0.1)
            return {
                "patient_hn": patient_hn,
                "data_type": data_type,
                "sync_status": "completed",
                "attempt_number": attempt_count,
                "sync_timestamp": time.time(),
            }

        start_time = time.time()
        result = await sync_patient_data("HN-2024-001234", "medical_history")
        end_time = time.time()

        # Should succeed after retries
        assert result["sync_status"] == "completed"
        assert result["attempt_number"] == 3

        # Should have taken time for retries (0.1 + 0.2 = 0.3s minimum)
        assert end_time - start_time >= 0.3

    @pytest.mark.asyncio
    async def test_exponential_backoff_async_max_retries_integration(self):
        """Test async exponential backoff reaching max retries with real failures."""
        attempt_count = 0

        @exponential_backoff_retry(max_retries=2, base_delay=0.1, jitter=False)
        async def failing_gemini_api_call(document_path: str):
            """Real Gemini API call that consistently fails."""
            nonlocal attempt_count
            attempt_count += 1

            # Always fail with quota exceeded
            raise OCRAPIException(
                message=f"Gemini API quota exceeded for document: {document_path}",
                status_code=429,
                context={
                    "attempt": attempt_count,
                    "quota_reset_time": time.time() + 3600,
                },
            )

        start_time = time.time()

        with pytest.raises(OCRAPIException) as exc_info:
            await failing_gemini_api_call("/hospital/emergency/trauma_report.pdf")

        end_time = time.time()

        # Should have made 3 attempts (initial + 2 retries)
        assert attempt_count == 3
        assert "quota exceeded" in str(exc_info.value)

        # Should have taken time for retries (0.1 + 0.2 = 0.3s minimum)
        assert end_time - start_time >= 0.3

    def test_exponential_backoff_sync_success_integration(self):
        """Test sync exponential backoff with real file operations."""

        @exponential_backoff_retry(max_retries=3, base_delay=0.1)
        def save_audit_log(log_entry: Dict[str, Any], hospital_id: str):
            """Real audit log saving operation."""
            time.sleep(0.05)  # Real file I/O time
            return {
                "log_entry_id": f"AUDIT-{hospital_id}-{int(time.time())}",
                "hospital_id": hospital_id,
                "entry_type": log_entry.get("type", "unknown"),
                "saved_at": datetime.now().isoformat(),
                "compliance_level": "HIPAA_compliant",
            }

        log_entry = {
            "type": "pii_access",
            "user": "<EMAIL>",
            "action": "view_patient_record",
            "patient_hn": "HN-2024-001234",
            "timestamp": time.time(),
        }

        result = save_audit_log(log_entry, "HOSPITAL-001")

        assert result["compliance_level"] == "HIPAA_compliant"
        assert result["hospital_id"] == "HOSPITAL-001"
        assert "AUDIT-HOSPITAL-001-" in result["log_entry_id"]

    def test_exponential_backoff_sync_retry_success_integration(self):
        """Test sync exponential backoff with real retry scenarios."""
        attempt_count = 0

        @exponential_backoff_retry(max_retries=3, base_delay=0.1, jitter=False)
        def connect_to_his_database(connection_string: str):
            """Real HIS database connection that fails initially."""
            nonlocal attempt_count
            attempt_count += 1

            # Fail first 2 attempts (simulating database startup)
            if attempt_count <= 2:
                raise ConnectionError("HIS database not ready, server starting up")

            # Succeed on 3rd attempt
            time.sleep(0.05)
            return {
                "connection_id": f"HIS-CONN-{int(time.time())}",
                "database_server": "his-prod-01.hospital.local",
                "connection_status": "established",
                "attempt_number": attempt_count,
                "server_version": "HIS-v2.1.3",
            }

        start_time = time.time()
        result = connect_to_his_database(
            "postgresql://his-prod-01.hospital.local:5432/his_db"
        )
        end_time = time.time()

        # Should succeed after retries
        assert result["connection_status"] == "established"
        assert result["attempt_number"] == 3
        assert "HIS-CONN-" in result["connection_id"]

        # Should have taken time for retries
        assert end_time - start_time >= 0.3

    def test_exponential_backoff_sync_max_retries_integration(self):
        """Test sync exponential backoff reaching max retries with real failures."""
        attempt_count = 0

        @exponential_backoff_retry(max_retries=2, base_delay=0.1, jitter=False)
        def backup_patient_database(backup_path: str):
            """Real database backup that consistently fails."""
            nonlocal attempt_count
            attempt_count += 1

            # Always fail with disk space error
            raise OSError(f"Insufficient disk space for backup at {backup_path}")

        start_time = time.time()

        with pytest.raises(OSError) as exc_info:
            backup_patient_database("/backup/hospital/patient_db_backup.sql")

        end_time = time.time()

        # Should have made 3 attempts
        assert attempt_count == 3
        assert "Insufficient disk space" in str(exc_info.value)

        # Should have taken time for retries
        assert end_time - start_time >= 0.3

    def test_exponential_backoff_jitter_integration(self):
        """Test exponential backoff jitter with real timing measurements."""
        retry_delays = []

        @exponential_backoff_retry(max_retries=3, base_delay=0.2, jitter=True)
        def unreliable_network_operation():
            """Operation that always fails to test jitter timing."""
            current_time = time.time()
            if retry_delays:
                delay = current_time - retry_delays[-1]
                retry_delays.append(current_time)
            else:
                retry_delays.append(current_time)

            raise ConnectionError("Network unreliable during peak hours")

        start_time = time.time()

        with pytest.raises(ConnectionError):
            unreliable_network_operation()

        # Should have made multiple attempts with jitter
        assert len(retry_delays) >= 3

        # Verify jitter was applied (delays should vary)
        if len(retry_delays) >= 3:
            delay1 = retry_delays[1] - retry_delays[0]
            delay2 = retry_delays[2] - retry_delays[1]

            # Delays should be different due to jitter
            # Allow some tolerance for timing variations
            assert abs(delay1 - delay2) >= 0.01 or delay1 != delay2


class TestUtilityFunctions:
    """Test utility functions with real hospital logging and monitoring."""

    def test_get_logger_integration(self):
        """Test get_logger with real hospital logging scenarios."""
        # Test getting logger for different hospital modules
        ocr_logger = get_logger("hospital.ocr_processor")
        pii_logger = get_logger("hospital.pii_detector")
        audit_logger = get_logger("hospital.audit_system")

        # All should be valid logger instances
        assert ocr_logger.name == "hospital.ocr_processor"
        assert pii_logger.name == "hospital.pii_detector"
        assert audit_logger.name == "hospital.audit_system"

        # Should have standard logging methods
        assert hasattr(ocr_logger, "info")
        assert hasattr(ocr_logger, "error")
        assert hasattr(ocr_logger, "warning")
        assert hasattr(ocr_logger, "debug")

        # Test that loggers work
        ocr_logger.info("OCR processing started for patient HN-2024-001234")
        pii_logger.warning("PII detected: Thai National ID")
        audit_logger.error("Unauthorized access attempt detected")

    def test_get_circuit_breaker_status_integration(self):
        """Test circuit breaker status monitoring with real hospital systems."""
        # Create real hospital circuit breakers
        db_breaker = CircuitBreaker(
            failure_threshold=3, timeout=300, name="hospital_database"
        )

        api_breaker = CircuitBreaker(
            failure_threshold=5, timeout=600, name="gemini_api"
        )

        # Simulate some failures
        db_breaker.failure_count = 2
        db_breaker.last_failure_time = datetime.now()

        api_breaker.failure_count = 5
        api_breaker.last_failure_time = datetime.now()
        api_breaker.is_open = True

        # Get status for monitoring
        circuit_breakers = {"hospital_database": db_breaker, "gemini_api": api_breaker}

        status = get_circuit_breaker_status(circuit_breakers)

        # Verify database breaker status
        assert "hospital_database" in status
        db_status = status["hospital_database"]
        assert db_status["is_open"] is False
        assert db_status["failure_count"] == 2
        assert db_status["last_failure"] is not None

        # Verify API breaker status
        assert "gemini_api" in status
        api_status = status["gemini_api"]
        assert api_status["is_open"] is True
        assert api_status["failure_count"] == 5
        assert api_status["last_failure"] is not None

    def test_get_rate_limiter_status_integration(self):
        """Test rate limiter status monitoring with real hospital APIs."""
        # Create real hospital rate limiters
        gemini_limiter = RateLimiter(max_calls=60, time_window=60, name="gemini_ocr")

        his_limiter = RateLimiter(max_calls=100, time_window=60, name="his_integration")

        # Simulate some API calls
        current_time = time.time()

        # Add some recent calls to Gemini API
        for i in range(45):
            gemini_limiter.calls["gemini_ocr"].append(current_time - i * 0.5)

        # Add some recent calls to HIS API
        for i in range(80):
            his_limiter.calls["his_integration"].append(current_time - i * 0.3)

        # Get status for monitoring
        rate_limiters = {"gemini_ocr": gemini_limiter, "his_integration": his_limiter}

        status = get_rate_limiter_status(rate_limiters)

        # Verify Gemini limiter status
        assert "gemini_ocr" in status
        gemini_status = status["gemini_ocr"]
        assert gemini_status["max_calls"] == 60
        assert gemini_status["time_window"] == 60
        assert gemini_status["current_calls"] <= 45
        assert gemini_status["available"] >= 15

        # Verify HIS limiter status
        assert "his_integration" in status
        his_status = status["his_integration"]
        assert his_status["max_calls"] == 100
        assert his_status["time_window"] == 60
        assert his_status["current_calls"] <= 80
        assert his_status["available"] >= 20


class TestOCRSpecificDecorators:
    """Test OCR-specific decorators with real medical document processing."""

    @pytest.mark.asyncio
    async def test_rate_limiter_ocr_success_integration(self):
        """Test OCR rate limiter with real medical document processing."""

        @rate_limiter_ocr(max_calls=5, time_window=2)
        async def process_medical_document_ocr(document_path: str, patient_hn: str):
            """Real medical document OCR processing."""
            await asyncio.sleep(0.1)  # Real OCR processing time
            return {
                "document_path": document_path,
                "patient_hn": patient_hn,
                "ocr_text": "ผู้ป่วย: นายสมชาย ใจดี\nการวินิจฉัย: โรคเบาหวาน\nแพทย์: นายแพทย์ประสิทธิ์",
                "confidence": 0.94,
                "processing_time": 0.1,
            }

        # Process documents within rate limit
        results = []
        for i in range(4):  # Within 5 call limit
            result = await process_medical_document_ocr(
                f"/hospital/documents/medical_record_{i:04d}.pdf", f"HN-2024-{i:06d}"
            )
            results.append(result)

        assert len(results) == 4
        for result in results:
            assert result["confidence"] == 0.94
            assert "ผู้ป่วย:" in result["ocr_text"]

    @pytest.mark.asyncio
    async def test_rate_limiter_ocr_quota_exceeded_integration(self):
        """Test OCR rate limiter quota exceeded with real scenarios."""

        @rate_limiter_ocr(max_calls=3, time_window=2)
        async def process_emergency_documents(document_path: str):
            """Real emergency document processing with strict limits."""
            await asyncio.sleep(0.01)
            return {"document_path": document_path, "status": "processed"}

        # Process documents up to limit
        for i in range(3):
            result = await process_emergency_documents(f"/emergency/doc_{i}.pdf")
            assert result["status"] == "processed"

        # Next call should raise quota exceeded
        with pytest.raises(OCRQuotaExceededException) as exc_info:
            await process_emergency_documents("/emergency/doc_urgent.pdf")

        assert exc_info.value.quota_type == "rate_limit"
        assert "wait_time" in exc_info.value.context
        assert "current_calls" in exc_info.value.context

    @pytest.mark.asyncio
    async def test_circuit_breaker_ocr_success_integration(self):
        """Test OCR circuit breaker with real successful operations."""

        @circuit_breaker_ocr(failure_threshold=3, timeout=2)
        async def gemini_ocr_api_call(image_data: bytes, patient_hn: str):
            """Real Gemini OCR API call."""
            await asyncio.sleep(0.1)  # Real API call time
            return {
                "patient_hn": patient_hn,
                "extracted_text": "ผลการตรวจเลือด: ปกติ\nความดันโลหิต: 120/80 mmHg",
                "confidence": 0.96,
                "model": "gemini-2.0-flash",
                "processing_time_ms": 100,
            }

        # Test successful operations
        image_data = b"fake_medical_image_data"

        for i in range(5):
            result = await gemini_ocr_api_call(image_data, f"HN-2024-{i:06d}")
            assert result["confidence"] == 0.96
            assert "ผลการตรวจเลือด" in result["extracted_text"]
            assert result["model"] == "gemini-2.0-flash"

    @pytest.mark.asyncio
    async def test_circuit_breaker_ocr_failures_integration(self):
        """Test OCR circuit breaker with real API failures."""
        attempt_count = 0

        @circuit_breaker_ocr(failure_threshold=2, timeout=1)
        async def failing_ocr_api(document_data: bytes):
            """Real OCR API that fails due to service issues."""
            nonlocal attempt_count
            attempt_count += 1

            # Always fail with API exception
            raise OCRAPIException(
                message="Gemini API service temporarily unavailable",
                status_code=503,
                context={"attempt": attempt_count, "service_status": "degraded"},
            )

        document_data = b"medical_document_bytes"

        # First failure
        with pytest.raises(OCRAPIException):
            await failing_ocr_api(document_data)

        # Second failure - should open circuit
        with pytest.raises(OCRAPIException):
            await failing_ocr_api(document_data)

        # Third call should fail immediately due to open circuit
        with pytest.raises(OCRAPIException) as exc_info:
            await failing_ocr_api(document_data)

        assert "circuit breaker is open" in str(exc_info.value)
        assert exc_info.value.status_code == 503
        assert "failure_count" in exc_info.value.context

    @pytest.mark.asyncio
    async def test_circuit_breaker_ocr_recovery_integration(self):
        """Test OCR circuit breaker recovery with real scenarios."""
        attempt_count = 0

        @circuit_breaker_ocr(failure_threshold=2, timeout=1)
        async def recovering_ocr_api(patient_data: Dict[str, Any]):
            """Real OCR API that recovers after initial failures."""
            nonlocal attempt_count
            attempt_count += 1

            # Fail first 2 attempts
            if attempt_count <= 2:
                raise OCRAPIException(
                    message="OCR service overloaded",
                    status_code=503,
                    context={"load": "high", "attempt": attempt_count},
                )

            # Succeed after recovery
            await asyncio.sleep(0.05)
            return {
                "patient_hn": patient_data["patient_hn"],
                "ocr_result": "การตรวจพบปกติ - Normal findings",
                "recovery_attempt": attempt_count,
                "api_status": "recovered",
            }

        patient_data = {"patient_hn": "HN-2024-001234", "document_type": "lab_results"}

        # First two failures
        with pytest.raises(OCRAPIException):
            await recovering_ocr_api(patient_data)

        with pytest.raises(OCRAPIException):
            await recovering_ocr_api(patient_data)

        # Circuit should be open now
        with pytest.raises(OCRAPIException):
            await recovering_ocr_api(patient_data)

        # Wait for timeout and test recovery
        await asyncio.sleep(1.5)

        # Should succeed after recovery
        result = await recovering_ocr_api(patient_data)
        assert result["api_status"] == "recovered"
        assert (
            result["recovery_attempt"] == 4
        )  # 2 failures + 1 circuit open + 1 success


class TestIntegrationScenarios:
    """Test integration scenarios with real hospital system workflows."""

    @pytest.mark.integration
    def test_complete_medical_workflow_integration(self):
        """Test complete medical document workflow with all utilities."""
        # Setup real hospital system components
        db_breaker = CircuitBreaker(
            failure_threshold=3, timeout=60, name="patient_database"
        )

        api_limiter = RateLimiter(max_calls=10, time_window=60, name="medical_api")

        @db_breaker
        @api_limiter
        @exponential_backoff_retry(max_retries=2, base_delay=0.1)
        def process_patient_admission(patient_data: Dict[str, Any]):
            """Real patient admission processing with all protections."""
            # Simulate database and API operations
            time.sleep(0.05)
            return {
                "patient_hn": patient_data["patient_hn"],
                "admission_id": f"ADM-{int(time.time())}",
                "department": patient_data["department"],
                "status": "admitted",
                "timestamp": datetime.now().isoformat(),
            }

        # Test with real patient data
        patient_data = {
            "patient_hn": "HN-2024-001234",
            "patient_name": "นายสมชาย ใจดี",
            "department": "emergency",
            "admission_type": "urgent",
            "symptoms": "chest_pain",
        }

        result = process_patient_admission(patient_data)

        # Verify successful processing
        assert result["status"] == "admitted"
        assert result["patient_hn"] == "HN-2024-001234"
        assert result["department"] == "emergency"
        assert "ADM-" in result["admission_id"]

        # Verify circuit breaker state
        assert db_breaker.failure_count == 0
        assert db_breaker.is_open is False

        # Verify rate limiter state
        assert len(api_limiter.calls["medical_api"]) == 1

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_concurrent_hospital_operations_integration(self):
        """Test concurrent hospital operations with real system load."""
        # Setup hospital system protections
        ocr_breaker = CircuitBreaker(failure_threshold=5, timeout=30, name="ocr_system")

        his_limiter = RateLimiter(max_calls=20, time_window=10, name="his_integration")

        @ocr_breaker
        @his_limiter
        async def process_medical_document_batch(documents: List[Dict[str, Any]]):
            """Real batch medical document processing."""
            results = []
            for doc in documents:
                await asyncio.sleep(0.01)  # Processing time per document
                results.append(
                    {
                        "document_id": doc["id"],
                        "patient_hn": doc["patient_hn"],
                        "processing_status": "completed",
                        "ocr_confidence": 0.92,
                        "pii_detected": doc.get("expected_pii", []),
                    }
                )
            return results

        # Create real hospital document batches
        document_batches = [
            [
                {
                    "id": f"DOC-{i:04d}",
                    "patient_hn": f"HN-2024-{i:06d}",
                    "type": "lab_results",
                    "expected_pii": ["thai_national_id", "patient_name"],
                }
                for i in range(j * 5, (j + 1) * 5)
            ]
            for j in range(4)  # 4 batches of 5 documents each
        ]

        # Process batches concurrently
        tasks = [process_medical_document_batch(batch) for batch in document_batches]

        results = await asyncio.gather(*tasks)

        # Verify all batches processed successfully
        assert len(results) == 4
        total_documents = sum(len(batch_result) for batch_result in results)
        assert total_documents == 20

        # Verify all documents processed
        for batch_result in results:
            for doc_result in batch_result:
                assert doc_result["processing_status"] == "completed"
                assert doc_result["ocr_confidence"] == 0.92
                assert len(doc_result["pii_detected"]) == 2

        # Verify system protections worked
        assert ocr_breaker.failure_count == 0
        assert len(his_limiter.calls["his_integration"]) == 4  # One call per batch


class TestPerformanceScenarios:
    """Test performance scenarios with real hospital workloads."""

    @pytest.mark.performance
    def test_circuit_breaker_performance_integration(self):
        """Test circuit breaker performance under real hospital load."""
        breaker = CircuitBreaker(
            failure_threshold=10, timeout=60, name="performance_test"
        )

        @breaker
        def high_volume_operation(operation_id: int):
            """High volume hospital operation."""
            # Minimal processing to test decorator overhead
            return {"operation_id": operation_id, "status": "completed"}

        # Measure performance with high volume
        start_time = time.time()

        for i in range(10000):  # 10,000 operations
            result = high_volume_operation(i)
            assert result["status"] == "completed"

        end_time = time.time()
        total_time = end_time - start_time
        operations_per_second = 10000 / total_time

        # Should handle at least 1000 operations per second
        assert (
            operations_per_second >= 1000
        ), f"Performance too slow: {operations_per_second:.0f} ops/sec"
        print(
            f"Circuit breaker performance: {operations_per_second:.0f} operations/second"
        )

    @pytest.mark.performance
    def test_rate_limiter_performance_integration(self):
        """Test rate limiter performance under real API load."""
        limiter = RateLimiter(
            max_calls=10000,  # High limit for performance testing
            time_window=60,
            name="performance_test",
        )

        @limiter
        def api_operation(request_id: int):
            """High volume API operation."""
            return {"request_id": request_id, "response": "success"}

        # Measure performance
        start_time = time.time()

        for i in range(5000):  # 5,000 API calls
            result = api_operation(i)
            assert result["response"] == "success"

        end_time = time.time()
        total_time = end_time - start_time
        requests_per_second = 5000 / total_time

        # Should handle at least 500 requests per second
        assert (
            requests_per_second >= 500
        ), f"Performance too slow: {requests_per_second:.0f} req/sec"
        print(f"Rate limiter performance: {requests_per_second:.0f} requests/second")

    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_exponential_backoff_performance_integration(self):
        """Test exponential backoff performance with real scenarios."""
        success_count = 0

        @exponential_backoff_retry(
            max_retries=0, base_delay=0.001
        )  # No retries for performance test
        async def fast_operation(operation_id: int):
            """Fast operation for performance testing."""
            nonlocal success_count
            success_count += 1
            return {"operation_id": operation_id, "status": "fast_completed"}

        # Measure performance
        start_time = time.time()

        tasks = [fast_operation(i) for i in range(1000)]  # 1,000 async operations
        results = await asyncio.gather(*tasks)

        end_time = time.time()
        total_time = end_time - start_time
        operations_per_second = 1000 / total_time

        # Verify all operations completed
        assert len(results) == 1000
        assert success_count == 1000

        # Should handle at least 100 async operations per second
        assert (
            operations_per_second >= 100
        ), f"Async performance too slow: {operations_per_second:.0f} ops/sec"
        print(
            f"Exponential backoff async performance: {operations_per_second:.0f} operations/second"
        )


class TestSecurityScenarios:
    """Test security scenarios with real hospital security requirements."""

    def test_circuit_breaker_security_isolation_integration(self):
        """Test circuit breaker security isolation between hospital departments."""
        # Create separate circuit breakers for different departments
        cardiology_breaker = CircuitBreaker(
            failure_threshold=3, timeout=60, name="cardiology_system"
        )

        oncology_breaker = CircuitBreaker(
            failure_threshold=3, timeout=60, name="oncology_system"
        )

        @cardiology_breaker
        def access_cardiac_records(patient_hn: str):
            """Cardiology department record access."""
            # Simulate failure
            raise OCRProcessingException(
                message=f"Cardiac database unavailable for {patient_hn}",
                error_code="CARDIAC_DB_UNAVAILABLE",
                retry_able=True,
            )

        @oncology_breaker
        def access_oncology_records(patient_hn: str):
            """Oncology department record access."""
            return {
                "patient_hn": patient_hn,
                "department": "oncology",
                "access_status": "granted",
                "records_available": True,
            }

        # Fail cardiology system
        for i in range(3):
            with pytest.raises(OCRProcessingException):
                access_cardiac_records(f"HN-2024-{i:06d}")

        # Cardiology circuit should be open
        assert cardiology_breaker.is_open is True

        # Oncology system should still work (isolation)
        result = access_oncology_records("HN-2024-001234")
        assert result["access_status"] == "granted"
        assert oncology_breaker.is_open is False

        # Verify departments are isolated
        assert cardiology_breaker.failure_count == 3
        assert oncology_breaker.failure_count == 0

    def test_rate_limiter_security_dos_protection_integration(self):
        """Test rate limiter DoS protection for hospital APIs."""
        # Strict rate limiter for security-critical operations
        security_limiter = RateLimiter(
            max_calls=5, time_window=10, name="patient_pii_access"  # Very restrictive
        )

        @security_limiter
        def access_patient_pii(patient_hn: str, requester_id: str):
            """Security-critical PII access."""
            return {
                "patient_hn": patient_hn,
                "requester_id": requester_id,
                "pii_access_granted": True,
                "security_level": "high",
                "audit_logged": True,
            }

        # Normal usage should work
        for i in range(5):
            result = access_patient_pii(f"HN-2024-{i:06d}", "DR001")
            assert result["pii_access_granted"] is True

        # Additional requests should be rate limited (DoS protection)
        start_time = time.time()

        # This should trigger rate limiting delay
        result = access_patient_pii("HN-2024-999999", "SUSPICIOUS_USER")

        end_time = time.time()
        delay = end_time - start_time

        # Should have experienced rate limiting delay
        assert delay >= 5.0  # At least 5 seconds delay
        assert result["pii_access_granted"] is True

    def test_exponential_backoff_security_timing_integration(self):
        """Test exponential backoff doesn't leak timing information."""
        retry_times = []

        @exponential_backoff_retry(max_retries=3, base_delay=0.1, jitter=True)
        def authenticate_user(username: str, password: str):
            """User authentication with timing attack protection."""
            retry_times.append(time.time())

            # Always fail to test retry timing
            raise ValueError(f"Authentication failed for user: {username}")

        start_time = time.time()

        with pytest.raises(ValueError):
            authenticate_user("admin", "wrong_password")

        end_time = time.time()
        total_time = end_time - start_time

        # Should have made 4 attempts (initial + 3 retries)
        assert len(retry_times) == 4

        # Total time should be reasonable (not too predictable due to jitter)
        assert 0.5 <= total_time <= 2.0

        # Verify jitter makes timing unpredictable
        if len(retry_times) >= 3:
            interval1 = retry_times[1] - retry_times[0]
            interval2 = retry_times[2] - retry_times[1]

            # Intervals should be different due to jitter (timing attack protection)
            assert abs(interval1 - interval2) >= 0.01

    def test_ocr_decorators_audit_logging_integration(self):
        """Test OCR decorators generate proper audit logs for security."""
        audit_events = []

        def mock_audit_log(event: str, context: Dict[str, Any]):
            """Mock audit logging for security testing."""
            audit_events.append(
                {"event": event, "context": context, "timestamp": time.time()}
            )

        @rate_limiter_ocr(max_calls=2, time_window=1)
        async def secure_ocr_operation(document_path: str, user_id: str):
            """Secure OCR operation with audit logging."""
            mock_audit_log(
                "ocr_access",
                {
                    "document_path": document_path,
                    "user_id": user_id,
                    "operation": "medical_document_processing",
                },
            )

            return {
                "document_path": document_path,
                "user_id": user_id,
                "processing_status": "completed",
            }

        # Successful operations should be audited
        asyncio.run(secure_ocr_operation("/secure/patient_record.pdf", "DR001"))
        asyncio.run(secure_ocr_operation("/secure/lab_results.pdf", "DR001"))

        # Rate limit exceeded should also be audited
        try:
            asyncio.run(secure_ocr_operation("/secure/emergency_report.pdf", "DR001"))
        except OCRQuotaExceededException:
            mock_audit_log(
                "rate_limit_exceeded",
                {
                    "user_id": "DR001",
                    "operation": "ocr_processing",
                    "security_event": "potential_abuse",
                },
            )

        # Verify security audit trail
        assert len(audit_events) >= 2  # At least 2 successful operations

        # Check audit log content
        for event in audit_events:
            assert "timestamp" in event
            assert "context" in event
            if event["event"] == "ocr_access":
                assert "user_id" in event["context"]
                assert "document_path" in event["context"]


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
