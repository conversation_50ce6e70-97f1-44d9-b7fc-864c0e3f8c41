#!/usr/bin/env python3
"""
Test script for the new generic medical record schema.
This script validates the refactored OCRResult class and its methods.
"""

import json
import sys
from pathlib import Path

# Add parent directory to path to import src modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.models import OCRResult


def test_generic_schema_creation():
    """Test creating OCRResult with new generic schema."""
    print("=== Testing Generic Schema Creation ===")

    # Test complete extraction
    result = OCRResult(
        full_text="Complete medical record text...",
        # Core 13-field schema
        patient_code="TT04035",
        sample_code="A1B2C3",
        investigation="K-TRACK",
        patient_name_th="นายทดสอบ ระบบ",
        patient_name_en="Mr. Test System",
        dob_gregorian="1990-05-15",
        dob_buddhist_era="15/05/2533",
        patient_contact_no="************",
        place_of_treatment="โรงพยาบาลทดสอบ",
        referring_physician_th="นพ.หมอ ทดสอบ",
        referring_physician_en="Dr. Test Doctor",
        referring_physician_md_code="MD12345",
        referring_physician_email=["<EMAIL>", "<EMAIL>"],
        # Additional medical data
        diagnoses=["Hypertension", "Diabetes"],
        medications=["Metformin", "Lisinopril"],
        test_results={"HbA1c": "7.2%", "BP": "140/90"},
        # Processing metadata
        confidence_score=0.85,
        processing_time=2.5,
        page_count=1,
        detected_languages=["thai", "english"],
        warnings=["Some handwritten text unclear"],
    )

    print(f"✅ Created OCRResult with patient code: {result.patient_code}")
    print(f"✅ Investigation type: {result.investigation}")
    print(f"✅ Patient name (TH): {result.patient_name_th}")
    print(f"✅ Patient name (EN): {result.patient_name_en}")
    print(f"✅ Place of treatment: {result.place_of_treatment}")
    print(f"✅ Referring physician emails: {result.referring_physician_email}")

    return result


def test_extraction_completeness(result: OCRResult):
    """Test the extraction completeness calculation."""
    print("\n=== Testing Extraction Completeness ===")

    completeness = result.get_extraction_completeness()

    print(f"✅ Extracted fields: {completeness['extracted_fields']}/13")
    print(f"✅ Completeness percentage: {completeness['completeness_percentage']}%")
    print("✅ Field status:")
    for field, status in completeness["field_status"].items():
        status_icon = "✅" if status else "❌"
        print(f"   {status_icon} {field}: {status}")

    return completeness


def test_pii_detection(result: OCRResult):
    """Test PII detection with new schema."""
    print("\n=== Testing PII Detection ===")

    has_pii = result.has_pii()
    pii_summary = result.get_pii_summary()

    print(f"✅ Has PII: {has_pii}")
    print("✅ PII Summary:")
    for pii_type, found in pii_summary.items():
        status_icon = "⚠️" if found else "✅"
        print(f"   {status_icon} {pii_type}: {found}")


def test_partial_extraction():
    """Test partial extraction with only some fields."""
    print("\n=== Testing Partial Extraction ===")

    partial_result = OCRResult(
        full_text="Partial document text...",
        patient_code="TT04040",
        investigation="SPOT-MAS",
        patient_name_th="นางทดสอบ บางส่วน",
        place_of_treatment="โรงพยาบาลอื่น",
        # Missing many fields intentionally
        confidence_score=0.65,
        processing_time=1.8,
        page_count=1,
    )

    completeness = partial_result.get_extraction_completeness()
    print(
        f"✅ Partial extraction - {completeness['extracted_fields']}/13 fields ({completeness['completeness_percentage']}%)"
    )

    # Test fields that should be present
    assert partial_result.patient_code == "TT04040"
    assert partial_result.investigation == "SPOT-MAS"
    assert partial_result.patient_name_th == "นางทดสอบ บางส่วน"

    # Test fields that should be None
    assert partial_result.sample_code is None
    assert partial_result.patient_name_en is None
    assert partial_result.dob_gregorian is None
    assert partial_result.referring_physician_email == []

    print("✅ All partial extraction assertions passed")
    return partial_result


def test_validation():
    """Test field validation."""
    print("\n=== Testing Field Validation ===")

    # Test patient code validation
    result1 = OCRResult(
        full_text="Test",
        patient_code="TT12345",  # Valid - contains TT
        confidence_score=0.8,
        processing_time=1.0,
        page_count=1,
    )
    print(f"✅ Valid patient code: {result1.patient_code}")

    # Test sample code validation
    result2 = OCRResult(
        full_text="Test",
        sample_code="ABC123",  # Valid - 6 alphanumeric
        confidence_score=0.8,
        processing_time=1.0,
        page_count=1,
    )
    print(f"✅ Valid sample code: {result2.sample_code}")

    # Test email validation
    result3 = OCRResult(
        full_text="Test",
        referring_physician_email=["<EMAIL>", "<EMAIL>"],
        confidence_score=0.8,
        processing_time=1.0,
        page_count=1,
    )
    print(f"✅ Valid emails: {result3.referring_physician_email}")


def test_backward_compatibility():
    """Test that legacy fields still work."""
    print("\n=== Testing Backward Compatibility ===")

    legacy_result = OCRResult(
        full_text="Legacy document",
        thai_id="1234567890123",  # Legacy field
        patient_name_th="นายเก่า ระบบ",  # Maps to new schema
        confidence_score=0.7,
        processing_time=2.0,
        page_count=1,
    )

    print(f"✅ Legacy Thai ID: {legacy_result.thai_id}")
    print(f"✅ Patient name (TH): {legacy_result.patient_name_th}")

    # Test PII detection includes legacy fields
    has_pii = legacy_result.has_pii()
    pii_summary = legacy_result.get_pii_summary()

    print(f"✅ Has PII (including legacy): {has_pii}")
    print(f"✅ Thai ID in PII summary: {pii_summary['thai_id']}")


def main():
    """Run all tests."""
    print("🧪 Testing ChromoForge Generic Medical Record Schema")
    print("=" * 60)

    try:
        # Test 1: Complete extraction
        complete_result = test_generic_schema_creation()

        # Test 2: Extraction completeness
        completeness = test_extraction_completeness(complete_result)

        # Test 3: PII detection
        test_pii_detection(complete_result)

        # Test 4: Partial extraction
        partial_result = test_partial_extraction()

        # Test 5: Field validation
        test_validation()

        # Test 6: Backward compatibility
        test_backward_compatibility()

        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED!")
        print(
            f"✅ Generic schema supports {len(complete_result.get_extraction_completeness()['field_status'])} core fields"
        )
        print("✅ Backward compatibility maintained")
        print("✅ PII detection working")
        print("✅ Field validation working")
        print("✅ Partial extraction handling working")

        # Summary
        print(f"\n📊 Schema Summary:")
        print(
            f"   • Complete extraction: {completeness['completeness_percentage']}% completeness"
        )
        print(f"   • Supports multiple file patterns and formats")
        print(f"   • All fields nullable for partial extractions")
        print(f"   • Legacy field support maintained")

    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        import traceback

        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
