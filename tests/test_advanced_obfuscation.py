"""Comprehensive tests for medical obfuscation pipeline.

This module tests the critical obfuscation functionality to ensure that
PII is actually covered by rectangles and not placed randomly.
"""

import asyncio
import json
import tempfile
from pathlib import Path
from typing import List, Tuple

import pytest

from src.processing.pdf_obfuscator import (ObfuscationMethod, PDFObfuscator,
                                           TextCoordinate)
from src.processing.pii_detector import PIIDetector, PIIMatch, PIIType


class TestEnhancedObfuscation:
    """Test suite for medical obfuscation pipeline."""

    @pytest.fixture
    def pdf_obfuscator(self):
        """Create PDF obfuscator instance."""
        return PDFObfuscator(method=ObfuscationMethod.BLACK_BOX)

    @pytest.fixture
    def pii_detector(self):
        """Create PII detector instance."""
        return PIIDetector()

    @pytest.fixture
    def sample_pii_matches(self):
        """Create sample PII matches for testing."""
        return [
            PIIMatch(
                pii_type=PIIType.ADDRESS,
                text="90 ถนนศรีธรรมไตรปิฎก ตำบลในเมือง อำเภอเมือง จังหวัดพิษณุโลก 65000",
                confidence=1.0,
                line_number=332,
                context="GENE\nSOLUTIONS\n1\n(blood)\nSENT COD\nΗΝ 66014402\nชื่อ นายสุชาติ ลิ้มวัฒนา",
            ),
            PIIMatch(
                pii_type=PIIType.ENGLISH_NAME,
                text="Gene Solutions",
                confidence=0.7,
                line_number=91,
                context="OD\nΗΝ 66014402\nชื่อ นายสุชาติ ลิ้มวัฒนา\nID 3-6599-00221-11-6",
            ),
            PIIMatch(
                pii_type=PIIType.ENGLISH_NAME,
                text="Gene Solutions Lab",
                confidence=0.7,
                line_number=80,
                context="นายสุชาติ ลิ้มวัฒนา\nID 3-6599-00221-11-6\nวัน/เดือน/ปีเกิด 8/6/2501",
            ),
        ]

    @pytest.fixture
    def sample_text_coordinates(self):
        """Create sample text coordinates that should match PII."""
        return [
            TextCoordinate(
                x1=100.0,
                y1=200.0,
                x2=300.0,
                y2=220.0,
                page_num=0,
                text="Gene Solutions",
                font_size=12.0,
            ),
            TextCoordinate(
                x1=100.0,
                y1=180.0,
                x2=350.0,
                y2=200.0,
                page_num=0,
                text="Gene Solutions Lab",
                font_size=12.0,
            ),
            TextCoordinate(
                x1=50.0,
                y1=100.0,
                x2=400.0,
                y2=120.0,
                page_num=0,
                text="90 ถนนศรีธรรมไตรปิฎก ตำบลในเมือง",
                font_size=10.0,
            ),
            TextCoordinate(
                x1=50.0,
                y1=80.0,
                x2=300.0,
                y2=100.0,
                page_num=0,
                text="อำเภอเมือง จังหวัดพิษณุโลก 65000",
                font_size=10.0,
            ),
            # Some non-PII text that should not be obfuscated
            TextCoordinate(
                x1=100.0,
                y1=300.0,
                x2=200.0,
                y2=320.0,
                page_num=0,
                text="Normal text",
                font_size=12.0,
            ),
            TextCoordinate(
                x1=100.0,
                y1=280.0,
                x2=250.0,
                y2=300.0,
                page_num=0,
                text="Test results",
                font_size=12.0,
            ),
        ]

    def test_exact_matching(
        self, pdf_obfuscator, sample_pii_matches, sample_text_coordinates
    ):
        """Test exact text matching between PII and coordinates."""
        pii_match = sample_pii_matches[1]  # "Gene Solutions"

        matches = pdf_obfuscator.coordinate_mapper._find_exact_matches(
            pii_match.text, sample_text_coordinates
        )

        assert len(matches) >= 1, f"Should find exact match for '{pii_match.text}'"
        assert any(
            coord.text == "Gene Solutions" for coord in matches
        ), "Should match exact text"

        # Verify coordinates are reasonable
        for match in matches:
            assert match.x1 < match.x2, "x1 should be less than x2"
            assert match.y1 < match.y2, "y1 should be less than y2"
            assert match.x1 >= 0 and match.y1 >= 0, "Coordinates should be positive"

    def test_fuzzy_matching(
        self, pdf_obfuscator, sample_pii_matches, sample_text_coordinates
    ):
        """Test fuzzy text matching for partial matches."""
        pii_match = sample_pii_matches[2]  # "Gene Solutions Lab"

        matches = pdf_obfuscator.coordinate_mapper._find_fuzzy_matches(
            pii_match.text, sample_text_coordinates
        )

        assert len(matches) >= 1, f"Should find fuzzy match for '{pii_match.text}'"

        # Check that we found the right coordinate
        found_exact = any(coord.text == "Gene Solutions Lab" for coord in matches)
        found_partial = any("Gene Solutions" in coord.text for coord in matches)

        assert found_exact or found_partial, "Should find exact or partial match"

    def test_address_matching(
        self, pdf_obfuscator, sample_pii_matches, sample_text_coordinates
    ):
        """Test matching for Thai address text."""
        pii_match = sample_pii_matches[0]  # Thai address

        # Try exact matching first
        exact_matches = pdf_obfuscator.coordinate_mapper._find_exact_matches(
            pii_match.text, sample_text_coordinates
        )

        # Try fuzzy matching
        fuzzy_matches = pdf_obfuscator.coordinate_mapper._find_fuzzy_matches(
            pii_match.text, sample_text_coordinates
        )

        # Try word-by-word matching
        words = pii_match.text.split()
        word_matches = pdf_obfuscator.coordinate_mapper._find_word_matches(
            words, sample_text_coordinates
        )

        total_matches = len(exact_matches) + len(fuzzy_matches) + len(word_matches)
        assert (
            total_matches >= 1
        ), f"Should find at least one match for address: {pii_match.text}"

    def test_coordinate_mapping_integration(
        self, pdf_obfuscator, sample_pii_matches, sample_text_coordinates
    ):
        """Test the complete coordinate mapping process."""
        full_text = " ".join([coord.text for coord in sample_text_coordinates])

        pii_coordinate_mapping = (
            pdf_obfuscator.coordinate_mapper.map_pii_to_coordinates(
                sample_pii_matches, sample_text_coordinates, full_text
            )
        )

        assert len(pii_coordinate_mapping) == len(
            sample_pii_matches
        ), "Should map all PII matches"

        # Check that each PII match has coordinates
        for pii_match, coordinates in pii_coordinate_mapping:
            assert (
                len(coordinates) >= 0
            ), f"Should have coordinates for PII: {pii_match.text}"

            # If coordinates found, verify they're valid
            for coord in coordinates:
                assert isinstance(
                    coord, TextCoordinate
                ), "Should return TextCoordinate objects"
                assert coord.x1 < coord.x2, "Invalid coordinate: x1 >= x2"
                assert coord.y1 < coord.y2, "Invalid coordinate: y1 >= y2"

    def test_coordinate_conversion_accuracy(self, pdf_obfuscator):
        """Test that image-based OCR coordinates are converted correctly."""
        # Test coordinate conversion from image space to PDF space
        # This tests the ImageBasedTextExtractor coordinate conversion

        # Simulate image dimensions and OCR results
        image_height = 1000  # pixels
        dpi = 300
        scale_factor = 72.0 / dpi  # Convert to PDF points

        # Test coordinate conversion
        x, y, w, h = 100, 200, 150, 20  # Image coordinates

        # Convert to PDF coordinates (as done in ImageBasedTextExtractor)
        x1 = x * scale_factor
        y1 = (image_height - y - h) * scale_factor  # Flip Y coordinate
        x2 = (x + w) * scale_factor
        y2 = (image_height - y) * scale_factor

        # Verify conversion
        assert x1 < x2, "Converted x1 should be less than x2"
        assert y1 < y2, "Converted y1 should be less than y2"
        assert x1 >= 0 and y1 >= 0, "Converted coordinates should be positive"

        # Verify scale factor is reasonable
        assert (
            0.2 < scale_factor < 0.3
        ), f"Scale factor should be ~0.24, got {scale_factor}"

    @pytest.mark.asyncio
    async def test_integration_pdf_obfuscation(self, pdf_obfuscator):
        """Test obfuscation with a real PDF file."""
        # Use the test PDF that we know works
        test_pdf = Path("original-pdf-examples/CSF TT04049_Pathology.pdf")

        if not test_pdf.exists():
            pytest.skip("Test PDF not available")

        # Create temporary output file
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
            output_path = Path(tmp_file.name)

        try:
            # Create sample PII matches (based on known content)
            pii_matches = [
                PIIMatch(
                    pii_type=PIIType.ENGLISH_NAME,
                    text="Gene Solutions",
                    confidence=0.7,
                    line_number=91,
                    context="Test context",
                )
            ]

            # Test the complete obfuscation pipeline
            result = await pdf_obfuscator.obfuscate_pdf(
                test_pdf, output_path, pii_matches
            )

            # Verify result
            assert result["success"] is True, "Obfuscation should succeed"
            assert result["pii_items_found"] >= 1, "Should find PII items"
            assert output_path.exists(), "Output PDF should be created"
            assert output_path.stat().st_size > 0, "Output PDF should not be empty"

            # Verify that some obfuscation was applied
            if result["pii_items_obfuscated"] > 0:
                print(
                    f"✅ Successfully obfuscated {result['pii_items_obfuscated']} PII items"
                )
            else:
                print(
                    "⚠️  No PII items were obfuscated - this indicates a coordinate mapping issue"
                )

        finally:
            # Clean up
            if output_path.exists():
                output_path.unlink()

    def test_obfuscation_rectangle_placement(
        self, pdf_obfuscator, sample_text_coordinates
    ):
        """Test that obfuscation rectangles are placed correctly."""
        # Create a mapping with known coordinates
        pii_match = PIIMatch(
            pii_type=PIIType.ENGLISH_NAME,
            text="Gene Solutions",
            confidence=0.7,
            line_number=91,
            context="Test",
        )

        # Find the matching coordinate
        matching_coord = None
        for coord in sample_text_coordinates:
            if coord.text == "Gene Solutions":
                matching_coord = coord
                break

        assert matching_coord is not None, "Should find matching coordinate"

        # Test rectangle calculation
        padding = pdf_obfuscator.padding
        expected_rect = {
            "x1": matching_coord.x1 - padding,
            "y1": matching_coord.y1 - padding,
            "x2": matching_coord.x2 + padding,
            "y2": matching_coord.y2 + padding,
        }

        # Verify rectangle dimensions
        width = expected_rect["x2"] - expected_rect["x1"]
        height = expected_rect["y2"] - expected_rect["y1"]

        assert width > 0, "Rectangle width should be positive"
        assert height > 0, "Rectangle height should be positive"
        assert (
            width > matching_coord.width
        ), "Rectangle should be wider than text (due to padding)"
        assert (
            height > matching_coord.height
        ), "Rectangle should be taller than text (due to padding)"

    def test_no_false_positive_obfuscation(
        self, pdf_obfuscator, sample_text_coordinates
    ):
        """Test that non-PII text is not obfuscated."""
        # Create PII matches that should NOT match normal text
        pii_matches = [
            PIIMatch(
                pii_type=PIIType.ENGLISH_NAME,
                text="Nonexistent Name",
                confidence=0.7,
                line_number=1,
                context="Test",
            )
        ]

        full_text = " ".join([coord.text for coord in sample_text_coordinates])

        pii_coordinate_mapping = (
            pdf_obfuscator.coordinate_mapper.map_pii_to_coordinates(
                pii_matches, sample_text_coordinates, full_text
            )
        )

        # Should not find matches for non-existent PII
        for pii_match, coordinates in pii_coordinate_mapping:
            if pii_match.text == "Nonexistent Name":
                assert (
                    len(coordinates) == 0
                ), "Should not find coordinates for non-existent PII"


def test_coordinate_mapping_accuracy():
    """Test coordinate mapping accuracy with real-world scenarios."""
    print("\n=== Testing Coordinate Mapping Accuracy ===")

    # Test various PII types and their matching
    test_cases = [
        {
            "pii_text": "Gene Solutions",
            "coordinate_text": "Gene Solutions",
            "should_match": True,
            "match_type": "exact",
        },
        {
            "pii_text": "Gene Solutions Lab",
            "coordinate_text": "Gene Solutions",
            "should_match": True,
            "match_type": "partial",
        },
        {
            "pii_text": "นายสุชาติ ลิ้มวัฒนา",
            "coordinate_text": "สุชาติ ลิ้มวัฒนา",
            "should_match": True,
            "match_type": "fuzzy",
        },
        {
            "pii_text": "082-4040359",
            "coordinate_text": "082-4040359",
            "should_match": True,
            "match_type": "exact",
        },
        {
            "pii_text": "Random Text",
            "coordinate_text": "Gene Solutions",
            "should_match": False,
            "match_type": "none",
        },
    ]

    obfuscator = PDFObfuscator()

    for i, test_case in enumerate(test_cases):
        print(f"\nTest {i+1}: {test_case['match_type']} matching")
        print(f"PII: '{test_case['pii_text']}'")
        print(f"Coordinate: '{test_case['coordinate_text']}'")

        # Create test coordinate
        coord = TextCoordinate(
            x1=100.0,
            y1=200.0,
            x2=300.0,
            y2=220.0,
            page_num=0,
            text=test_case["coordinate_text"],
            font_size=12.0,
        )

        # Test exact matching
        exact_matches = obfuscator.coordinate_mapper._find_exact_matches(
            test_case["pii_text"], [coord]
        )

        # Test fuzzy matching
        fuzzy_matches = obfuscator.coordinate_mapper._find_fuzzy_matches(
            test_case["pii_text"], [coord]
        )

        total_matches = len(exact_matches) + len(fuzzy_matches)

        if test_case["should_match"]:
            assert total_matches > 0, f"Should find match for test case {i+1}"
            print(f"✅ Found {total_matches} matches as expected")
        else:
            assert total_matches == 0, f"Should not find match for test case {i+1}"
            print(f"✅ No matches found as expected")

    print("\n✅ All coordinate mapping tests passed!")


if __name__ == "__main__":
    test_coordinate_mapping_accuracy()
