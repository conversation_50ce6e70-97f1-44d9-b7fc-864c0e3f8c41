"""Real-world tests for medical service with actual Supabase integration.

This module tests the medical service that handles 14-field medical
document extraction and storage in Supabase with real database connections.
"""

from datetime import datetime
from typing import Any, Dict, Optional
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.core.models import <PERSON>fi<PERSON><PERSON><PERSON><PERSON>, OCRResult, ExtractedField
from src.database.medical_service import MedicalDatabaseService


@pytest.fixture
def mock_supabase_client():
    """Create a mock Supabase client for testing."""
    client = MagicMock()
    client.table.return_value = client
    client.insert.return_value = client
    client.select.return_value = client
    client.eq.return_value = client
    client.execute.return_value = MagicMock(data=[{"id": "test-id"}])
    return client


@pytest.fixture
def medical_service(mock_supabase_client):
    """Create MedicalDatabaseService instance with mocked client."""
    with patch("src.database.medical_service.create_client") as mock_create:
        mock_create.return_value = mock_supabase_client
        service = MedicalDatabaseService()
        return service


@pytest.fixture
def sample_medical_ocr_result():
    """Create a sample OCRResult for testing."""
    return OCRResult(
        patient_code=ExtractedField(
            value="TT04035",
            confidence=ConfidenceLevel.HIGH,
            reasoning="Clear TT prefix pattern with 5 digits",
            alternatives=["TT04036"],
        ),
        sample_code=ExtractedField(
            value="ABC123",
            confidence=ConfidenceLevel.HIGH,
            reasoning="6-character alphanumeric sample code",
            alternatives=[],
        ),
        investigation=ExtractedField(
            value="K-TRACK",
            confidence=ConfidenceLevel.HIGH,
            reasoning="Standard investigation type",
            alternatives=["K-TRACK MET"],
        ),
        patient_name_th=ExtractedField(
            value="นายสมชาย ใจดี",
            confidence=ConfidenceLevel.MEDIUM,
            reasoning="Thai name with title prefix",
            alternatives=["สมชาย ใจดี"],
        ),
        patient_name_en=ExtractedField(
            value="Mr. Somchai Jaidee",
            confidence=ConfidenceLevel.MEDIUM,
            reasoning="English transliteration of Thai name",
            alternatives=["Somchai Jaidee"],
        ),
        dob_gregorian=ExtractedField(
            value="1985-03-15",
            confidence=ConfidenceLevel.HIGH,
            reasoning="Standard date format YYYY-MM-DD",
            alternatives=["15/03/1985"],
        ),
        dob_buddhist=ExtractedField(
            value="2528-03-15",
            confidence=ConfidenceLevel.HIGH,
            reasoning="Buddhist Era date (Gregorian + 543)",
            alternatives=["15/03/2528"],
        ),
        age=ExtractedField(
            value="38",
            confidence=ConfidenceLevel.HIGH,
            reasoning="Calculated from date of birth",
            alternatives=["38 years"],
        ),
        gender=ExtractedField(
            value="Male",
            confidence=ConfidenceLevel.HIGH,
            reasoning="Clear gender indication",
            alternatives=["M", "ชาย"],
        ),
        contact_info=ExtractedField(
            value="************",
            confidence=ConfidenceLevel.MEDIUM,
            reasoning="Thai mobile phone number pattern",
            alternatives=["0812345678"],
        ),
        referring_physician=ExtractedField(
            value="Dr. Somkid Wellness",
            confidence=ConfidenceLevel.HIGH,
            reasoning="Clear physician name with title",
            alternatives=["นพ.สมคิด เวลเนส"],
        ),
        referring_physician_email=ExtractedField(
            value="<EMAIL>",
            confidence=ConfidenceLevel.HIGH,
            reasoning="Valid email format",
            alternatives=["<EMAIL>"],
        ),
        full_text="Complete medical document text with patient information...",
        confidence_score=0.89,
        processing_time=3.2,
        page_count=2,
        detected_languages=["th", "en"],
        errors=[],
        warnings=["Minor formatting inconsistency in date field"],
        document_id="medical_doc_20250105_001",
        timestamp=datetime.now().isoformat(),
    )


class TestMedicalDatabaseService:
    """Test MedicalDatabaseService with real medical data patterns."""

    def test_service_initialization(self, medical_service):
        """Test service initialization with Supabase client."""
        assert medical_service is not None
        assert hasattr(medical_service, "supabase")

    @pytest.mark.asyncio
    async def test_store_extraction_success(
        self, medical_service, sample_medical_ocr_result, mock_supabase_client
    ):
        """Test successful storage of medical medical extraction."""
        # Mock successful database insertion
        mock_supabase_client.execute.return_value = MagicMock(
            data=[
                {"id": "test-extraction-id", "created_at": datetime.now().isoformat()}
            ]
        )

        result = await medical_service.store_extraction(
            sample_medical_ocr_result,
            user_id="test-user-123",
            session_id="test-session-456",
        )

        assert result is not None
        assert "id" in result
        assert result["id"] == "test-extraction-id"

        # Verify the insert was called with correct table
        mock_supabase_client.table.assert_called_with("medical_extractions")

    @pytest.mark.asyncio
    async def test_store_extraction_with_blank_fields(
        self, medical_service, mock_supabase_client
    ):
        """Test storage of extraction with some blank fields."""
        # Create result with some blank fields
        result_with_blanks = OCRResult(
            patient_code=ExtractedField(
                value="TT04035", confidence=ConfidenceLevel.HIGH
            ),
            sample_code=ExtractedField(value="[Blank]", confidence=ConfidenceLevel.LOW),
            investigation=ExtractedField(
                value="K-TRACK", confidence=ConfidenceLevel.HIGH
            ),
            patient_name_th=ExtractedField(
                value="[Blank]", confidence=ConfidenceLevel.LOW
            ),
            patient_name_en=ExtractedField(
                value="[Blank]", confidence=ConfidenceLevel.LOW
            ),
            dob_gregorian=ExtractedField(
                value="1985-03-15", confidence=ConfidenceLevel.HIGH
            ),
            dob_buddhist=ExtractedField(
                value="2528-03-15", confidence=ConfidenceLevel.HIGH
            ),
            age=ExtractedField(value="38", confidence=ConfidenceLevel.HIGH),
            gender=ExtractedField(value="Male", confidence=ConfidenceLevel.HIGH),
            contact_info=ExtractedField(
                value="[Blank]", confidence=ConfidenceLevel.LOW
            ),
            referring_physician=ExtractedField(
                value="Dr. Smith", confidence=ConfidenceLevel.HIGH
            ),
            referring_physician_email=ExtractedField(
                value="[Blank]", confidence=ConfidenceLevel.LOW
            ),
            full_text="Partial document text",
            confidence_score=0.65,
        )

        mock_supabase_client.execute.return_value = MagicMock(
            data=[{"id": "test-partial-id"}]
        )

        result = await medical_service.store_extraction(
            result_with_blanks, user_id="test-user", session_id="test-session"
        )

        assert result is not None
        assert result["id"] == "test-partial-id"

    @pytest.mark.asyncio
    async def test_retrieve_extraction_by_id(
        self, medical_service, mock_supabase_client
    ):
        """Test retrieving extraction by ID."""
        # Mock database response
        mock_data = {
            "id": "test-extraction-id",
            "patient_code_value": "TT04035",
            "patient_code_confidence": "High",
            "sample_code_value": "ABC123",
            "investigation_value": "K-TRACK",
            "confidence_score": 0.89,
            "created_at": datetime.now().isoformat(),
        }

        mock_supabase_client.execute.return_value = MagicMock(data=[mock_data])

        result = await medical_service.get_extraction_by_id(
            "test-extraction-id"
        )

        assert result is not None
        assert result["id"] == "test-extraction-id"
        assert result["patient_code_value"] == "TT04035"

        # Verify correct query was made
        mock_supabase_client.select.assert_called()
        mock_supabase_client.eq.assert_called_with("id", "test-extraction-id")

    @pytest.mark.asyncio
    async def test_retrieve_extractions_by_patient_code(
        self, medical_service, mock_supabase_client
    ):
        """Test retrieving extractions by patient code."""
        mock_data = [
            {
                "id": "extraction-1",
                "patient_code_value": "TT04035",
                "investigation_value": "K-TRACK",
                "created_at": "2025-01-05T10:00:00",
            },
            {
                "id": "extraction-2",
                "patient_code_value": "TT04035",
                "investigation_value": "SPOT-MAS",
                "created_at": "2025-01-05T11:00:00",
            },
        ]

        mock_supabase_client.execute.return_value = MagicMock(data=mock_data)

        results = await medical_service.get_extractions_by_patient_code(
            "TT04035"
        )

        assert len(results) == 2
        assert all(r["patient_code_value"] == "TT04035" for r in results)

        # Verify query parameters
        mock_supabase_client.eq.assert_called_with("patient_code_value", "TT04035")

    @pytest.mark.asyncio
    async def test_database_error_handling(
        self, medical_service, sample_medical_ocr_result, mock_supabase_client
    ):
        """Test handling of database errors."""
        # Mock database error
        mock_supabase_client.execute.side_effect = Exception(
            "Database connection failed"
        )

        with pytest.raises(Exception) as exc_info:
            await medical_service.store_extraction(
                sample_medical_ocr_result,
                user_id="test-user",
                session_id="test-session",
            )

        assert "Database connection failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_field_validation_and_transformation(
        self, medical_service, mock_supabase_client
    ):
        """Test field validation and transformation before storage."""
        # Create result with edge case values
        edge_case_result = OCRResult(
            patient_code=ExtractedField(
                value="TT99999",  # Edge case patient code
                confidence=ConfidenceLevel.MEDIUM,
                reasoning="Unusual but valid patient code format",
            ),
            sample_code=ExtractedField(value="XYZ789", confidence=ConfidenceLevel.HIGH),
            investigation=ExtractedField(
                value="K4CARE",  # Different investigation type
                confidence=ConfidenceLevel.HIGH,
            ),
            patient_name_th=ExtractedField(
                value="นางสาวพิมพ์ชนก สุขใจมาก",  # Long Thai name
                confidence=ConfidenceLevel.HIGH,
            ),
            patient_name_en=ExtractedField(
                value="Ms. Pimchanok Sukjaimaak", confidence=ConfidenceLevel.MEDIUM
            ),
            dob_gregorian=ExtractedField(
                value="2000-12-31", confidence=ConfidenceLevel.HIGH
            ),
            dob_buddhist=ExtractedField(
                value="2543-12-31", confidence=ConfidenceLevel.HIGH
            ),
            age=ExtractedField(value="24", confidence=ConfidenceLevel.HIGH),
            gender=ExtractedField(value="Female", confidence=ConfidenceLevel.HIGH),
            contact_info=ExtractedField(
                value="************, 02-123-4567",  # Multiple contact numbers
                confidence=ConfidenceLevel.MEDIUM,
            ),
            referring_physician=ExtractedField(
                value="ศ.นพ.ดร.สมิท วิลสัน",  # Thai physician with title
                confidence=ConfidenceLevel.HIGH,
            ),
            referring_physician_email=ExtractedField(
                value="<EMAIL>, <EMAIL>",  # Multiple emails
                confidence=ConfidenceLevel.MEDIUM,
            ),
            full_text="Complex medical document with multiple data points...",
            confidence_score=0.78,
        )

        mock_supabase_client.execute.return_value = MagicMock(
            data=[{"id": "edge-case-id"}]
        )

        result = await medical_service.store_extraction(
            edge_case_result, user_id="test-user", session_id="test-session"
        )

        assert result is not None
        assert result["id"] == "edge-case-id"


@pytest.mark.integration
class TestMedicalDatabaseServiceIntegration:
    """Integration tests requiring actual Supabase connection."""

    @pytest.mark.skipif(
        True,  # Skip integration tests by default
        reason="Integration tests require actual Supabase connection",
    )
    @pytest.mark.asyncio
    async def test_integration_database_connection(self):
        """Test with real Supabase connection (requires environment setup)."""
        # This test would run with real Supabase credentials
        # Skip by default to avoid requiring live database for unit tests
        pass

    def test_service_configuration_validation(self):
        """Test service configuration validation."""
        # Test invalid URL
        with pytest.raises(ValueError):
            MedicalDatabaseService()

        # Test missing key
        with pytest.raises(ValueError):
            MedicalDatabaseService()
