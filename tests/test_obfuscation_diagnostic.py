"""Diagnostic script to analyze obfuscation coordinate mapping issues.

This script provides detailed analysis of the obfuscation pipeline to identify
why rectangles might not be covering PII text correctly.
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Any, Dict, List

from src.processing.ocr_processor import GeminiOCRProcessor
from src.processing.pdf_obfuscator import Obfusca<PERSON><PERSON>ethod, PDFObfuscator
from src.processing.pii_detector import PIIDetector

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


async def diagnose_obfuscation_pipeline(
    pdf_path: str, output_dir: str = "diagnostic-results"
):
    """Comprehensive diagnostic analysis of the obfuscation pipeline.

    Args:
        pdf_path: Path to the PDF file to analyze
        output_dir: Directory to save diagnostic results
    """
    print("🔍 ChromoForge Obfuscation Pipeline Diagnostic")
    print("=" * 60)

    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)

    # Initialize components
    ocr_processor = GeminiOCRProcessor()
    pii_detector = PIIDetector()
    pdf_obfuscator = PDFObfuscator(method=ObfuscationMethod.BLACK_BOX)

    pdf_file = Path(pdf_path)
    if not pdf_file.exists():
        print(f"❌ PDF file not found: {pdf_path}")
        return

    print(f"📄 Analyzing PDF: {pdf_file.name}")
    print(f"📁 File size: {pdf_file.stat().st_size / 1024:.1f} KB")

    # Step 1: OCR Processing
    print("\n🔤 Step 1: OCR Processing")
    try:
        ocr_result = await ocr_processor.process_pdf(pdf_file)
        print(f"✅ OCR completed successfully")
        print(f"   Confidence: {ocr_result.confidence_score:.2f}")
        print(f"   Processing time: {ocr_result.processing_time:.2f}s")
        print(f"   Page count: {ocr_result.page_count}")
        print(f"   Text length: {len(ocr_result.full_text)} characters")

        # Save OCR result
        ocr_output = output_path / f"{pdf_file.stem}_ocr_diagnostic.json"
        with open(ocr_output, "w", encoding="utf-8") as f:
            json.dump(ocr_result.model_dump(), f, ensure_ascii=False, indent=2)
        print(f"   Saved OCR result to: {ocr_output}")

    except Exception as e:
        print(f"❌ OCR processing failed: {str(e)}")
        return

    # Step 2: PII Detection
    print("\n🔒 Step 2: PII Detection")
    try:
        pii_matches = pii_detector.detect_pii(ocr_result.full_text)
        print(f"✅ PII detection completed")
        print(f"   PII items found: {len(pii_matches)}")

        for i, pii_match in enumerate(pii_matches):
            print(
                f"   PII {i+1}: {pii_match.pii_type.value} - '{pii_match.text[:50]}...' (confidence: {pii_match.confidence:.2f})"
            )

        # Save PII results
        pii_output = output_path / f"{pdf_file.stem}_pii_diagnostic.json"
        pii_data = [
            {
                "type": match.pii_type.value,
                "text": match.text,
                "confidence": match.confidence,
                "line_number": match.line_number,
                "context": match.context,
            }
            for match in pii_matches
        ]
        with open(pii_output, "w", encoding="utf-8") as f:
            json.dump(pii_data, f, ensure_ascii=False, indent=2)
        print(f"   Saved PII results to: {pii_output}")

    except Exception as e:
        print(f"❌ PII detection failed: {str(e)}")
        return

    # Step 3: Text Coordinate Extraction
    print("\n📍 Step 3: Text Coordinate Extraction")
    try:
        # Try standard extraction first
        full_text, text_coordinates = (
            pdf_obfuscator.text_extractor.extract_text_with_coordinates(pdf_file)
        )

        if not text_coordinates:
            print(
                "⚠️  Standard extraction returned no coordinates, trying image-based OCR..."
            )
            full_text, text_coordinates = (
                pdf_obfuscator.image_extractor.extract_text_with_coordinates(pdf_file)
            )

        print(f"✅ Text coordinate extraction completed")
        print(f"   Text coordinates found: {len(text_coordinates)}")
        print(f"   Full text length: {len(full_text)} characters")

        # Analyze coordinate distribution
        if text_coordinates:
            pages = set(coord.page_num for coord in text_coordinates)
            print(f"   Pages with coordinates: {sorted(pages)}")

            for page_num in sorted(pages):
                page_coords = [
                    coord for coord in text_coordinates if coord.page_num == page_num
                ]
                print(f"   Page {page_num}: {len(page_coords)} text elements")

        # Save coordinate data (sample)
        coord_output = output_path / f"{pdf_file.stem}_coordinates_diagnostic.json"
        coord_data = []
        for i, coord in enumerate(
            text_coordinates[:100]
        ):  # Save first 100 for analysis
            coord_data.append(
                {
                    "index": i,
                    "page_num": coord.page_num,
                    "x1": coord.x1,
                    "y1": coord.y1,
                    "x2": coord.x2,
                    "y2": coord.y2,
                    "text": coord.text,
                    "font_size": coord.font_size,
                }
            )

        with open(coord_output, "w", encoding="utf-8") as f:
            json.dump(
                {
                    "total_coordinates": len(text_coordinates),
                    "sample_coordinates": coord_data,
                    "extraction_method": (
                        "image_based"
                        if not pdf_obfuscator.text_extractor.extract_text_with_coordinates(
                            pdf_file
                        )[
                            1
                        ]
                        else "standard"
                    ),
                },
                f,
                ensure_ascii=False,
                indent=2,
            )
        print(f"   Saved coordinate sample to: {coord_output}")

    except Exception as e:
        print(f"❌ Text coordinate extraction failed: {str(e)}")
        return

    # Step 4: PII to Coordinate Mapping
    print("\n🎯 Step 4: PII to Coordinate Mapping")
    try:
        pii_coordinate_mapping = (
            pdf_obfuscator.coordinate_mapper.map_pii_to_coordinates(
                pii_matches, text_coordinates, full_text
            )
        )

        print(f"✅ PII coordinate mapping completed")
        print(f"   PII items mapped: {len(pii_coordinate_mapping)}")

        mapping_results = []
        for i, (pii_match, coordinates) in enumerate(pii_coordinate_mapping):
            print(
                f"   PII {i+1}: '{pii_match.text[:30]}...' -> {len(coordinates)} coordinates"
            )

            mapping_results.append(
                {
                    "pii_text": pii_match.text,
                    "pii_type": pii_match.pii_type.value,
                    "pii_confidence": pii_match.confidence,
                    "coordinates_found": len(coordinates),
                    "coordinates": [
                        {
                            "page_num": coord.page_num,
                            "x1": coord.x1,
                            "y1": coord.y1,
                            "x2": coord.x2,
                            "y2": coord.y2,
                            "text": coord.text,
                            "font_size": coord.font_size,
                        }
                        for coord in coordinates
                    ],
                }
            )

            if coordinates:
                for j, coord in enumerate(coordinates):
                    print(
                        f"     Coord {j+1}: Page {coord.page_num}, ({coord.x1:.1f}, {coord.y1:.1f}) -> ({coord.x2:.1f}, {coord.y2:.1f}), Text: '{coord.text[:20]}...'"
                    )
            else:
                print(f"     ⚠️  No coordinates found for this PII item!")

        # Save mapping results
        mapping_output = output_path / f"{pdf_file.stem}_mapping_diagnostic.json"
        with open(mapping_output, "w", encoding="utf-8") as f:
            json.dump(mapping_results, f, ensure_ascii=False, indent=2)
        print(f"   Saved mapping results to: {mapping_output}")

    except Exception as e:
        print(f"❌ PII coordinate mapping failed: {str(e)}")
        return

    # Step 5: Obfuscation Application
    print("\n🖤 Step 5: Obfuscation Application")
    try:
        obfuscated_pdf = output_path / f"obfuscated_{pdf_file.name}"

        result = await pdf_obfuscator.obfuscate_pdf(
            pdf_file, obfuscated_pdf, pii_matches
        )

        print(f"✅ Obfuscation completed")
        print(f"   Success: {result['success']}")
        print(f"   PII items found: {result['pii_items_found']}")
        print(f"   PII items obfuscated: {result['pii_items_obfuscated']}")
        print(f"   Obfuscation method: {result['obfuscation_method']}")
        print(f"   Output file: {obfuscated_pdf}")

        if obfuscated_pdf.exists():
            print(f"   Output file size: {obfuscated_pdf.stat().st_size / 1024:.1f} KB")

        # Save obfuscation results
        obfuscation_output = (
            output_path / f"{pdf_file.stem}_obfuscation_diagnostic.json"
        )
        with open(obfuscation_output, "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"   Saved obfuscation results to: {obfuscation_output}")

    except Exception as e:
        print(f"❌ Obfuscation application failed: {str(e)}")
        return

    # Step 6: Analysis and Recommendations
    print("\n📊 Step 6: Analysis and Recommendations")

    total_pii = len(pii_matches)
    total_obfuscated = result.get("pii_items_obfuscated", 0)
    obfuscation_rate = (total_obfuscated / total_pii * 100) if total_pii > 0 else 0

    print(
        f"   Obfuscation success rate: {obfuscation_rate:.1f}% ({total_obfuscated}/{total_pii})"
    )

    if obfuscation_rate < 100:
        print("\n⚠️  ISSUES DETECTED:")

        unmapped_pii = []
        for pii_match, coordinates in pii_coordinate_mapping:
            if not coordinates:
                unmapped_pii.append(pii_match)

        if unmapped_pii:
            print(
                f"   • {len(unmapped_pii)} PII items could not be mapped to coordinates:"
            )
            for pii in unmapped_pii:
                print(f"     - {pii.pii_type.value}: '{pii.text[:50]}...'")

        print("\n🔧 RECOMMENDATIONS:")
        print("   1. Check if PII text exactly matches extracted text coordinates")
        print("   2. Verify fuzzy matching tolerance (currently 0.8)")
        print("   3. Consider improving OCR accuracy for handwritten text")
        print("   4. Check coordinate conversion from image space to PDF space")

        if len(text_coordinates) == 0:
            print("   5. ⚠️  CRITICAL: No text coordinates found - check OCR extraction")
        elif len(text_coordinates) < 100:
            print(
                "   5. ⚠️  WARNING: Very few text coordinates found - check OCR quality"
            )
    else:
        print("✅ All PII items successfully obfuscated!")

    print(f"\n📁 All diagnostic files saved to: {output_path}")
    print("🔍 Diagnostic analysis complete!")


async def main():
    """Main diagnostic function."""
    # Test with the known problematic PDF
    pdf_path = "original-pdf-examples/CSF TT04049_Pathology.pdf"

    if not Path(pdf_path).exists():
        print(f"❌ Test PDF not found: {pdf_path}")
        print(
            "Please ensure the PDF file exists in the original-pdf-examples directory"
        )
        return

    await diagnose_obfuscation_pipeline(pdf_path)


if __name__ == "__main__":
    asyncio.run(main())
