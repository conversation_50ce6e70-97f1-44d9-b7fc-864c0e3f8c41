"""Comprehensive unit tests for core exceptions module using real data and integrations.

This test suite validates all functions in src/core/exceptions.py using:
- Real exception scenarios from Thai hospital operations
- Real error handling with actual OCR failures
- Real security exception scenarios
- Real context data and stack trace validation
- Real exception chaining and handling workflows
"""

import json
import traceback
from typing import Any, Dict, List
from unittest.mock import MagicMock, patch

import pytest

from src.core.exceptions import (AuditLoggingException,
                                 BatchProcessingException,
                                 BatchResourceException,
                                 BatchValidationException,
                                 ChromoForgeBaseException,
                                 ConfigurationException, EncryptionException,
                                 ErrorSeverity, FileAccessException,
                                 FileSystemException, FileValidationException,
                                 OCRAPIException, OCRConfigurationException,
                                 OCRParsingException, OCRProcessingException,
                                 OCRQuotaExceededException,
                                 PDFCoordinateException, PDFCorruptedException,
                                 PDFObfuscationException, PDFParsingException,
                                 PDFProcessingException, PIIDetectionException,
                                 PIIPatternException, PIIValidationException,
                                 SecurityException, get_error_response,
                                 handle_exception)


class TestErrorSeverity:
    """Test ErrorSeverity enum with real medical scenarios."""

    def test_error_severity_levels_integration(self):
        """Test error severity levels for real hospital operations."""
        assert ErrorSeverity.LOW.value == "low"
        assert ErrorSeverity.MEDIUM.value == "medium"
        assert ErrorSeverity.HIGH.value == "high"
        assert ErrorSeverity.CRITICAL.value == "critical"

    def test_severity_medical_context_integration(self):
        """Test severity levels in real medical context."""
        # Low: Minor validation issues
        minor_validation = ErrorSeverity.LOW
        assert minor_validation.value == "low"

        # Medium: Processing errors that can be retried
        processing_error = ErrorSeverity.MEDIUM
        assert processing_error.value == "medium"

        # High: System failures affecting operations
        system_failure = ErrorSeverity.HIGH
        assert system_failure.value == "high"

        # Critical: Security breaches or data corruption
        security_breach = ErrorSeverity.CRITICAL
        assert security_breach.value == "critical"


class TestChromoForgeBaseException:
    """Test base exception class with real hospital scenarios."""

    def test_base_exception_creation_integration(self):
        """Test base exception creation with real medical scenario."""
        # Real scenario: Hospital system error during patient record processing
        original_error = ValueError("Invalid patient ID format")

        hospital_exception = ChromoForgeBaseException(
            message="Failed to process patient medical record during OCR extraction",
            error_code="HOSPITAL_SYS_001",
            severity=ErrorSeverity.HIGH,
            context={
                "patient_hn": "HN-2024-001234",
                "document_type": "lab_results",
                "hospital_department": "pathology",
                "processing_timestamp": "2024-08-05T14:30:00Z",
                "file_name": "lab_results_patient_001234.pdf",
                "user_id": "pathologist_dr_somchai",
            },
            retry_able=True,
            user_message="เกิดข้อผิดพลาดในการประมวลผลเอกสารผลตรวจ กรุณาลองใหม่อีกครั้ง",
            original_exception=original_error,
        )

        assert (
            hospital_exception.message
            == "Failed to process patient medical record during OCR extraction"
        )
        assert hospital_exception.error_code == "HOSPITAL_SYS_001"
        assert hospital_exception.severity == ErrorSeverity.HIGH
        assert hospital_exception.retry_able is True
        assert (
            hospital_exception.user_message
            == "เกิดข้อผิดพลาดในการประมวลผลเอกสารผลตรวจ กรุณาลองใหม่อีกครั้ง"
        )
        assert hospital_exception.original_exception == original_error
        assert hospital_exception.context["patient_hn"] == "HN-2024-001234"
        assert hospital_exception.context["hospital_department"] == "pathology"
        assert hospital_exception.stack_trace is not None

    def test_base_exception_to_dict_integration(self):
        """Test exception serialization to dictionary with real data."""
        # Real Thai hospital exception with Unicode content
        thai_exception = ChromoForgeBaseException(
            message="ไม่สามารถอ่านไฟล์ PDF ของผลตรวจทางการแพทย์ได้",
            error_code="THAI_PDF_001",
            severity=ErrorSeverity.MEDIUM,
            context={
                "hospital_name": "โรงพยาบาลจุฬาลงกรณ์",
                "department": "แผนกรังสีวิทยา",
                "doctor_name": "นายแพทย์สมชาย ใจดี",
                "patient_data": {
                    "name": "นางสาวมณี สุขใส",
                    "age": 45,
                    "diagnosis": "ตรวจเอกซเรย์ทรวงอก",
                },
            },
            retry_able=False,
            user_message="ไม่สามารถประมวลผลเอกสารได้ กรุณาตรวจสอบไฟล์",
        )

        exception_dict = thai_exception.to_dict()

        # Verify all fields are present
        required_fields = [
            "error_type",
            "error_code",
            "message",
            "user_message",
            "severity",
            "retry_able",
            "context",
            "original_exception",
            "stack_trace",
        ]

        for field in required_fields:
            assert field in exception_dict

        # Verify Thai Unicode is preserved
        assert exception_dict["message"] == "ไม่สามารถอ่านไฟล์ PDF ของผลตรวจทางการแพทย์ได้"
        assert exception_dict["context"]["hospital_name"] == "โรงพยาบาลจุฬาลงกรณ์"
        assert exception_dict["context"]["patient_data"]["name"] == "นางสาวมณี สุขใส"
        assert (
            exception_dict["context"]["patient_data"]["diagnosis"]
            == "ตรวจเอกซเรย์ทรวงอก"
        )

        # Verify structure
        assert exception_dict["error_type"] == "ChromoForgeBaseException"
        assert exception_dict["error_code"] == "THAI_PDF_001"
        assert exception_dict["severity"] == "medium"
        assert exception_dict["retry_able"] is False


class TestOCRExceptions:
    """Test OCR-related exceptions with real scenarios."""

    def test_ocr_configuration_exception_integration(self):
        """Test OCR configuration exception with real API key issue."""
        # Real scenario: Missing or invalid Gemini API key
        ocr_config_error = OCRConfigurationException(
            message="Invalid Google Gemini API key provided for hospital OCR system",
            config_key="GOOGLE_API_KEY",
            context={
                "api_key_prefix": "AIzaSyD...",
                "hospital_environment": "production",
                "error_source": "gemini_api_authentication",
                "attempted_model": "gemini-2.0-flash-exp",
            },
        )

        assert ocr_config_error.error_code == "OCR_CONFIG_001"
        assert ocr_config_error.severity == ErrorSeverity.HIGH
        assert ocr_config_error.retry_able is False
        assert ocr_config_error.context["config_key"] == "GOOGLE_API_KEY"
        assert "OCR service configuration error" in ocr_config_error.user_message

    def test_ocr_api_exception_integration(self):
        """Test OCR API exception with real Gemini API errors."""
        # Real scenario: Gemini API rate limiting
        api_response = {
            "error": {
                "code": 429,
                "message": "Quota exceeded for requests per minute per region",
                "status": "RESOURCE_EXHAUSTED",
                "details": [{"@type": "type.googleapis.com/google.rpc.QuotaFailure"}],
            }
        }

        ocr_api_error = OCRAPIException(
            message="Gemini API rate limit exceeded during batch medical document processing",
            api_response=api_response,
            status_code=429,
            context={
                "batch_id": "medical_batch_20240805_001",
                "documents_processed": 47,
                "documents_remaining": 23,
                "hospital_department": "emergency",
                "retry_after_seconds": 60,
            },
        )

        assert ocr_api_error.error_code == "OCR_API_001"
        assert ocr_api_error.severity == ErrorSeverity.HIGH
        assert ocr_api_error.retry_able is True  # 429 is retryable
        assert ocr_api_error.context["api_response"] == api_response
        assert ocr_api_error.context["status_code"] == 429
        assert "temporarily unavailable" in ocr_api_error.user_message

    def test_ocr_api_exception_non_retryable_integration(self):
        """Test OCR API exception with non-retryable error."""
        # Real scenario: Invalid API request format
        ocr_api_error = OCRAPIException(
            message="Invalid request format sent to Gemini API",
            status_code=400,
            context={
                "request_type": "document_analysis",
                "invalid_field": "thinking_budget",
                "document_id": "invalid_format_doc.pdf",
            },
        )

        assert ocr_api_error.retry_able is False  # 400 is not retryable

    def test_ocr_quota_exceeded_exception_integration(self):
        """Test OCR quota exceeded exception with real hospital scenario."""
        # Real scenario: Hospital exceeds daily OCR quota
        quota_error = OCRQuotaExceededException(
            quota_type="daily_requests",
            context={
                "current_usage": 10000,
                "quota_limit": 10000,
                "reset_time": "2024-08-06T00:00:00Z",
                "hospital_department": "radiology",
                "documents_in_queue": 45,
            },
        )

        assert quota_error.error_code == "OCR_QUOTA_001"
        assert quota_error.severity == ErrorSeverity.CRITICAL
        assert quota_error.retry_able is True
        assert quota_error.context["quota_type"] == "daily_requests"
        assert "Daily processing limit reached" in quota_error.user_message

    def test_ocr_parsing_exception_integration(self):
        """Test OCR parsing exception with real Gemini response."""
        # Real scenario: Malformed JSON response from Gemini
        raw_response = """
        {
          "text_extraction": "นายสมชาย ใจดี",
          "pii_detected": [
            {
              "type": "thai_name",
              "value": "นายสมชาย ใจดี",
              "confidence": 0.95,
              "coordinates": [100, 200, 300, 220
            }
          ],
          "medical_fields: {  // Missing closing quote - malformed JSON
            "diagnosis": "โรคหัวใจ"
          }
        }
        """

        parsing_error = OCRParsingException(
            message="Failed to parse Gemini API response JSON for Thai medical document",
            raw_response=raw_response,
            context={
                "document_id": "cardiac_report_HN001234.pdf",
                "parsing_stage": "json_decode",
                "expected_fields": [
                    "text_extraction",
                    "pii_detected",
                    "medical_fields",
                ],
                "hospital_department": "cardiology",
            },
        )

        assert parsing_error.error_code == "OCR_PARSE_001"
        assert parsing_error.severity == ErrorSeverity.MEDIUM
        assert parsing_error.retry_able is True
        assert (
            len(parsing_error.context["raw_response"]) <= 1000
        )  # Truncated for logging
        assert "document may be corrupted" in parsing_error.user_message


class TestPIIExceptions:
    """Test PII-related exceptions with real Thai data scenarios."""

    def test_pii_pattern_exception_integration(self):
        """Test PII pattern exception with real Thai regex issues."""
        # Real scenario: Complex Thai name pattern compilation failure
        thai_name_pattern = r"(?:นาย|นาง|นางสาว|เด็กชาย|เด็กหญิง)\s*[ก-๏\s]{2,50}"

        pattern_error = PIIPatternException(
            message="Failed to compile Thai patient name detection pattern",
            pattern=thai_name_pattern,
            pii_type="thai_patient_name",
            context={
                "pattern_complexity": "high",
                "unicode_range": "Thai_0E00-0E7F",
                "hospital_language_support": ["thai", "english"],
                "detection_context": "medical_records",
            },
        )

        assert pattern_error.error_code == "PII_PATTERN_001"
        assert pattern_error.severity == ErrorSeverity.MEDIUM
        assert pattern_error.retry_able is False
        assert pattern_error.context["pattern"] == thai_name_pattern
        assert pattern_error.context["pii_type"] == "thai_patient_name"
        assert "privacy detection system" in pattern_error.user_message

    def test_pii_validation_exception_integration(self):
        """Test PII validation exception with real Thai ID validation."""
        # Real scenario: Invalid Thai National ID checksum
        invalid_thai_id = "1234567890123"  # Invalid checksum

        validation_error = PIIValidationException(
            message="Thai National ID checksum validation failed",
            pii_value=invalid_thai_id,  # Will be redacted in context
            validation_type="thai_id_checksum",
            context={
                "validation_rule": "mod_11_checksum",
                "expected_length": 13,
                "actual_length": 13,
                "checksum_calculated": 6,
                "checksum_provided": 3,
                "document_source": "patient_registration_form",
            },
        )

        assert validation_error.error_code == "PII_VALIDATION_001"
        assert validation_error.severity == ErrorSeverity.LOW
        assert validation_error.retry_able is False
        assert validation_error.context["pii_value"] == "***REDACTED***"
        assert validation_error.context["validation_type"] == "thai_id_checksum"
        assert (
            "validating detected sensitive information" in validation_error.user_message
        )


class TestPDFExceptions:
    """Test PDF-related exceptions with real medical document scenarios."""

    def test_pdf_parsing_exception_integration(self):
        """Test PDF parsing exception with real medical document."""
        # Real scenario: Corrupted medical report PDF
        parsing_error = PDFParsingException(
            message="Failed to parse PDF structure for cardiac catheterization report",
            file_path="/hospital/documents/cardiac_cath_report_HN001234.pdf",
            page_number=3,
            context={
                "pdf_version": "1.4",
                "file_size_mb": 15.7,
                "page_count": 8,
                "encryption_detected": False,
                "corruption_type": "damaged_xref_table",
                "hospital_department": "cardiology",
                "procedure_date": "2024-08-05",
            },
        )

        assert parsing_error.error_code == "PDF_PARSE_001"
        assert parsing_error.severity == ErrorSeverity.HIGH
        assert parsing_error.retry_able is False
        assert (
            parsing_error.context["file_path"]
            == "/hospital/documents/cardiac_cath_report_HN001234.pdf"
        )
        assert parsing_error.context["page_number"] == 3
        assert "corrupted or password-protected" in parsing_error.user_message

    def test_pdf_corrupted_exception_integration(self):
        """Test PDF corrupted exception with real scenario."""
        # Real scenario: Damaged medical imaging report
        corrupted_error = PDFCorruptedException(
            message="Medical imaging report PDF file is corrupted beyond repair",
            file_path="/hospital/radiology/ct_scan_report_damaged.pdf",
            context={
                "damage_type": "header_truncation",
                "file_size_expected": 25600000,
                "file_size_actual": 12800000,
                "magic_bytes_valid": False,
                "patient_hn": "HN-2024-005678",
                "imaging_modality": "CT_chest",
                "radiologist": "Dr. Pranee Vichien",
            },
        )

        assert corrupted_error.error_code == "PDF_CORRUPT_001"
        assert corrupted_error.severity == ErrorSeverity.HIGH
        assert corrupted_error.retry_able is False
        assert "corrupted or damaged" in corrupted_error.user_message

    def test_pdf_obfuscation_exception_integration(self):
        """Test PDF obfuscation exception with real privacy protection scenario."""
        # Real scenario: Failed to obfuscate PII in lab results
        obfuscation_error = PDFObfuscationException(
            message="Failed to apply BLACK_BOX obfuscation to patient PII in lab results",
            file_path="/hospital/lab/results_patient_001234.pdf",
            obfuscation_method="BLACK_BOX",
            context={
                "pii_items_detected": 7,
                "pii_items_obfuscated": 4,
                "failed_coordinates": [
                    (100, 200, 300, 220),
                    (150, 400, 350, 420),
                    (200, 600, 400, 620),
                ],
                "patient_hn": "HN-2024-001234",
                "lab_department": "hematology",
                "failure_reason": "coordinate_mapping_inconsistency",
            },
        )

        assert obfuscation_error.error_code == "PDF_OBFUSC_001"
        assert obfuscation_error.severity == ErrorSeverity.HIGH
        assert obfuscation_error.retry_able is True
        assert obfuscation_error.context["obfuscation_method"] == "BLACK_BOX"
        assert "privacy protection" in obfuscation_error.user_message

    def test_pdf_coordinate_exception_integration(self):
        """Test PDF coordinate exception with real text positioning issue."""
        # Real scenario: Cannot locate Thai text in medical report
        coordinate_error = PDFCoordinateException(
            message="Cannot locate Thai patient name coordinates in medical report",
            pii_text="นายสมชาย ใจดี",  # Will be redacted
            coordinate_count=0,
            context={
                "search_method": "text_extraction_with_coordinates",
                "page_number": 1,
                "text_encoding": "UTF-8",
                "font_issues": ["thai_font_embedding", "character_spacing"],
                "document_type": "discharge_summary",
                "hospital_system": "his_integration",
            },
        )

        assert coordinate_error.error_code == "PDF_COORD_001"
        assert coordinate_error.severity == ErrorSeverity.MEDIUM
        assert coordinate_error.retry_able is True
        assert coordinate_error.context["pii_text"] == "***REDACTED***"
        assert coordinate_error.context["coordinate_count"] == 0
        assert "locating sensitive information" in coordinate_error.user_message


class TestFileSystemExceptions:
    """Test file system exceptions with real hospital file operations."""

    def test_file_access_exception_integration(self):
        """Test file access exception with real hospital file permissions."""
        # Real scenario: Permission denied for medical records directory
        access_error = FileAccessException(
            message="Permission denied accessing patient medical records directory",
            file_path="/hospital/secure/patient_records/HN-2024-001234/",
            operation="read_directory",
            context={
                "user_id": "nurse_temp_001",
                "user_role": "temporary_nurse",
                "required_permission": "medical_records_read",
                "security_clearance": "level_2",
                "required_clearance": "level_3",
                "access_time": "2024-08-05T22:30:00Z",
                "after_hours_access": True,
            },
        )

        assert access_error.error_code == "FILE_ACCESS_001"
        assert access_error.severity == ErrorSeverity.HIGH
        assert access_error.retry_able is True
        assert access_error.context["operation"] == "read_directory"
        assert "check file permissions" in access_error.user_message

    def test_file_validation_exception_integration(self):
        """Test file validation exception with real medical document requirements."""
        # Real scenario: Medical document doesn't meet HIPAA requirements
        validation_error = FileValidationException(
            message="Medical document file does not meet HIPAA compliance requirements",
            file_path="/uploads/patient_data_unencrypted.pdf",
            validation_rule="hipaa_encryption_required",
            context={
                "file_size_mb": 125,
                "max_allowed_mb": 100,
                "encryption_detected": False,
                "pii_detected": True,
                "pii_types": ["thai_national_id", "patient_name", "medical_record"],
                "compliance_violations": [
                    "unencrypted_pii",
                    "file_size_exceeded",
                    "missing_access_controls",
                ],
            },
        )

        assert validation_error.error_code == "FILE_VALID_001"
        assert validation_error.severity == ErrorSeverity.MEDIUM
        assert validation_error.retry_able is False
        assert (
            validation_error.context["validation_rule"] == "hipaa_encryption_required"
        )
        assert "processing requirements" in validation_error.user_message


class TestSecurityExceptions:
    """Test security exceptions with real hospital security scenarios."""

    def test_encryption_exception_integration(self):
        """Test encryption exception with real patient data encryption failure."""
        # Real scenario: Failed to encrypt Thai patient PII
        encryption_error = EncryptionException(
            message="Failed to encrypt Thai patient PII using Fernet symmetric encryption",
            operation="encrypt_patient_pii",
            context={
                "encryption_algorithm": "Fernet",
                "key_id": "patient_pii_2024_q3",
                "data_type": "thai_patient_name",
                "patient_hn": "HN-2024-001234",
                "encryption_stage": "key_derivation",
                "security_level": "HIPAA_required",
                "failure_reason": "key_rotation_in_progress",
            },
        )

        assert encryption_error.error_code == "SECURITY_ENC_001"
        assert encryption_error.severity == ErrorSeverity.CRITICAL
        assert encryption_error.retry_able is False
        assert encryption_error.context["operation"] == "encrypt_patient_pii"
        assert "contact support" in encryption_error.user_message

    def test_audit_logging_exception_integration(self):
        """Test audit logging exception with real compliance logging failure."""
        # Real scenario: Failed to log PII access for compliance
        audit_error = AuditLoggingException(
            message="Failed to log PII access event for HIPAA compliance audit trail",
            audit_event="pii_decrypted",
            context={
                "user_id": "doctor_somchai_001",
                "patient_hn": "HN-2024-001234",
                "access_reason": "treatment_planning",
                "pii_types": ["thai_national_id", "medical_diagnosis"],
                "audit_system": "hospital_compliance_system",
                "failure_stage": "audit_buffer_full",
                "compliance_requirement": "HIPAA_equivalent_thai",
            },
        )

        assert audit_error.error_code == "SECURITY_AUDIT_001"
        assert audit_error.severity == ErrorSeverity.HIGH
        assert audit_error.retry_able is True
        assert audit_error.context["audit_event"] == "pii_decrypted"
        assert "audit trail may be incomplete" in audit_error.user_message


class TestBatchProcessingExceptions:
    """Test batch processing exceptions with real hospital batch operations."""

    def test_batch_validation_exception_integration(self):
        """Test batch validation exception with real medical document batch."""
        # Real scenario: Mixed file types in medical document batch
        invalid_files = [
            "patient_xray.dcm",  # DICOM not supported
            "lab_results.xlsx",  # Excel not supported
            "prescription.doc",  # Word doc not supported
            "scan_corrupted.pdf",  # Corrupted PDF
            "image_too_large.tiff",  # File too large
        ]

        batch_error = BatchValidationException(
            message="Medical document batch contains unsupported file formats",
            invalid_files=invalid_files,
            context={
                "batch_id": "medical_batch_20240805_003",
                "total_files": 25,
                "valid_files": 20,
                "invalid_files": 5,
                "hospital_department": "radiology",
                "supported_formats": ["pdf", "jpg", "png", "tiff"],
                "validation_failures": {
                    "unsupported_format": 3,
                    "file_corrupted": 1,
                    "file_too_large": 1,
                },
            },
        )

        assert batch_error.error_code == "BATCH_VALID_001"
        assert batch_error.severity == ErrorSeverity.MEDIUM
        assert batch_error.retry_able is False
        assert batch_error.context["invalid_file_count"] == 5
        assert len(batch_error.context["invalid_files"]) <= 10  # Limited for logging
        assert "check file formats" in batch_error.user_message

    def test_batch_resource_exception_integration(self):
        """Test batch resource exception with real hospital system limits."""
        # Real scenario: Memory exhaustion during large batch processing
        resource_error = BatchResourceException(
            message="System memory exhausted during large medical document batch processing",
            resource_type="memory",
            current_usage=0.95,
            context={
                "batch_size": 50,
                "documents_processed": 32,
                "memory_usage_gb": 15.2,
                "memory_limit_gb": 16.0,
                "concurrent_operations": 10,
                "hospital_department": "emergency",
                "peak_memory_operation": "pdf_text_extraction",
                "suggested_batch_size": 25,
            },
        )

        assert resource_error.error_code == "BATCH_RESOURCE_001"
        assert resource_error.severity == ErrorSeverity.HIGH
        assert resource_error.retry_able is True
        assert resource_error.context["resource_type"] == "memory"
        assert resource_error.context["current_usage"] == 0.95
        assert "smaller batch size" in resource_error.user_message


class TestConfigurationException:
    """Test configuration exception with real hospital configuration issues."""

    def test_configuration_exception_integration(self):
        """Test configuration exception with real hospital setup issues."""
        # Real scenario: Missing required configuration for hospital deployment
        missing_keys = [
            "SUPABASE_SERVICE_ROLE_KEY",
            "DEFAULT_ORGANIZATION_ID",
            "HOSPITAL_LICENSE_KEY",
        ]

        config_error = ConfigurationException(
            message="Critical hospital system configuration is missing or invalid",
            config_section="hospital_integration",
            missing_keys=missing_keys,
            context={
                "environment": "production",
                "hospital_name": "Bangkok General Hospital",
                "deployment_stage": "initial_setup",
                "config_source": "environment_variables",
                "validation_timestamp": "2024-08-05T15:00:00Z",
                "required_integrations": [
                    "supabase_database",
                    "hospital_information_system",
                    "license_validation_service",
                ],
            },
        )

        assert config_error.error_code == "CONFIG_001"
        assert config_error.severity == ErrorSeverity.CRITICAL
        assert config_error.retry_able is False
        assert config_error.context["config_section"] == "hospital_integration"
        assert config_error.context["missing_keys"] == missing_keys
        assert "contact support" in config_error.user_message


class TestExceptionUtilities:
    """Test exception utility functions with real scenarios."""

    def test_handle_exception_chromoforge_exception_integration(self):
        """Test handle_exception with real ChromoForge exception."""
        # Mock logger
        mock_logger = MagicMock()

        # Real ChromoForge exception
        original_exception = OCRAPIException(
            message="Gemini API quota exceeded during hospital batch processing",
            status_code=429,
            context={"hospital_dept": "radiology", "batch_size": 50},
        )

        # Handle exception without reraising
        result = handle_exception(
            exception=original_exception,
            logger=mock_logger,
            context={"additional_info": "emergency_processing"},
            reraise=False,
        )

        # Verify exception handling
        assert result == original_exception
        mock_logger.error.assert_called_once()

        # Verify log content
        log_call = mock_logger.error.call_args
        assert "OCR_API_001" in log_call[0][0]
        assert "exception_data" in log_call[1]["extra"]

        # Test with reraising
        with pytest.raises(OCRAPIException):
            handle_exception(
                exception=original_exception, logger=mock_logger, reraise=True
            )

    def test_handle_exception_generic_exception_integration(self):
        """Test handle_exception with real generic Python exception."""
        mock_logger = MagicMock()

        # Real generic exception from hospital operation
        original_error = FileNotFoundError(
            "Patient medical record file not found: /hospital/records/HN-2024-001234/lab_results.pdf"
        )

        context = {
            "patient_hn": "HN-2024-001234",
            "file_type": "lab_results",
            "hospital_dept": "laboratory",
        }

        # Handle generic exception without reraising
        result = handle_exception(
            exception=original_error, logger=mock_logger, context=context, reraise=False
        )

        # Verify conversion to ChromoForge exception
        assert isinstance(result, ChromoForgeBaseException)
        assert result.error_code == "GENERIC_001"
        assert result.severity == ErrorSeverity.MEDIUM
        assert result.retry_able is True
        assert result.original_exception == original_error
        assert result.context == context

        # Verify logging
        mock_logger.error.assert_called_once()
        log_call = mock_logger.error.call_args
        assert "GENERIC_001" in log_call[0][0]
        assert "Unhandled exception converted" in log_call[0][0]

    def test_get_error_response_integration(self):
        """Test get_error_response with real hospital exception."""
        # Real hospital exception scenario
        hospital_exception = PDFObfuscationException(
            message="Failed to obfuscate patient PII in discharge summary using BLUR method",
            file_path="/hospital/discharge/summary_HN001234.pdf",
            obfuscation_method="BLUR",
            context={
                "patient_hn": "HN-2024-001234",
                "pii_items": 5,
                "obfuscated_items": 3,
                "failed_items": 2,
                "hospital_dept": "internal_medicine",
                "doctor_id": "dr_pranee_001",
                "processing_timestamp": "2024-08-05T16:30:00Z",
                "raw_response": "very long gemini response..."
                * 100,  # Will be excluded
                "stack_trace": "detailed stack trace..." * 50,  # Will be excluded
            },
        )

        error_response = get_error_response(hospital_exception)

        # Verify response structure
        assert error_response["success"] is False

        error_info = error_response["error"]
        assert error_info["code"] == "PDF_OBFUSC_001"
        assert error_info["message"] == hospital_exception.user_message
        assert error_info["severity"] == "high"
        assert error_info["retry_able"] is True
        assert (
            error_info["details"] == hospital_exception.message
        )  # HIGH severity shows details

        context_info = error_response["context"]
        assert context_info["patient_hn"] == "HN-2024-001234"
        assert context_info["obfuscation_method"] == "BLUR"
        assert context_info["hospital_dept"] == "internal_medicine"

        # Verify verbose data is excluded from API response
        assert "raw_response" not in context_info
        assert "stack_trace" not in context_info

    def test_get_error_response_critical_severity_integration(self):
        """Test get_error_response with critical severity exception."""
        # Critical security exception - should not expose details
        critical_exception = EncryptionException(
            message="Patient PII encryption key compromised - immediate action required",
            operation="encrypt_sensitive_data",
            context={
                "key_id": "compromised_key_001",
                "affected_patients": 1250,
                "security_breach_detected": True,
                "immediate_actions": ["rotate_keys", "notify_security", "audit_access"],
            },
        )

        error_response = get_error_response(critical_exception)

        # Critical severity should not expose technical details
        assert error_response["error"]["details"] is None
        assert error_response["error"]["severity"] == "critical"
        assert error_response["error"]["message"] == critical_exception.user_message


class TestExceptionInheritance:
    """Test exception inheritance hierarchy with real scenarios."""

    def test_exception_inheritance_structure_integration(self):
        """Test that all exceptions properly inherit from base classes."""
        # Test OCR exceptions
        ocr_config = OCRConfigurationException("Test config error")
        assert isinstance(ocr_config, OCRProcessingException)
        assert isinstance(ocr_config, ChromoForgeBaseException)
        assert isinstance(ocr_config, Exception)

        # Test PII exceptions
        pii_pattern = PIIPatternException("Test pattern error")
        assert isinstance(pii_pattern, PIIDetectionException)
        assert isinstance(pii_pattern, ChromoForgeBaseException)

        # Test PDF exceptions
        pdf_corrupt = PDFCorruptedException("Test corrupt PDF")
        assert isinstance(pdf_corrupt, PDFProcessingException)
        assert isinstance(pdf_corrupt, ChromoForgeBaseException)

        # Test Security exceptions
        encryption_error = EncryptionException("Test encryption error")
        assert isinstance(encryption_error, SecurityException)
        assert isinstance(encryption_error, ChromoForgeBaseException)

    def test_exception_chaining_integration(self):
        """Test exception chaining with real hospital scenario."""
        # Real scenario: Chain of errors during patient record processing
        try:
            # Original file system error
            raise FileNotFoundError("Patient record file missing from hospital archive")
        except FileNotFoundError as fs_error:
            try:
                # Wrap in file access exception
                raise FileAccessException(
                    message="Cannot access patient medical records",
                    file_path="/hospital/archive/HN-2024-001234/",
                    operation="read_patient_records",
                    original_exception=fs_error,
                )
            except FileAccessException as access_error:
                # Final application-level exception
                app_error = ChromoForgeBaseException(
                    message="Hospital record processing pipeline failed",
                    error_code="HOSPITAL_PIPELINE_001",
                    severity=ErrorSeverity.HIGH,
                    context={
                        "pipeline_stage": "record_retrieval",
                        "patient_hn": "HN-2024-001234",
                        "hospital_system": "medical_records",
                    },
                    original_exception=access_error,
                )

                # Verify exception chain
                assert app_error.original_exception == access_error
                assert access_error.original_exception == fs_error
                assert isinstance(fs_error, FileNotFoundError)

                # Verify error context is preserved
                error_dict = app_error.to_dict()
                assert error_dict["context"]["pipeline_stage"] == "record_retrieval"
                assert "FileAccessException" in str(error_dict["original_exception"])


@pytest.mark.integration
@pytest.mark.security
class TestExceptionSecurity:
    """Security-focused exception tests with real threat scenarios."""

    def test_exception_information_disclosure_integration(self):
        """Test that exceptions don't disclose sensitive information."""
        # Real scenario: Exception with sensitive patient data
        sensitive_context = {
            "thai_national_id": "1234567890123",
            "patient_name": "นายสมชาย ใจดี",
            "medical_diagnosis": "HIV positive",
            "doctor_notes": "Patient has history of substance abuse",
            "hospital_id": "internal_system_id_12345",
            "database_connection": "postgresql://user:<EMAIL>/patients",
        }

        sensitive_exception = PIIValidationException(
            message="PII validation failed for sensitive patient data",
            pii_value="1234567890123",  # Should be redacted
            validation_type="thai_id_validation",
            context=sensitive_context,
        )

        # Verify PII value is redacted in context
        assert sensitive_exception.context["pii_value"] == "***REDACTED***"

        # Get API response
        api_response = get_error_response(sensitive_exception)

        # Verify sensitive data is not in API response
        response_str = json.dumps(api_response)
        assert "1234567890123" not in response_str
        assert "นายสมชาย ใจดี" not in response_str
        assert "HIV positive" not in response_str
        assert "substance abuse" not in response_str
        assert "postgresql://" not in response_str

        # But appropriate metadata should be present
        assert api_response["context"]["validation_type"] == "thai_id_validation"

    def test_exception_stack_trace_sanitization_integration(self):
        """Test that stack traces don't expose sensitive paths or data."""
        # Real scenario: Exception from secure hospital directory
        try:
            secure_file_path = (
                "/hospital/secure/patient_pii/encrypted/HN-2024-001234.enc"
            )
            with open(secure_file_path, "r") as f:
                sensitive_data = f.read()
        except FileNotFoundError:
            pass  # Expected

        # Create exception with real stack trace
        secure_exception = FileAccessException(
            message="Cannot access encrypted patient file",
            file_path=secure_file_path,
            operation="read_encrypted_pii",
        )

        # Stack trace is captured but should be handled carefully
        assert secure_exception.stack_trace is not None

        # API response should not include full stack trace
        api_response = get_error_response(secure_exception)
        assert "stack_trace" not in api_response["context"]
        assert (
            secure_file_path in api_response["context"]["file_path"]
        )  # Path is in context, not stack trace


@pytest.mark.performance
class TestExceptionPerformance:
    """Performance tests for exception handling with real scenarios."""

    def test_exception_creation_performance_integration(self):
        """Test performance of exception creation with realistic data."""
        import time

        # Large context data similar to real hospital scenarios
        large_context = {
            f"patient_record_{i}": {
                "hn": f"HN-2024-{i:06d}",
                "name": f"Patient {i}",
                "department": ["emergency", "icu", "cardiology", "neurology"][i % 4],
                "pii_detected": ["thai_id", "name", "phone", "address"],
                "processing_time": i * 0.1,
                "confidence_scores": {
                    "thai_id": 0.95 + (i % 5) * 0.01,
                    "name": 0.87 + (i % 8) * 0.01,
                    "phone": 0.92 + (i % 3) * 0.01,
                },
            }
            for i in range(100)  # 100 patient records
        }

        # Measure exception creation time
        start_time = time.time()

        for i in range(100):
            exception = OCRParsingException(
                message=f"Failed to parse medical document batch {i}",
                raw_response="x" * 1000,  # 1KB response
                context=large_context,
            )

            # Test dictionary conversion
            exception_dict = exception.to_dict()
            assert len(exception_dict) > 0

        total_time = time.time() - start_time
        avg_time = total_time / 100

        # Exception creation should be fast (< 10ms per exception)
        assert (
            avg_time < 0.01
        ), f"Exception creation too slow: {avg_time:.4f}s per exception"
        print(f"Exception creation: {avg_time*1000:.2f}ms per exception")

    def test_exception_handling_performance_integration(self):
        """Test performance of exception handling utilities."""
        import time

        mock_logger = MagicMock()

        # Create realistic exceptions
        exceptions = [
            OCRAPIException("API error", status_code=429),
            PIIPatternException("Pattern error", pattern=r"complex_pattern"),
            PDFObfuscationException("Obfuscation error", obfuscation_method="BLUR"),
            FileAccessException("Access error", file_path="/hospital/records/"),
            EncryptionException("Encryption error", operation="encrypt_pii"),
        ]

        # Measure exception handling performance
        start_time = time.time()

        for _ in range(200):  # Handle 200 exceptions
            for exception in exceptions:
                # Handle exception
                result = handle_exception(
                    exception=exception, logger=mock_logger, reraise=False
                )

                # Get API response
                api_response = get_error_response(result)
                assert api_response["success"] is False

        total_time = time.time() - start_time
        operations = 200 * len(exceptions)
        avg_time = total_time / operations

        # Exception handling should be fast (< 1ms per operation)
        assert (
            avg_time < 0.001
        ), f"Exception handling too slow: {avg_time:.6f}s per operation"
        print(f"Exception handling: {avg_time*1000:.3f}ms per operation")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
