"""Real Gemini API integration tests - NO MOCKS.

These tests make actual API calls to Google Gemini 2.5 Pro API using real PDF samples.
All tests validate real-world performance, accuracy, and error handling.
"""

import asyncio
import time
from pathlib import Path
from typing import List

import pytest

from src.core.models import OCRResult
from src.processing.ocr_processor import GeminiOCRProcessor


@pytest.mark.integration
@pytest.mark.asyncio
class TestRealGeminiIntegration:
    """Real Gemini API integration test suite."""

    async def test_integration_pdf_ocr_with_thai_content(
        self, real_ocr_processor: GeminiOCRProcessor, sample_thai_medical_pdf: Path
    ):
        """Test OCR processing with real Thai medical PDF.

        Validates:
        - Successful API call to Gemini 2.5 Pro
        - Thai text extraction accuracy
        - Structured medical data extraction
        - Response time performance
        - Confidence scoring accuracy
        """
        start_time = time.time()

        # Process real PDF with actual API call
        result = await real_ocr_processor.process_pdf_with_retry(
            sample_thai_medical_pdf
        )

        processing_time = time.time() - start_time

        # Validate successful processing
        assert isinstance(result, OCRResult), "Should return OCRResult object"
        assert (
            not result.errors
        ), f"Processing should succeed without errors: {result.errors}"
        assert result.full_text, "Should extract text content from PDF"
        assert (
            processing_time < 60
        ), f"Processing should complete within 60 seconds, took {processing_time:.2f}s"

        # Validate Thai content detection
        assert result.detected_languages, "Should detect document languages"

        # Thai text validation (check for Thai Unicode characters)
        thai_unicode_range = any(
            "\u0e00" <= char <= "\u0e7f" for char in result.full_text
        )
        if thai_unicode_range:
            assert "thai" in [
                lang.lower() for lang in result.detected_languages
            ], "Should detect Thai language when Thai characters present"

        # Validate confidence scoring
        assert (
            0.0 <= result.confidence_score <= 1.0
        ), f"Confidence score should be between 0.0 and 1.0, got {result.confidence_score}"
        assert (
            result.confidence_score > 0.3
        ), f"Confidence score should be reasonable for medical documents, got {result.confidence_score}"

        # Validate processing metadata
        assert result.processing_time > 0, "Should track processing time"
        assert result.page_count > 0, "Should count PDF pages"

        print(f"✓ Successfully processed {sample_thai_medical_pdf.name}")
        print(f"  Processing time: {processing_time:.2f}s")
        print(f"  Confidence score: {result.confidence_score:.2f}")
        print(f"  Text length: {len(result.full_text)} characters")
        print(f"  Languages detected: {result.detected_languages}")

    async def test_integration_medical_data_extraction(
        self, real_ocr_processor: GeminiOCRProcessor, sample_thai_medical_pdf: Path
    ):
        """Test extraction of structured medical data from real PDF.

        Validates:
        - Patient identification extraction
        - Medical terminology recognition
        - Hospital/lab number extraction
        - Diagnosis and medication extraction
        """
        result = await real_ocr_processor.process_pdf_with_retry(
            sample_thai_medical_pdf
        )

        assert (
            not result.errors
        ), f"Medical data extraction should succeed: {result.errors}"

        # Check for medical data structures (at least one should be present)
        medical_data_found = any(
            [
                result.patient_name,
                result.thai_id,
                result.hospital_number,
                result.lab_number,
                result.diagnoses,
                result.medications,
                result.test_results,
            ]
        )

        assert (
            medical_data_found
        ), "Should extract at least some structured medical data"

        # Validate extracted identifiers format if present
        if result.thai_id:
            # Thai ID should be 13 digits (with or without dashes)
            thai_id_digits = "".join(filter(str.isdigit, result.thai_id))
            assert (
                len(thai_id_digits) == 13
            ), f"Thai ID should have 13 digits, got {len(thai_id_digits)} in '{result.thai_id}'"

        if result.hospital_number:
            # Hospital number should contain digits
            assert any(
                char.isdigit() for char in result.hospital_number
            ), f"Hospital number should contain digits: '{result.hospital_number}'"

        if result.lab_number:
            # Lab number should contain alphanumeric characters
            assert any(
                char.isalnum() for char in result.lab_number
            ), f"Lab number should contain alphanumeric characters: '{result.lab_number}'"

        print(f"✓ Extracted medical data from {sample_thai_medical_pdf.name}")
        print(f"  Patient name: {'✓' if result.patient_name else '✗'}")
        print(f"  Thai ID: {'✓' if result.thai_id else '✗'}")
        print(f"  Hospital number: {'✓' if result.hospital_number else '✗'}")
        print(f"  Lab number: {'✓' if result.lab_number else '✗'}")
        print(f"  Diagnoses: {len(result.diagnoses)} found")
        print(f"  Medications: {len(result.medications)} found")

    async def test_integration_api_error_handling(
        self, real_ocr_processor: GeminiOCRProcessor
    ):
        """Test error handling with real API error conditions.

        Validates:
        - Invalid file handling
        - Large file rejection
        - Network error recovery
        - Rate limit handling
        """
        # Test with non-existent file
        non_existent_file = Path("non_existent_file.pdf")
        result = await real_ocr_processor.process_pdf_with_retry(non_existent_file)

        assert result.errors, "Should report errors for non-existent file"
        assert (
            result.confidence_score == 0.0
        ), "Should have zero confidence for failed processing"
        assert not result.full_text, "Should have empty text for failed processing"

        print("✓ Properly handles non-existent file errors")

    async def test_integration_rate_limit_handling(
        self, real_ocr_processor: GeminiOCRProcessor, real_pdf_samples: List[Path]
    ):
        """Test rate limit handling with real API quotas.

        Validates:
        - Respect for API rate limits
        - Proper retry behavior
        - Graceful degradation
        - Error recovery
        """
        # Use first 3 samples to test concurrent processing within rate limits
        test_samples = real_pdf_samples[:3]

        # Process concurrently to potentially hit rate limits
        tasks = []
        for pdf_path in test_samples:
            task = real_ocr_processor.process_pdf_with_retry(pdf_path)
            tasks.append(task)

        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time

        # Validate results
        successful_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"✗ Exception processing {test_samples[i].name}: {result}")
            elif isinstance(result, OCRResult):
                if result.errors:
                    print(
                        f"? Errors processing {test_samples[i].name}: {result.errors}"
                    )
                else:
                    successful_results.append(result)
                    print(f"✓ Successfully processed {test_samples[i].name}")

        # Should have at least some successful results
        assert (
            len(successful_results) > 0
        ), "At least one PDF should process successfully"

        # Rate limiting should prevent instant completion
        expected_min_time = len(test_samples) * 2  # At least 2 seconds per request
        if total_time < expected_min_time:
            print(
                f"⚠ Processing completed very quickly ({total_time:.2f}s), rate limiting may not be active"
            )

        print(
            f"✓ Processed {len(successful_results)}/{len(test_samples)} files in {total_time:.2f}s"
        )

    async def test_integration_performance_benchmarks(
        self,
        real_ocr_processor: GeminiOCRProcessor,
        performance_test_samples: List[Path],
    ):
        """Test performance with real PDFs against SLA requirements.

        Validates:
        - Processing time < 30 seconds per PDF (95th percentile)
        - Memory usage stays reasonable
        - Throughput meets requirements
        - Response time consistency
        """
        processing_times = []
        confidence_scores = []

        for pdf_path in performance_test_samples:
            start_time = time.time()
            result = await real_ocr_processor.process_pdf_with_retry(pdf_path)
            processing_time = time.time() - start_time

            if not result.errors:
                processing_times.append(processing_time)
                confidence_scores.append(result.confidence_score)

                print(
                    f"✓ {pdf_path.name}: {processing_time:.2f}s (confidence: {result.confidence_score:.2f})"
                )
            else:
                print(f"✗ {pdf_path.name}: Failed with errors: {result.errors}")

        if processing_times:
            avg_time = sum(processing_times) / len(processing_times)
            max_time = max(processing_times)
            min_time = min(processing_times)

            # Performance validation
            assert (
                max_time < 60
            ), f"Maximum processing time should be < 60s, got {max_time:.2f}s"
            assert (
                avg_time < 30
            ), f"Average processing time should be < 30s, got {avg_time:.2f}s"

            # Confidence validation
            avg_confidence = sum(confidence_scores) / len(confidence_scores)
            assert (
                avg_confidence > 0.5
            ), f"Average confidence should be > 0.5, got {avg_confidence:.2f}"

            print(f"✓ Performance benchmarks:")
            print(f"  Average time: {avg_time:.2f}s")
            print(f"  Max time: {max_time:.2f}s")
            print(f"  Min time: {min_time:.2f}s")
            print(f"  Average confidence: {avg_confidence:.2f}")
        else:
            pytest.fail("No successful processing results for performance benchmarking")

    async def test_integration_confidence_scoring_accuracy(
        self, real_ocr_processor: GeminiOCRProcessor, sample_thai_medical_pdf: Path
    ):
        """Test confidence scoring accuracy with real document.

        Validates:
        - Confidence score correlation with text quality
        - Reasonable confidence ranges
        - Consistency across multiple runs
        """
        # Process the same document multiple times
        results = []
        for i in range(3):
            result = await real_ocr_processor.process_pdf_with_retry(
                sample_thai_medical_pdf
            )
            if not result.errors:
                results.append(result)
                print(f"Run {i+1}: Confidence {result.confidence_score:.3f}")

        assert (
            len(results) >= 2
        ), "Should have at least 2 successful runs for consistency testing"

        # Check confidence score consistency (should be relatively stable)
        confidence_scores = [r.confidence_score for r in results]
        avg_confidence = sum(confidence_scores) / len(confidence_scores)
        max_deviation = max(abs(score - avg_confidence) for score in confidence_scores)

        assert (
            max_deviation < 0.2
        ), f"Confidence scores should be consistent (max deviation {max_deviation:.3f})"

        # Check reasonable confidence range for medical documents
        assert all(
            0.3 <= score <= 1.0 for score in confidence_scores
        ), f"Confidence scores should be reasonable for medical documents: {confidence_scores}"

        print(f"✓ Confidence scoring consistency validated")
        print(f"  Average confidence: {avg_confidence:.3f}")
        print(f"  Max deviation: {max_deviation:.3f}")
        print(f"  Range: {min(confidence_scores):.3f} - {max(confidence_scores):.3f}")

    async def test_integration_large_file_handling(
        self, real_ocr_processor: GeminiOCRProcessor, real_pdf_samples: List[Path]
    ):
        """Test handling of large PDF files.

        Validates:
        - Large file processing (up to size limits)
        - Memory usage optimization
        - Timeout handling
        - Error reporting for oversized files
        """
        # Find the largest PDF sample
        largest_pdf = max(real_pdf_samples, key=lambda p: p.stat().st_size)
        file_size_mb = largest_pdf.stat().st_size / (1024 * 1024)

        print(f"Testing with largest sample: {largest_pdf.name} ({file_size_mb:.1f}MB)")

        if file_size_mb > 50:
            # Should reject files over 50MB limit
            result = await real_ocr_processor.process_pdf_with_retry(largest_pdf)
            assert result.errors, f"Should reject files over 50MB limit"
            print(f"✓ Properly rejected oversized file ({file_size_mb:.1f}MB)")
        else:
            # Should process files within limit
            start_time = time.time()
            result = await real_ocr_processor.process_pdf_with_retry(largest_pdf)
            processing_time = time.time() - start_time

            if result.errors:
                print(f"? Large file processing had errors: {result.errors}")
            else:
                assert result.full_text, "Should extract text from large file"
                assert (
                    processing_time < 120
                ), f"Large file processing should complete within 2 minutes"
                print(f"✓ Successfully processed large file in {processing_time:.2f}s")

    @pytest.mark.slow
    async def test_integration_concurrent_api_limits(
        self, real_ocr_processor: GeminiOCRProcessor, real_pdf_samples: List[Path]
    ):
        """Test concurrent processing with real API limits.

        Validates:
        - Proper semaphore-based rate limiting
        - No API quota violations
        - Graceful handling of rate limit responses
        - Performance under concurrent load
        """
        # Test with up to 10 concurrent requests (within typical limits)
        test_samples = real_pdf_samples[:10]

        print(f"Testing concurrent processing with {len(test_samples)} files")

        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(5)  # Conservative limit

        async def process_with_limit(pdf_path: Path):
            async with semaphore:
                return await real_ocr_processor.process_pdf_with_retry(pdf_path)

        start_time = time.time()
        results = await asyncio.gather(
            *[process_with_limit(pdf) for pdf in test_samples], return_exceptions=True
        )
        total_time = time.time() - start_time

        # Analyze results
        successful = 0
        failed = 0
        rate_limited = 0

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"✗ Exception: {test_samples[i].name} - {result}")
                failed += 1
            elif isinstance(result, OCRResult):
                if result.errors:
                    error_text = " ".join(result.errors).lower()
                    if (
                        "rate" in error_text
                        or "quota" in error_text
                        or "429" in error_text
                    ):
                        rate_limited += 1
                        print(f"⚠ Rate limited: {test_samples[i].name}")
                    else:
                        failed += 1
                        print(f"✗ Failed: {test_samples[i].name} - {result.errors}")
                else:
                    successful += 1
                    print(f"✓ Success: {test_samples[i].name}")

        print(f"\nConcurrent processing results:")
        print(f"  Successful: {successful}")
        print(f"  Failed: {failed}")
        print(f"  Rate limited: {rate_limited}")
        print(f"  Total time: {total_time:.2f}s")
        print(f"  Average per file: {total_time/len(test_samples):.2f}s")

        # Should have at least 50% success rate
        success_rate = successful / len(test_samples)
        assert (
            success_rate >= 0.5
        ), f"Success rate should be >= 50%, got {success_rate:.1%}"
