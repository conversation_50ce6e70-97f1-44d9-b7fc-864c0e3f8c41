"""Comprehensive unit tests for BatchProcessor with real data and integrations.

Tests the BatchProcessor class with real PDF files, concurrent processing,
progress tracking, error handling, and performance validation.
"""

import asyncio
import json
import logging
import os
import time
import tracemalloc
from pathlib import Path
from typing import Any, Dict, List
from unittest.mock import AsyncMock, Mock, patch

import pytest

from src.core.exceptions import OCRProcessingException
from src.core.models import OCRResult
from src.processing.batch_processor import (BatchProcessingStats,
                                            BatchProcessor,
                                            ConsoleProgressCallback,
                                            ProcessingResult, ProcessingStatus,
                                            ProgressCallback)
from src.processing.ocr_processor import GeminiOCRProcessor
from src.processing.pdf_obfuscator import PDFObfuscator
from src.processing.pii_detector import PIIDetector, PIIMatch

logger = logging.getLogger(__name__)


class MockProgressCallback(ProgressCallback):
    """Mock progress callback for capturing progress events."""

    def __init__(self):
        self.started_files = []
        self.completed_files = []
        self.batch_completed = False
        self.completion_stats = None

    def on_file_started(self, file_path: Path, current: int, total: int):
        self.started_files.append(
            {
                "file_path": file_path,
                "current": current,
                "total": total,
                "timestamp": time.time(),
            }
        )

    def on_file_completed(self, result: ProcessingResult, current: int, total: int):
        self.completed_files.append(
            {
                "result": result,
                "current": current,
                "total": total,
                "timestamp": time.time(),
            }
        )

    def on_batch_completed(self, stats: BatchProcessingStats):
        self.batch_completed = True
        self.completion_stats = stats


class MemoryMonitor:
    """Memory usage monitoring for batch processing tests."""

    def __init__(self):
        self.initial_memory = None
        self.peak_memory = None
        self.final_memory = None
        tracemalloc.start()

    def start(self):
        """Start memory monitoring."""
        self.initial_memory = tracemalloc.get_traced_memory()[0]
        logger.info(f"Initial memory usage: {self.initial_memory / 1024 / 1024:.2f} MB")

    def update_peak(self):
        """Update peak memory usage."""
        current, peak = tracemalloc.get_traced_memory()
        if self.peak_memory is None or peak > self.peak_memory:
            self.peak_memory = peak

    def stop(self):
        """Stop memory monitoring and record final usage."""
        self.final_memory = tracemalloc.get_traced_memory()[0]
        tracemalloc.stop()
        logger.info(f"Final memory usage: {self.final_memory / 1024 / 1024:.2f} MB")
        if self.peak_memory:
            logger.info(f"Peak memory usage: {self.peak_memory / 1024 / 1024:.2f} MB")

    def get_memory_increase(self) -> float:
        """Get memory increase in MB."""
        if self.initial_memory and self.final_memory:
            return (self.final_memory - self.initial_memory) / 1024 / 1024
        return 0.0


@pytest.mark.integration
@pytest.mark.asyncio
class TestBatchProcessorReal:
    """Comprehensive tests for BatchProcessor with real data."""

    async def test_process_batch_single_file_success(
        self,
        real_batch_processor: BatchProcessor,
        sample_thai_medical_pdf: Path,
        temp_output_dir: Path,
    ):
        """Test successful processing of a single PDF file."""
        # Arrange
        input_files = [sample_thai_medical_pdf]
        progress_callback = MockProgressCallback()
        real_batch_processor.progress_callback = progress_callback

        # Act
        result = await real_batch_processor.process_batch(
            input_files, temp_output_dir, enable_obfuscation=True, save_report=True
        )

        # Assert
        assert isinstance(result, dict)
        assert "batch_stats" in result
        assert "processing_results" in result
        assert "total_batch_time" in result

        # Check batch stats
        stats = result["batch_stats"]
        assert stats.total_files == 1
        assert stats.completed_files >= 0  # May fail due to API issues
        assert stats.failed_files >= 0
        assert stats.completed_files + stats.failed_files == 1

        # Check processing results
        processing_results = result["processing_results"]
        assert len(processing_results) == 1

        result_dict = processing_results[0]
        assert result_dict["file_path"] == str(sample_thai_medical_pdf)
        assert result_dict["status"] in [
            ProcessingStatus.COMPLETED.value,
            ProcessingStatus.FAILED.value,
        ]
        assert "processing_time" in result_dict
        assert result_dict["processing_time"] > 0

        # Check progress tracking
        assert len(progress_callback.started_files) == 1
        assert len(progress_callback.completed_files) == 1
        assert progress_callback.batch_completed is True

        # Check report file creation
        report_files = list(temp_output_dir.glob("batch_report_*.json"))
        assert len(report_files) == 1

        # Validate report content
        with open(report_files[0], "r", encoding="utf-8") as f:
            saved_report = json.load(f)
        assert "batch_stats" in saved_report
        assert "configuration" in saved_report
        assert "timestamp" in saved_report

    async def test_process_batch_multiple_files_concurrent(
        self,
        real_batch_processor: BatchProcessor,
        performance_test_samples: List[Path],
        temp_output_dir: Path,
    ):
        """Test concurrent processing of multiple PDF files."""
        # Arrange
        input_files = performance_test_samples[:3]  # Use first 3 files
        progress_callback = MockProgressCallback()
        real_batch_processor.progress_callback = progress_callback

        start_time = time.time()

        # Act
        result = await real_batch_processor.process_batch(
            input_files, temp_output_dir, enable_obfuscation=True, save_report=True
        )

        total_time = time.time() - start_time

        # Assert
        assert isinstance(result, dict)

        # Check batch stats
        stats = result["batch_stats"]
        assert stats.total_files == len(input_files)
        assert stats.completed_files + stats.failed_files == len(input_files)

        # Check processing results
        processing_results = result["processing_results"]
        assert len(processing_results) == len(input_files)

        # Verify all files were processed
        processed_files = {r["file_path"] for r in processing_results}
        expected_files = {str(p) for p in input_files}
        assert processed_files == expected_files

        # Check progress tracking
        assert len(progress_callback.started_files) == len(input_files)
        assert len(progress_callback.completed_files) == len(input_files)
        assert progress_callback.batch_completed is True

        # Verify concurrent processing (should be faster than sequential)
        # Note: This is a rough estimate, actual timing may vary
        estimated_sequential_time = len(input_files) * 70  # 70 seconds per file (realistic with API calls and retries)
        assert (
            total_time < estimated_sequential_time * 0.9
        )  # Should be at least 10% faster than sequential

        logger.info(f"Processed {len(input_files)} files in {total_time:.2f}s")

    @pytest.mark.performance
    async def test_batch_processing_performance_metrics(
        self,
        real_batch_processor: BatchProcessor,
        performance_test_samples: List[Path],
        temp_output_dir: Path,
    ):
        """Test performance metrics and resource usage during batch processing."""
        # Arrange
        input_files = performance_test_samples
        memory_monitor = MemoryMonitor()
        progress_callback = MockProgressCallback()
        real_batch_processor.progress_callback = progress_callback

        # Start monitoring
        memory_monitor.start()
        start_time = time.time()

        # Act
        result = await real_batch_processor.process_batch(
            input_files, temp_output_dir, enable_obfuscation=True, save_report=True
        )

        # Stop monitoring
        end_time = time.time()
        memory_monitor.stop()

        # Assert performance metrics
        total_time = end_time - start_time
        stats = result["batch_stats"]

        # Performance assertions
        assert total_time > 0
        assert stats.total_files == len(input_files)

        if stats.completed_files > 0:
            avg_time_per_file = stats.average_processing_time
            assert avg_time_per_file > 0
            assert (
                avg_time_per_file < 120
            )  # Should not take more than 2 minutes per file

            logger.info(f"Average processing time per file: {avg_time_per_file:.2f}s")

        # Memory usage validation
        memory_increase = memory_monitor.get_memory_increase()
        logger.info(
            f"Memory increase during batch processing: {memory_increase:.2f} MB"
        )

        # Memory should not increase dramatically (less than 100MB per file)
        max_expected_memory = len(input_files) * 100  # 100MB per file
        assert memory_increase < max_expected_memory

        # Check report metrics
        assert "total_batch_time" in result
        assert result["total_batch_time"] == pytest.approx(total_time, rel=0.1)

    async def test_batch_processing_with_failures(
        self, real_batch_processor: BatchProcessor, temp_output_dir: Path
    ):
        """Test batch processing with mix of valid and invalid files."""
        # Arrange
        valid_file = temp_output_dir / "valid.pdf"
        invalid_file = temp_output_dir / "invalid.txt"
        nonexistent_file = temp_output_dir / "nonexistent.pdf"

        # Create a valid PDF file (minimal PDF structure)
        with open(valid_file, "wb") as f:
            f.write(
                b"%PDF-1.4\n1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj\n"
                b"2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj\n"
                b"3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]>>endobj\n"
                b"xref\n0 4\n0000000000 65535 f \n0000000010 00000 n \n"
                b"0000000053 00000 n \n0000000103 00000 n \n"
                b"trailer<</Size 4/Root 1 0 R>>\nstartxref\n162\n%%EOF"
            )

        # Create an invalid file
        with open(invalid_file, "w") as f:
            f.write("This is not a PDF file")

        input_files = [valid_file, invalid_file, nonexistent_file]
        progress_callback = MockProgressCallback()
        real_batch_processor.progress_callback = progress_callback

        # Act
        result = await real_batch_processor.process_batch(
            input_files, temp_output_dir, enable_obfuscation=True, save_report=True
        )

        # Assert
        stats = result["batch_stats"]

        # Only valid PDF should be processed
        assert stats.total_files == 1  # Only valid.pdf should pass validation

        # Check that invalid files were filtered out during validation
        processing_results = result["processing_results"]
        assert len(processing_results) == 1
        assert processing_results[0]["file_path"] == str(valid_file)

        # Progress callback should only track valid files
        assert len(progress_callback.started_files) == 1
        assert len(progress_callback.completed_files) == 1

    async def test_partial_batch_failure_recovery(
        self,
        real_batch_processor: BatchProcessor,
        performance_test_samples: List[Path],
        temp_output_dir: Path,
    ):
        """Test batch processing with partial failures and recovery."""
        # Arrange
        input_files = performance_test_samples[:3]
        progress_callback = MockProgressCallback()
        real_batch_processor.progress_callback = progress_callback

        # Mock OCR processor to fail on second file
        original_process_pdf = real_batch_processor.ocr_processor.process_pdf_with_retry

        async def mock_process_pdf_with_retry(file_path: Path):
            if "TT03919" in str(file_path):  # Fail on specific file
                raise OCRProcessingException(
                    message="Simulated OCR failure", error_code="TEST_001"
                )
            return await original_process_pdf(file_path)

        with patch.object(
            real_batch_processor.ocr_processor,
            "process_pdf_with_retry",
            side_effect=mock_process_pdf_with_retry,
        ):
            # Act
            result = await real_batch_processor.process_batch(
                input_files, temp_output_dir, enable_obfuscation=True, save_report=True
            )

        # Assert
        stats = result["batch_stats"]
        assert stats.total_files == len(input_files)
        assert stats.completed_files + stats.failed_files == len(input_files)
        assert stats.failed_files >= 1  # At least one failure

        # Check processing results
        processing_results = result["processing_results"]
        assert len(processing_results) == len(input_files)

        # Find the failed result
        failed_results = [
            r
            for r in processing_results
            if r["status"] == ProcessingStatus.FAILED.value
        ]
        assert len(failed_results) >= 1

        # Check error tracking
        assert "error_message" in failed_results[0]
        assert failed_results[0]["error_message"] is not None

        # Verify other files still processed
        completed_results = [
            r
            for r in processing_results
            if r["status"] == ProcessingStatus.COMPLETED.value
        ]
        assert len(completed_results) >= 0  # May be 0 if all fail due to API issues

    async def test_progress_tracking_validation(
        self,
        real_batch_processor: BatchProcessor,
        performance_test_samples: List[Path],
        temp_output_dir: Path,
    ):
        """Test detailed progress tracking and reporting."""
        # Arrange
        input_files = performance_test_samples[:3]
        progress_callback = MockProgressCallback()
        real_batch_processor.progress_callback = progress_callback

        # Act
        result = await real_batch_processor.process_batch(
            input_files, temp_output_dir, enable_obfuscation=True, save_report=True
        )

        # Assert progress tracking
        assert len(progress_callback.started_files) == len(input_files)
        assert len(progress_callback.completed_files) == len(input_files)

        # Check file start events
        for i, started_event in enumerate(progress_callback.started_files):
            assert started_event["current"] == i + 1
            assert started_event["total"] == len(input_files)
            assert started_event["file_path"] in input_files
            assert "timestamp" in started_event

        # Check file completion events
        for i, completed_event in enumerate(progress_callback.completed_files):
            assert completed_event["current"] == i + 1
            assert completed_event["total"] == len(input_files)
            assert isinstance(completed_event["result"], ProcessingResult)
            assert "timestamp" in completed_event

        # Check batch completion
        assert progress_callback.batch_completed is True
        assert isinstance(progress_callback.completion_stats, BatchProcessingStats)
        assert progress_callback.completion_stats.total_files == len(input_files)

        # Verify chronological order
        start_times = [event["timestamp"] for event in progress_callback.started_files]
        completion_times = [
            event["timestamp"] for event in progress_callback.completed_files
        ]

        # All start times should be before corresponding completion times
        for start_time, completion_time in zip(start_times, completion_times):
            assert start_time <= completion_time

    @pytest.mark.slow
    async def test_large_batch_memory_management(
        self,
        real_batch_processor: BatchProcessor,
        real_pdf_samples: List[Path],
        temp_output_dir: Path,
    ):
        """Test memory management during processing of large batches."""
        # Arrange
        # Use all available samples (but limit to first 10 for test speed)
        input_files = real_pdf_samples[: min(10, len(real_pdf_samples))]
        memory_monitor = MemoryMonitor()
        progress_callback = MockProgressCallback()
        real_batch_processor.progress_callback = progress_callback

        # Start monitoring
        memory_monitor.start()

        # Act
        result = await real_batch_processor.process_batch(
            input_files, temp_output_dir, enable_obfuscation=True, save_report=True
        )

        # Stop monitoring
        memory_monitor.stop()

        # Assert
        stats = result["batch_stats"]
        assert stats.total_files == len(input_files)

        # Check memory usage
        memory_increase = memory_monitor.get_memory_increase()
        logger.info(
            f"Memory increase for {len(input_files)} files: {memory_increase:.2f} MB"
        )

        # Memory increase should be reasonable (less than 200MB total)
        assert memory_increase < 200

        # Check that all files were processed (completed or failed)
        assert stats.completed_files + stats.failed_files == len(input_files)

        # Verify progress tracking
        assert len(progress_callback.started_files) == len(input_files)
        assert len(progress_callback.completed_files) == len(input_files)

    async def test_error_aggregation_and_reporting(
        self,
        real_batch_processor: BatchProcessor,
        performance_test_samples: List[Path],
        temp_output_dir: Path,
    ):
        """Test error aggregation and categorization in batch processing."""
        # Arrange
        input_files = performance_test_samples[:3]
        progress_callback = MockProgressCallback()
        real_batch_processor.progress_callback = progress_callback

        # Mock different types of failures
        original_process_pdf = real_batch_processor.ocr_processor.process_pdf_with_retry

        call_count = 0

        async def mock_process_pdf_with_retry(file_path: Path):
            nonlocal call_count
            call_count += 1

            if call_count == 1:
                raise OCRProcessingException(
                    message="API quota exceeded", error_code="API_001"
                )
            elif call_count == 2:
                raise OCRProcessingException(
                    message="File format not supported", error_code="FORMAT_001"
                )
            else:
                return await original_process_pdf(file_path)

        with patch.object(
            real_batch_processor.ocr_processor,
            "process_pdf_with_retry",
            side_effect=mock_process_pdf_with_retry,
        ):
            # Act
            result = await real_batch_processor.process_batch(
                input_files, temp_output_dir, enable_obfuscation=True, save_report=True
            )

        # Assert
        stats = result["batch_stats"]

        # Check error categorization
        assert stats.failed_files >= 2  # At least 2 should fail
        assert len(stats.error_types) >= 1  # Should have error categories

        # Verify error types are categorized
        if "API Error" in stats.error_types:
            assert stats.error_types["API Error"] >= 1
        if "PDF Format Error" in stats.error_types:
            assert stats.error_types["PDF Format Error"] >= 1

        # Check processing results contain error information
        processing_results = result["processing_results"]
        failed_results = [
            r
            for r in processing_results
            if r["status"] == ProcessingStatus.FAILED.value
        ]

        for failed_result in failed_results:
            assert "error_message" in failed_result
            assert failed_result["error_message"] is not None
            assert failed_result["processing_time"] > 0

    async def test_concurrent_processing_limits(
        self, performance_test_samples: List[Path], temp_output_dir: Path
    ):
        """Test concurrent processing with different concurrency limits."""
        # Test with different concurrency settings
        concurrency_levels = [1, 2, 5]
        results = {}

        for max_concurrent in concurrency_levels:
            # Create processor with specific concurrency limit
            processor = BatchProcessor()
            processor.semaphore = asyncio.Semaphore(max_concurrent)

            progress_callback = MockProgressCallback()
            processor.progress_callback = progress_callback

            # Use subset of files for speed
            input_files = performance_test_samples[:3]

            start_time = time.time()

            # Process batch
            result = await processor.process_batch(
                input_files,
                temp_output_dir,
                enable_obfuscation=False,  # Disable for speed
                save_report=False,
            )

            end_time = time.time()

            results[max_concurrent] = {
                "time": end_time - start_time,
                "stats": result["batch_stats"],
            }

            logger.info(f"Concurrency {max_concurrent}: {end_time - start_time:.2f}s")

        # Assert
        # Higher concurrency should generally be faster (though not always due to API limits)
        # Just verify all processed the same number of files
        file_counts = [
            results[level]["stats"].total_files for level in concurrency_levels
        ]
        assert all(count == file_counts[0] for count in file_counts)

    async def test_directory_processing(
        self, real_batch_processor: BatchProcessor, temp_output_dir: Path
    ):
        """Test directory-based batch processing."""
        # Arrange
        samples_dir = Path(__file__).parent.parent / "original-pdf-examples"

        if not samples_dir.exists():
            pytest.skip("PDF samples directory not found")

        # Act
        result = await real_batch_processor.process_directory(
            samples_dir,
            temp_output_dir,
            pattern="*.pdf",
            recursive=False,
            enable_obfuscation=False,  # Disable for speed
        )

        # Assert
        assert isinstance(result, dict)
        assert "batch_stats" in result

        stats = result["batch_stats"]
        assert stats.total_files > 0

        # Check that files were found and processed
        pdf_files = list(samples_dir.glob("*.pdf"))
        assert stats.total_files == len(pdf_files)

    async def test_console_progress_callback(
        self,
        real_batch_processor: BatchProcessor,
        sample_thai_medical_pdf: Path,
        temp_output_dir: Path,
        caplog,
    ):
        """Test console progress callback logging."""
        # Arrange
        console_callback = ConsoleProgressCallback()
        real_batch_processor.progress_callback = console_callback

        input_files = [sample_thai_medical_pdf]

        with caplog.at_level(logging.INFO):
            # Act
            result = await real_batch_processor.process_batch(
                input_files,
                temp_output_dir,
                enable_obfuscation=False,
                save_report=False,
            )

        # Assert
        # Check that progress messages were logged
        log_messages = [record.message for record in caplog.records]

        # Should have start, completion, and batch completion messages
        start_messages = [msg for msg in log_messages if "Processing file" in msg]
        completion_messages = [
            msg for msg in log_messages if ("✓" in msg or "✗" in msg)
        ]
        batch_messages = [msg for msg in log_messages if "Batch completed" in msg]

        assert len(start_messages) >= 1
        assert len(completion_messages) >= 1
        assert len(batch_messages) >= 1

    async def test_multiple_upload_processing(
        self,
        real_batch_processor: BatchProcessor,
        sample_thai_medical_pdf: Path,
        temp_output_dir: Path,
    ):
        """Test processing of multiple uploaded files."""
        # Arrange
        # Read sample PDF content
        with open(sample_thai_medical_pdf, "rb") as f:
            pdf_content = f.read()

        uploaded_files = [
            (Path("upload1.pdf"), pdf_content),
            (Path("upload2.pdf"), pdf_content),
        ]

        # Act - Synchronous processing
        result = await real_batch_processor.process_multiple_uploads(
            uploaded_files,
            temp_output_dir,
            organization_id="test_org",
            enable_obfuscation=False,
            async_processing=False,
        )

        # Assert
        assert isinstance(result, dict)
        assert "batch_stats" in result

        stats = result["batch_stats"]
        assert stats.total_files == 2

    async def test_multiple_upload_async_processing(
        self,
        real_batch_processor: BatchProcessor,
        sample_thai_medical_pdf: Path,
        temp_output_dir: Path,
    ):
        """Test asynchronous processing of multiple uploaded files."""
        # Arrange
        with open(sample_thai_medical_pdf, "rb") as f:
            pdf_content = f.read()

        uploaded_files = [(Path("async_upload1.pdf"), pdf_content)]

        # Act - Asynchronous processing
        result = await real_batch_processor.process_multiple_uploads(
            uploaded_files,
            temp_output_dir,
            organization_id="test_org",
            enable_obfuscation=False,
            async_processing=True,
        )

        # Assert
        assert isinstance(result, dict)
        assert "job_id" in result
        assert "status" in result
        assert result["status"] == "processing"
        assert "total_files" in result
        assert result["total_files"] == 1
        assert "estimated_completion_time" in result

    async def test_batch_processor_initialization(self):
        """Test BatchProcessor initialization with different components."""
        # Test with default components
        processor1 = BatchProcessor()
        assert isinstance(processor1.ocr_processor, GeminiOCRProcessor)
        assert isinstance(processor1.pii_detector, PIIDetector)
        assert isinstance(processor1.pdf_obfuscator, PDFObfuscator)
        assert isinstance(processor1.progress_callback, ConsoleProgressCallback)

        # Test with custom components
        custom_callback = MockProgressCallback()
        processor2 = BatchProcessor(progress_callback=custom_callback)
        assert processor2.progress_callback is custom_callback

    async def test_input_file_validation(
        self, real_batch_processor: BatchProcessor, temp_output_dir: Path
    ):
        """Test input file validation logic."""
        # Create test files
        valid_pdf = temp_output_dir / "valid.pdf"
        invalid_txt = temp_output_dir / "invalid.txt"
        empty_pdf = temp_output_dir / "empty.pdf"
        nonexistent = temp_output_dir / "nonexistent.pdf"

        # Create valid PDF
        with open(valid_pdf, "wb") as f:
            f.write(b"%PDF-1.4\n%%EOF")

        # Create invalid file
        with open(invalid_txt, "w") as f:
            f.write("Not a PDF")

        # Create empty file
        empty_pdf.touch()

        input_files = [valid_pdf, invalid_txt, empty_pdf, nonexistent]

        # Test validation
        valid_files = real_batch_processor._validate_input_files(input_files)

        # Only valid PDF should pass
        assert len(valid_files) == 1
        assert valid_files[0] == valid_pdf

    @pytest.mark.security
    async def test_batch_processing_security_validation(
        self,
        real_batch_processor: BatchProcessor,
        sample_thai_medical_pdf: Path,
        temp_output_dir: Path,
    ):
        """Test security aspects of batch processing."""
        # Test that output files are created securely
        input_files = [sample_thai_medical_pdf]

        result = await real_batch_processor.process_batch(
            input_files, temp_output_dir, enable_obfuscation=True, save_report=True
        )

        # Check that report files don't contain sensitive information
        report_files = list(temp_output_dir.glob("batch_report_*.json"))
        if report_files:
            with open(report_files[0], "r", encoding="utf-8") as f:
                report_content = f.read()

            # Should not contain raw PII data
            assert "configuration" in report_content
            # The report should contain metadata but not raw extracted text
            assert "batch_stats" in report_content


# Synchronous tests (outside the async class)


@pytest.mark.integration
def test_processing_result_model():
    """Test ProcessingResult data model functionality."""
    # Arrange
    file_path = Path("/test/file.pdf")
    result = ProcessingResult(
        file_path=file_path, status=ProcessingStatus.PROCESSING, file_size=1024
    )

    # Test marking as completed
    start_time = time.time()
    result.mark_completed(5.5)

    assert result.status == ProcessingStatus.COMPLETED
    assert result.processing_time == 5.5
    assert result.end_time is not None
    assert result.end_time >= start_time

    # Test marking as failed
    result2 = ProcessingResult(file_path=file_path, status=ProcessingStatus.PROCESSING)

    start_time = time.time()
    result2.mark_failed("Test error")

    assert result2.status == ProcessingStatus.FAILED
    assert result2.error_message == "Test error"
    assert result2.end_time is not None
    assert result2.processing_time > 0

    # Test dictionary conversion
    result_dict = result.to_dict()
    assert result_dict["file_path"] == str(file_path)
    assert result_dict["status"] == ProcessingStatus.COMPLETED.value
    assert result_dict["processing_time"] == 5.5
    assert "file_size" in result_dict


@pytest.mark.integration
def test_batch_processing_stats_model():
    """Test BatchProcessingStats data model functionality."""
    # Arrange
    stats = BatchProcessingStats(total_files=3)

    # Create mock results
    completed_result = ProcessingResult(
        file_path=Path("/test/file1.pdf"),
        status=ProcessingStatus.COMPLETED,
        pii_matches=[Mock(), Mock()],  # 2 PII matches
        obfuscation_result={"success": True, "pii_items_obfuscated": 2},
    )
    completed_result.processing_time = 10.0

    failed_result = ProcessingResult(
        file_path=Path("/test/file2.pdf"),
        status=ProcessingStatus.FAILED,
        error_message="API quota exceeded",
    )

    skipped_result = ProcessingResult(
        file_path=Path("/test/file3.pdf"), status=ProcessingStatus.SKIPPED
    )

    # Act
    stats.update_from_result(completed_result)
    stats.update_from_result(failed_result)
    stats.update_from_result(skipped_result)

    # Assert
    assert stats.completed_files == 1
    assert stats.failed_files == 1
    assert stats.skipped_files == 1
    assert stats.total_processing_time == 10.0
    assert stats.average_processing_time == 10.0
    assert stats.total_pii_detected == 2
    assert stats.total_pii_obfuscated == 2

    # Check error categorization
    assert "API Error" in stats.error_types
    assert stats.error_types["API Error"] == 1


@pytest.mark.integration
def test_convenience_functions():
    """Test convenience functions for batch processing."""
    # These are simple wrapper functions, so we just test they exist and can be called
    from src.processing.batch_processor import (process_pdf_files,
                                                process_pdfs_from_directory)

    # Test that functions exist and are callable
    assert callable(process_pdfs_from_directory)
    assert callable(process_pdf_files)
