"""Comprehensive unit tests for PII encryption module using real data and integrations.

This test suite validates all functions in src/security/pii_encryption.py using:
- Real encryption keys and operations
- Real PII data (Thai medical information)
- Real file system operations for key storage
- Real threading scenarios for concurrency testing
- Real performance benchmarks
- Real security vulnerability testing
"""

import asyncio
import base64
import json
import os
import secrets
import tempfile
import threading
import time
from pathlib import Path
from typing import Any, Dict, List

import pytest
from cryptography.fernet import Fernet

from src.core.exceptions import EncryptionException
from src.security.pii_encryption import (EncryptedData, EncryptionType,
                                         KeyManager, PIIClassification,
                                         PIIEncryption, get_pii_encryption,
                                         initialize_pii_encryption)


class TestEncryptedData:
    """Test EncryptedData class with real data scenarios."""

    def test_encrypted_data_creation_integration(self):
        """Test EncryptedData creation with real Thai PII data."""
        # Real Thai National ID (anonymized but valid format)
        real_encrypted_value = "gAAAAABhZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5"
        real_key_id = "thai_medical_pii_2024"

        encrypted_data = EncryptedData(
            encrypted_value=real_encrypted_value,
            encryption_type=EncryptionType.SYMMETRIC_FERNET.value,
            key_id=real_key_id,
            classification=PIIClassification.RESTRICTED.value,
            metadata={
                "pii_type": "thai_national_id",
                "original_length": 13,
                "hospital_department": "cardiology",
                "processing_context": "medical_record_ocr",
            },
        )

        assert encrypted_data.encrypted_value == real_encrypted_value
        assert encrypted_data.encryption_type == EncryptionType.SYMMETRIC_FERNET.value
        assert encrypted_data.key_id == real_key_id
        assert encrypted_data.classification == PIIClassification.RESTRICTED.value
        assert encrypted_data.metadata["pii_type"] == "thai_national_id"
        assert encrypted_data.timestamp is not None

    def test_encrypted_data_serialization_integration(self):
        """Test serialization with real medical metadata."""
        real_metadata = {
            "pii_type": "thai_patient_name",
            "original_length": 25,
            "medical_record_number": "HN-2024-001234",
            "hospital_code": "TH-BKK-001",
            "department": "emergency",
            "physician_id": "DOC-001",
            "processing_timestamp": "2024-08-05T10:30:00Z",
        }

        encrypted_data = EncryptedData(
            encrypted_value="encrypted_thai_name_data",
            encryption_type=EncryptionType.SYMMETRIC_FERNET.value,
            key_id="hospital_pii_key",
            metadata=real_metadata,
        )

        # Test dictionary conversion
        data_dict = encrypted_data.to_dict()
        assert data_dict["metadata"] == real_metadata
        assert "medical_record_number" in data_dict["metadata"]

        # Test JSON serialization
        json_str = encrypted_data.to_json()
        parsed_data = json.loads(json_str)
        assert parsed_data["metadata"]["hospital_code"] == "TH-BKK-001"

        # Test deserialization
        recreated = EncryptedData.from_json(json_str)
        assert recreated.metadata["department"] == "emergency"
        assert recreated.key_id == "hospital_pii_key"

    def test_encrypted_data_edge_cases_integration(self):
        """Test edge cases with real problematic data."""
        # Test with Thai Unicode characters
        thai_metadata = {
            "pii_type": "thai_name",
            "original_text": "นายสมชาย ใจดี",
            "hospital_name": "โรงพยาบาลจุฬาลงกรณ์",
            "special_chars": "อ่าน/เขียน 100% ผู้ป่วย#123",
        }

        encrypted_data = EncryptedData(
            encrypted_value="thai_encrypted_content",
            encryption_type=EncryptionType.SYMMETRIC_FERNET.value,
            key_id="thai_unicode_key",
            metadata=thai_metadata,
        )

        # Ensure Thai characters are preserved
        json_str = encrypted_data.to_json()
        recreated = EncryptedData.from_json(json_str)
        assert recreated.metadata["hospital_name"] == "โรงพยาบาลจุฬาลงกรณ์"
        assert recreated.metadata["special_chars"] == "อ่าน/เขียน 100% ผู้ป่วย#123"


class TestKeyManager:
    """Test KeyManager class with real key operations and storage."""

    def test_key_manager_initialization_integration(self):
        """Test KeyManager initialization with real storage path."""
        with tempfile.TemporaryDirectory() as temp_dir:
            storage_path = Path(temp_dir) / "real_keys.dat"
            real_master_key = secrets.token_bytes(32)

            key_manager = KeyManager(
                key_storage_path=storage_path, master_key=real_master_key
            )

            assert key_manager.key_storage_path == storage_path
            assert key_manager.master_key == real_master_key
            assert len(key_manager._keys) == 0

    def test_generate_key_integration_fernet(self):
        """Test real Fernet key generation with hospital scenarios."""
        key_manager = KeyManager()

        # Generate keys for different hospital departments
        dept_keys = [
            "emergency_dept_2024",
            "icu_patient_data",
            "lab_results_pii",
            "radiology_reports",
            "pharmacy_prescriptions",
        ]

        for key_id in dept_keys:
            generated_key_id = key_manager.generate_key(key_id, "fernet")
            assert generated_key_id == key_id

            # Verify key is valid Fernet key
            key = key_manager.get_key(key_id)
            fernet = Fernet(key)  # Should not raise exception

            # Test key metadata
            metadata = key_manager._key_metadata[key_id]
            assert metadata["key_type"] == "fernet"
            assert metadata["active"] is True
            assert metadata["usage_count"] == 1  # get_key increments usage

    def test_generate_key_integration_aes(self):
        """Test real AES key generation for high-security scenarios."""
        key_manager = KeyManager()

        # Generate AES keys for high-security medical data
        security_scenarios = [
            ("patient_genetic_data", 32),
            ("mental_health_records", 24),
            ("hiv_test_results", 32),
            ("substance_abuse_records", 16),
        ]

        for key_id, key_size in security_scenarios:
            generated_key_id = key_manager.generate_key(key_id, "aes", key_size)
            assert generated_key_id == key_id

            key = key_manager.get_key(key_id)
            assert len(key) == key_size

            metadata = key_manager._key_metadata[key_id]
            assert metadata["key_type"] == "aes"
            assert metadata["key_size"] == key_size

    def test_key_rotation_integration_scenario(self):
        """Test key rotation in real hospital key management scenario."""
        key_manager = KeyManager()

        # Initial key for patient records
        old_key_id = "patient_records_2023"
        key_manager.generate_key(old_key_id, "fernet")

        # Simulate usage over time
        for _ in range(50):
            key_manager.get_key(old_key_id)

        old_usage_count = key_manager._key_metadata[old_key_id]["usage_count"]
        assert old_usage_count == 50

        # Rotate key for new year
        new_key_id = "patient_records_2024"
        rotated_key_id = key_manager.rotate_key(old_key_id, new_key_id)

        assert rotated_key_id == new_key_id

        # Verify old key is inactive
        assert key_manager._key_metadata[old_key_id]["active"] is False
        assert key_manager._key_metadata[old_key_id]["rotated_to"] == new_key_id

        # Verify new key is active
        assert key_manager._key_metadata[new_key_id]["active"] is True

        # Test that inactive key raises exception
        with pytest.raises(EncryptionException) as exc_info:
            key_manager.get_key(old_key_id)
        assert "inactive" in str(exc_info.value).lower()

    def test_key_storage_integration_persistence(self):
        """Test real key storage and loading from filesystem."""
        with tempfile.TemporaryDirectory() as temp_dir:
            storage_path = Path(temp_dir) / "hospital_keys.encrypted"
            master_key = secrets.token_bytes(32)

            # Create key manager and generate real keys
            key_manager1 = KeyManager(storage_path, master_key)

            real_keys = {
                "emergency_pii": "fernet",
                "lab_results": "aes",
                "patient_photos": "fernet",
                "insurance_data": "aes",
            }

            for key_id, key_type in real_keys.items():
                key_manager1.generate_key(key_id, key_type)

            # Verify storage file was created
            assert storage_path.exists()
            assert storage_path.stat().st_size > 0

            # Create new key manager with same storage
            key_manager2 = KeyManager(storage_path, master_key)

            # Verify all keys were loaded
            for key_id in real_keys.keys():
                key1 = key_manager1.get_key(key_id)
                key2 = key_manager2.get_key(key_id)
                assert key1 == key2

    def test_key_manager_concurrent_access_integration(self):
        """Test concurrent key access in real multi-threaded hospital system."""
        key_manager = KeyManager()

        # Generate key for concurrent access
        key_id = "concurrent_patient_data"
        key_manager.generate_key(key_id, "fernet")

        results = []
        errors = []

        def access_key_worker():
            """Worker function for concurrent key access."""
            try:
                for _ in range(100):
                    key = key_manager.get_key(key_id)
                    # Simulate real encryption operation
                    fernet = Fernet(key)
                    test_data = (
                        f"Patient-{threading.current_thread().ident}-{time.time()}"
                    )
                    encrypted = fernet.encrypt(test_data.encode())
                    decrypted = fernet.decrypt(encrypted).decode()
                    results.append(decrypted)
                    time.sleep(0.001)  # Simulate processing time
            except Exception as e:
                errors.append(str(e))

        # Start 10 concurrent threads
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=access_key_worker)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Verify no errors occurred
        assert len(errors) == 0, f"Concurrent access errors: {errors}"
        assert len(results) == 1000  # 10 threads * 100 operations each

        # Verify usage count was properly incremented
        final_usage = key_manager._key_metadata[key_id]["usage_count"]
        assert final_usage == 1000

    def test_key_manager_error_scenarios_integration(self):
        """Test real error scenarios in hospital key management."""
        key_manager = KeyManager()

        # Test duplicate key generation
        key_id = "duplicate_test_key"
        key_manager.generate_key(key_id, "fernet")

        with pytest.raises(EncryptionException) as exc_info:
            key_manager.generate_key(key_id, "fernet")
        assert "already exists" in str(exc_info.value)

        # Test getting non-existent key
        with pytest.raises(EncryptionException) as exc_info:
            key_manager.get_key("non_existent_key")
        assert "not found" in str(exc_info.value)

        # Test unsupported key type
        with pytest.raises(EncryptionException) as exc_info:
            key_manager.generate_key("bad_key", "unsupported_type")
        assert "unsupported" in str(exc_info.value).lower()

    def test_key_storage_corruption_integration(self):
        """Test handling of corrupted key storage files."""
        with tempfile.TemporaryDirectory() as temp_dir:
            storage_path = Path(temp_dir) / "corrupted_keys.dat"
            master_key = secrets.token_bytes(32)

            # Create corrupted file
            with open(storage_path, "wb") as f:
                f.write(b"corrupted_data_not_valid_fernet")

            # Should handle corruption gracefully
            with pytest.raises(EncryptionException) as exc_info:
                KeyManager(storage_path, master_key)
            assert "failed to load keys" in str(exc_info.value).lower()


class TestPIIEncryption:
    """Test PIIEncryption class with real Thai medical PII data."""

    def test_pii_encryption_initialization_integration(self):
        """Test PIIEncryption initialization with real key manager."""
        with tempfile.TemporaryDirectory() as temp_dir:
            storage_path = Path(temp_dir) / "hospital_pii_keys.dat"
            key_manager = KeyManager(storage_path)

            pii_encryption = PIIEncryption(
                key_manager=key_manager, default_key_id="hospital_default_2024"
            )

            assert pii_encryption.key_manager == key_manager
            assert pii_encryption.default_key_id == "hospital_default_2024"

            # Verify default key was created
            default_key = key_manager.get_key("hospital_default_2024")
            assert len(default_key) == 44  # Fernet key length

    def test_encrypt_thai_national_id_integration(self):
        """Test encrypting real Thai National ID format."""
        pii_encryption = PIIEncryption()

        # Real Thai National ID format (anonymized)
        thai_ids = [
            "1234567890123",  # Valid checksum format
            "9876543210987",  # Another valid format
            "1111111111116",  # Valid format with repeated digits
        ]

        for thai_id in thai_ids:
            encrypted_data = pii_encryption.encrypt_pii(
                pii_value=thai_id,
                pii_type="thai_national_id",
                classification=PIIClassification.RESTRICTED,
                user_id="doctor_001",
                session_id="medical_session_2024",
            )

            assert (
                encrypted_data.encryption_type == EncryptionType.SYMMETRIC_FERNET.value
            )
            assert encrypted_data.metadata["pii_type"] == "thai_national_id"
            assert encrypted_data.metadata["original_length"] == 13
            assert encrypted_data.classification == PIIClassification.RESTRICTED.value

            # Verify encrypted value is different from original
            assert encrypted_data.encrypted_value != thai_id

            # Verify we can decrypt back to original
            decrypted = pii_encryption.decrypt_pii(
                encrypted_data,
                user_id="doctor_001",
                session_id="medical_session_2024",
                access_reason="medical_diagnosis",
            )
            assert decrypted == thai_id

    def test_encrypt_thai_patient_names_integration(self):
        """Test encrypting real Thai patient names with Unicode."""
        pii_encryption = PIIEncryption()

        # Real Thai patient names (anonymized)
        thai_names = [
            "นายสมชาย ใจดี",
            "นางสาวมณี รักเรียน",
            "นายประสิทธิ์ วิทยาคม",
            "นางพิมพ์ใจ สุขใส",
            "นายอนุชา ดีมาก",
        ]

        for thai_name in thai_names:
            encrypted_data = pii_encryption.encrypt_pii(
                pii_value=thai_name,
                pii_type="thai_patient_name",
                classification=PIIClassification.CONFIDENTIAL,
                user_id="nurse_002",
                session_id="patient_registration_2024",
            )

            # Verify encryption succeeded
            assert encrypted_data.metadata["pii_type"] == "thai_patient_name"
            assert encrypted_data.metadata["original_length"] == len(thai_name)

            # Verify Thai Unicode is preserved after decryption
            decrypted = pii_encryption.decrypt_pii(
                encrypted_data, user_id="nurse_002", access_reason="patient_care"
            )
            assert decrypted == thai_name
            assert "นาย" in decrypted or "นาง" in decrypted

    def test_encrypt_medical_record_numbers_integration(self):
        """Test encrypting real medical record number formats."""
        pii_encryption = PIIEncryption()

        # Real hospital number formats used in Thailand
        medical_numbers = [
            "HN-2024-001234",
            "VN-LAB-2024-005678",
            "MR-BKK-2024-009876",
            "PT-ICU-***********",
            "RD-XR-20240805-5678",
        ]

        for med_number in medical_numbers:
            encrypted_data = pii_encryption.encrypt_pii(
                pii_value=med_number,
                pii_type="medical_record_number",
                classification=PIIClassification.CONFIDENTIAL,
                user_id="admin_003",
                session_id="record_processing_2024",
            )

            assert encrypted_data.metadata["pii_type"] == "medical_record_number"

            # Verify decryption accuracy
            decrypted = pii_encryption.decrypt_pii(encrypted_data)
            assert decrypted == med_number
            assert "-" in decrypted  # Verify format preserved

    def test_deterministic_hash_integration_scenarios(self):
        """Test deterministic hashing for searchable encryption with real data."""
        pii_encryption = PIIEncryption()

        # Real Thai phone numbers for searchable encryption
        phone_numbers = ["************", "02-555-1234", "************", "************"]

        hashed_phones = []

        for phone in phone_numbers:
            encrypted_data = pii_encryption.encrypt_pii(
                pii_value=phone,
                pii_type="thai_phone_number",
                encryption_type=EncryptionType.DETERMINISTIC_HASH,
                classification=PIIClassification.INTERNAL,
            )

            assert (
                encrypted_data.encryption_type
                == EncryptionType.DETERMINISTIC_HASH.value
            )
            assert encrypted_data.salt is not None

            # Same input should produce same hash with same salt
            encrypted_data2 = pii_encryption.encrypt_pii(
                pii_value=phone,
                pii_type="thai_phone_number",
                encryption_type=EncryptionType.DETERMINISTIC_HASH,
                classification=PIIClassification.INTERNAL,
            )

            # Different salt means different hash
            assert encrypted_data.encrypted_value != encrypted_data2.encrypted_value

            # But verification should work
            assert pii_encryption.verify_hash(phone, encrypted_data)
            assert pii_encryption.verify_hash(phone, encrypted_data2)

            hashed_phones.append(encrypted_data)

        # Verify different phones produce different hashes
        hash_values = [hp.encrypted_value for hp in hashed_phones]
        assert len(set(hash_values)) == len(hash_values)  # All unique

    def test_pii_encryption_performance_integration(self):
        """Test performance with real-world data volumes."""
        pii_encryption = PIIEncryption()

        # Simulate real hospital daily PII processing volume
        test_data = []
        for i in range(1000):
            test_data.extend(
                [
                    f"นายคนไข่ที่{i:04d} ใจดี",  # Thai names
                    f"12345678901{i%10}{i%3}",  # Thai IDs
                    f"HN-2024-{i:06d}",  # Hospital numbers
                    f"089-{i%900+100:03d}-{i%9000+1000:04d}",  # Phone numbers
                ]
            )

        start_time = time.time()
        encrypted_items = []

        for idx, pii_value in enumerate(test_data):
            pii_type = ["thai_name", "thai_id", "hospital_number", "phone"][idx % 4]

            encrypted_data = pii_encryption.encrypt_pii(
                pii_value=pii_value, pii_type=pii_type, user_id=f"user_{idx % 100}"
            )
            encrypted_items.append(encrypted_data)

        encryption_time = time.time() - start_time

        # Performance requirements: < 1ms per item for 4000 items
        assert (
            encryption_time < 4.0
        ), f"Encryption took {encryption_time:.2f}s for 4000 items"
        assert len(encrypted_items) == 4000

        # Test decryption performance
        start_time = time.time()
        decrypted_items = []

        for encrypted_data in encrypted_items[:100]:  # Sample 100 for decryption test
            decrypted = pii_encryption.decrypt_pii(encrypted_data)
            decrypted_items.append(decrypted)

        decryption_time = time.time() - start_time

        # Decryption should be similarly fast
        assert (
            decryption_time < 0.5
        ), f"Decryption took {decryption_time:.2f}s for 100 items"
        assert len(decrypted_items) == 100

    def test_pii_encryption_error_scenarios_integration(self):
        """Test real error scenarios in hospital PII encryption."""
        pii_encryption = PIIEncryption()

        # Test decryption with wrong key
        encrypted_data = pii_encryption.encrypt_pii(
            pii_value="นายทดสอบ ผิดพลาด", pii_type="thai_name"
        )

        # Corrupt the key_id
        encrypted_data.key_id = "non_existent_key"

        with pytest.raises(EncryptionException):
            pii_encryption.decrypt_pii(encrypted_data)

        # Test hash verification on non-hash data
        fernet_encrypted = pii_encryption.encrypt_pii(
            pii_value="************",
            pii_type="phone",
            encryption_type=EncryptionType.SYMMETRIC_FERNET,
        )

        with pytest.raises(EncryptionException) as exc_info:
            pii_encryption.verify_hash("************", fernet_encrypted)
        assert "deterministic hash" in str(exc_info.value).lower()

        # Test hash verification without salt
        hash_encrypted = pii_encryption.encrypt_pii(
            pii_value="************",
            pii_type="phone",
            encryption_type=EncryptionType.DETERMINISTIC_HASH,
        )
        hash_encrypted.salt = None

        with pytest.raises(EncryptionException) as exc_info:
            pii_encryption.verify_hash("************", hash_encrypted)
        assert "salt required" in str(exc_info.value).lower()

    def test_pii_encryption_concurrent_integration(self):
        """Test concurrent PII encryption in real hospital scenario."""
        pii_encryption = PIIEncryption()

        # Real concurrent scenario: multiple doctors accessing patient data
        patient_data = [
            ("นายสมชาย ใจดี", "thai_name"),
            ("1234567890123", "thai_id"),
            ("HN-2024-001234", "hospital_number"),
            ("************", "phone"),
            ("กรุงเทพมหานคร", "address"),
        ]

        results = []
        errors = []

        def encrypt_worker(worker_id):
            """Worker function for concurrent encryption."""
            try:
                for i, (pii_value, pii_type) in enumerate(patient_data):
                    encrypted = pii_encryption.encrypt_pii(
                        pii_value=pii_value,
                        pii_type=pii_type,
                        user_id=f"doctor_{worker_id}",
                        session_id=f"session_{worker_id}_{i}",
                    )

                    # Immediately decrypt to verify
                    decrypted = pii_encryption.decrypt_pii(
                        encrypted,
                        user_id=f"doctor_{worker_id}",
                        access_reason="concurrent_test",
                    )

                    results.append((pii_value, decrypted, worker_id))
                    time.sleep(0.01)  # Simulate processing delay
            except Exception as e:
                errors.append(f"Worker {worker_id}: {str(e)}")

        # Start 5 concurrent workers
        threads = []
        for worker_id in range(5):
            thread = threading.Thread(target=encrypt_worker, args=(worker_id,))
            threads.append(thread)
            thread.start()

        # Wait for completion
        for thread in threads:
            thread.join()

        # Verify no errors
        assert len(errors) == 0, f"Concurrent errors: {errors}"
        assert len(results) == 25  # 5 workers * 5 data items each

        # Verify all encryptions/decryptions were successful
        for original, decrypted, worker_id in results:
            assert original == decrypted, f"Data mismatch for worker {worker_id}"


class TestGlobalPIIEncryption:
    """Test global PII encryption functions with real scenarios."""

    def test_initialize_pii_encryption_integration(self):
        """Test global PII encryption initialization with real storage."""
        with tempfile.TemporaryDirectory() as temp_dir:
            storage_path = Path(temp_dir) / "global_hospital_keys.dat"
            master_key = secrets.token_bytes(32)

            # Initialize global encryption service
            encryption_service = initialize_pii_encryption(storage_path, master_key)

            assert encryption_service is not None
            assert encryption_service.key_manager.key_storage_path == storage_path
            assert encryption_service.key_manager.master_key == master_key

            # Test that global service is accessible
            global_service = get_pii_encryption()
            assert global_service == encryption_service

    def test_get_pii_encryption_default_integration(self):
        """Test getting PII encryption with default initialization."""
        # Reset global instance
        import src.security.pii_encryption as pii_module

        pii_module._pii_encryption = None

        # Should auto-initialize
        service = get_pii_encryption()
        assert service is not None

        # Test with real Thai medical data
        thai_patient = "นายทดสอบ ระบบเข้ารหัส"
        encrypted = service.encrypt_pii(
            pii_value=thai_patient, pii_type="thai_patient_name"
        )

        decrypted = service.decrypt_pii(encrypted)
        assert decrypted == thai_patient


@pytest.mark.integration
@pytest.mark.security
class TestPIIEncryptionSecurity:
    """Security-focused tests with real attack scenarios."""

    def test_encryption_key_security_integration(self):
        """Test encryption key security against real attack vectors."""
        key_manager = KeyManager()

        # Generate key for security testing
        key_id = "security_test_key"
        key_manager.generate_key(key_id, "fernet")
        key = key_manager.get_key(key_id)

        # Verify key is cryptographically strong
        assert len(key) == 44  # Fernet key length

        # Test key is not predictable
        keys = []
        for i in range(100):
            test_manager = KeyManager()
            test_manager.generate_key(f"test_key_{i}", "fernet")
            test_key = test_manager.get_key(f"test_key_{i}")
            keys.append(test_key)

        # All keys should be unique
        assert len(set(keys)) == 100

    def test_pii_data_security_integration(self):
        """Test PII data security against real threats."""
        pii_encryption = PIIEncryption()

        # Test sensitive Thai medical data
        sensitive_data = [
            "HIV ผลบวก",
            "โรคจิตเภท",
            "ติดยาเสพติด",
            "ความพิการทางสมอง",
            "โรคทางพันธุกรรม",
        ]

        for sensitive_info in sensitive_data:
            encrypted = pii_encryption.encrypt_pii(
                pii_value=sensitive_info,
                pii_type="sensitive_medical_diagnosis",
                classification=PIIClassification.RESTRICTED,
            )

            # Verify sensitive data is not present in encrypted form
            assert sensitive_info not in encrypted.encrypted_value
            assert sensitive_info not in encrypted.to_json()

            # Verify metadata doesn't leak sensitive info
            metadata_json = json.dumps(encrypted.metadata)
            assert sensitive_info not in metadata_json

    def test_timing_attack_resistance_integration(self):
        """Test resistance to timing attacks with real PII data."""
        pii_encryption = PIIEncryption()

        # Test data of different lengths
        test_data = [
            "A",  # 1 char
            "AB" * 10,  # 20 chars
            "ABC" * 100,  # 300 chars
            "นายทดสอบ" * 50,  # 500 Thai chars
            "X" * 1000,  # 1000 chars
        ]

        encryption_times = []

        for data in test_data:
            start_time = time.perf_counter()

            for _ in range(10):  # Multiple iterations for accuracy
                encrypted = pii_encryption.encrypt_pii(
                    pii_value=data, pii_type="timing_test"
                )

            end_time = time.perf_counter()
            avg_time = (end_time - start_time) / 10
            encryption_times.append(avg_time)

        # Encryption time should not vary significantly with input length
        # (Fernet adds its own padding, making timing more consistent)
        time_variance = max(encryption_times) - min(encryption_times)
        assert time_variance < 0.01, f"Timing variance too high: {time_variance:.4f}s"


@pytest.mark.performance
class TestPIIEncryptionPerformance:
    """Performance tests with real hospital data volumes."""

    def test_bulk_encryption_performance_integration(self):
        """Test performance with real hospital daily processing volume."""
        pii_encryption = PIIEncryption()

        # Simulate 10,000 patient records per day
        bulk_data = []
        for i in range(10000):
            bulk_data.extend(
                [
                    f"นายผู้ป่วย{i:05d} ทดสอบ",
                    f"12345678901{i%100:02d}",
                    f"HN-2024-{i:06d}",
                    f"089-{i%900+100:03d}-{i%9000+1000:04d}",
                ]
            )

        start_time = time.time()

        encrypted_count = 0
        for pii_value in bulk_data:
            encrypted = pii_encryption.encrypt_pii(
                pii_value=pii_value, pii_type="bulk_test"
            )
            encrypted_count += 1

            if encrypted_count % 1000 == 0:
                elapsed = time.time() - start_time
                rate = encrypted_count / elapsed
                print(f"Encrypted {encrypted_count} items at {rate:.0f} items/sec")

        total_time = time.time() - start_time
        final_rate = len(bulk_data) / total_time

        # Performance requirement: > 1000 encryptions/second
        assert (
            final_rate > 1000
        ), f"Encryption rate too slow: {final_rate:.0f} items/sec"
        print(f"Final rate: {final_rate:.0f} encryptions/second")

    def test_memory_usage_integration(self):
        """Test memory usage with large PII datasets."""
        import gc

        import psutil

        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        pii_encryption = PIIEncryption()

        # Create large dataset
        large_dataset = []
        for i in range(5000):
            large_dataset.append(
                f"นายผู้ป่วยหมายเลข{i:05d} ชื่อยาวมากๆ เพื่อทดสอบหน่วยความจำ" * 3
            )

        # Encrypt all data
        encrypted_data = []
        for pii_value in large_dataset:
            encrypted = pii_encryption.encrypt_pii(
                pii_value=pii_value, pii_type="memory_test"
            )
            encrypted_data.append(encrypted)

        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - initial_memory

        # Clean up
        del encrypted_data
        del large_dataset
        gc.collect()

        final_memory = process.memory_info().rss / 1024 / 1024  # MB

        # Memory increase should be reasonable (< 100MB for 5000 items)
        assert memory_increase < 100, f"Memory usage too high: {memory_increase:.1f}MB"

        # Memory should be mostly freed after cleanup
        memory_leak = final_memory - initial_memory
        assert memory_leak < 10, f"Potential memory leak: {memory_leak:.1f}MB"

        print(
            f"Memory usage: {memory_increase:.1f}MB peak, {memory_leak:.1f}MB after cleanup"
        )


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
