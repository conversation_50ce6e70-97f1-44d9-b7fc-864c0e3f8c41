"""Comprehensive unit tests for core configuration module using real data and integrations.

This test suite validates all functions in src/core/config.py using:
- Real environment variables and .env file integration
- Real directory creation and file system operations
- Real validation scenarios with Thai hospital configurations
- Real Gemini API key validation
- Real Supabase credentials validation
- Real performance benchmarks for configuration loading
"""

import os
import tempfile
import time
from pathlib import Path
from typing import Any, Dict
from unittest.mock import MagicMock, patch

import pytest
from pydantic import ValidationError

from src.core.config import (Settings, get_app_config, get_database_config,
                             get_file_config, get_gemini_config,
                             get_processing_config, settings)


class TestSettings:
    """Test Settings class with real configuration scenarios."""

    def test_settings_default_values_integration(self):
        """Test default settings values for real deployment scenarios."""
        # Create settings with minimal required fields
        with patch.dict(os.environ, {"GOOGLE_API_KEY": "test_integration_gemini_key_123"}):
            test_settings = Settings()

            # Verify default values for hospital deployment
            assert test_settings.chromoforge_version == "1.0.0"
            assert test_settings.environment == "development"
            assert test_settings.google_api_key == "test_integration_gemini_key_123"

            # OCR processing defaults suitable for hospital workloads
            assert test_settings.max_retries == 3
            assert test_settings.retry_delay == 1.0
            assert test_settings.batch_size == 5
            assert test_settings.max_concurrent_requests == 10

            # File processing defaults for medical documents
            assert test_settings.max_file_size_mb == 50
            assert test_settings.supported_formats == ["pdf"]
            assert test_settings.output_dir == Path("./processed")
            assert test_settings.temp_dir == Path("./temp")

            # PII detection defaults for Thai medical data
            assert test_settings.confidence_threshold == 0.7
            assert test_settings.enable_coordinate_tracking is True
            assert test_settings.obfuscation_method == "black_box"

            # Enhanced Gemini configuration
            assert test_settings.gemini_model == "gemini-2.0-flash-exp"
            assert test_settings.gemini_temperature == 0.1
            assert test_settings.gemini_max_tokens == 8192
            assert test_settings.enable_ultra_think is True

            # Thai-specific OCR settings
            assert test_settings.thai_cross_reference is True
            assert test_settings.contextual_name_mapping is True
            assert test_settings.medical_field_extraction is True

    def test_settings_environment_variables_integration(self):
        """Test settings with real environment variable configuration."""
        # Real hospital configuration via environment variables
        real_env_config = {
            "GOOGLE_API_KEY": "AIzaSyD-real-key-example-for-hospital",
            "NEXT_PUBLIC_SUPABASE_URL": "https://hospital-project.supabase.co",
            "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.hospital.anon.key",
            "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.hospital.service.key",
            "ENVIRONMENT": "production",
            "MAX_CONCURRENT_REQUESTS": "25",
            "CONFIDENCE_THRESHOLD": "0.85",
            "OBFUSCATION_METHOD": "blur",
            "GEMINI_MODEL": "gemini-2.0-flash-exp",
            "ENABLE_ULTRA_THINK": "true",
            "THAI_CROSS_REFERENCE": "true",
            "MAX_FILE_SIZE_MB": "100",
            "BATCH_SIZE": "10",
        }

        with patch.dict(os.environ, real_env_config):
            hospital_settings = Settings()

            assert (
                hospital_settings.google_api_key
                == "AIzaSyD-real-key-example-for-hospital"
            )
            assert (
                hospital_settings.supabase_url == "https://hospital-project.supabase.co"
            )
            assert hospital_settings.environment == "production"
            assert hospital_settings.max_concurrent_requests == 25
            assert hospital_settings.confidence_threshold == 0.85
            assert hospital_settings.obfuscation_method == "blur"
            assert hospital_settings.gemini_model == "gemini-2.0-flash-exp"
            assert hospital_settings.enable_ultra_think is True
            assert hospital_settings.thai_cross_reference is True
            assert hospital_settings.max_file_size_mb == 100
            assert hospital_settings.batch_size == 10

    def test_settings_env_file_integration(self):
        """Test settings loading from real .env file."""
        with tempfile.TemporaryDirectory() as temp_dir:
            env_file = Path(temp_dir) / ".env"

            # Create real hospital .env file
            env_content = """
# ChromoForge Hospital Configuration
GOOGLE_API_KEY=AIzaSyD-hospital-gemini-api-key-2024
NEXT_PUBLIC_SUPABASE_URL=https://chromoforge-hospital.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.anon.key
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.service.key

# Hospital Environment Configuration
ENVIRONMENT=production
CHROMOFORGE_VERSION=2.1.0

# OCR Processing Configuration for Hospital Workloads
MAX_RETRIES=5
RETRY_DELAY=2.0
BATCH_SIZE=8
MAX_CONCURRENT_REQUESTS=20
MAX_FILE_SIZE_MB=75

# Thai Medical PII Detection Configuration
CONFIDENCE_THRESHOLD=0.80
OBFUSCATION_METHOD=black_box
ENABLE_COORDINATE_TRACKING=true

# Enhanced Gemini Configuration for Medical Documents
GEMINI_MODEL=gemini-2.0-flash-exp
GEMINI_TEMPERATURE=0.05
GEMINI_MAX_TOKENS=16384
ENABLE_ULTRA_THINK=true
ENABLE_GOOGLE_SEARCH=false
THAI_CROSS_REFERENCE=true
CONTEXTUAL_NAME_MAPPING=true
MEDICAL_FIELD_EXTRACTION=true

# Database Configuration
ENABLE_DATABASE_RECORDING=true
DEFAULT_ORGANIZATION_ID=hospital-bangkok-001

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
            """

            with open(env_file, "w", encoding="utf-8") as f:
                f.write(env_content.strip())

            # Load settings from .env file
            with patch(
                "src.core.config.Settings.model_config", {"env_file": str(env_file)}
            ):
                env_settings = Settings(_env_file=str(env_file))

                assert (
                    env_settings.google_api_key
                    == "AIzaSyD-hospital-gemini-api-key-2024"
                )
                assert (
                    env_settings.supabase_url
                    == "https://chromoforge-hospital.supabase.co"
                )
                assert env_settings.environment == "production"
                assert env_settings.chromoforge_version == "2.1.0"
                assert env_settings.max_retries == 5
                assert env_settings.confidence_threshold == 0.80
                assert env_settings.gemini_temperature == 0.05
                assert env_settings.gemini_max_tokens == 16384
                assert env_settings.enable_ultra_think is True
                assert env_settings.thai_cross_reference is True
                assert env_settings.default_organization_id == "hospital-bangkok-001"

    def test_directory_creation_integration(self):
        """Test real directory creation for output and temp directories."""
        with tempfile.TemporaryDirectory() as temp_base:
            temp_base_path = Path(temp_base)

            # Test with nested directory paths
            output_dir = temp_base_path / "hospital_output" / "processed_documents"
            temp_dir = temp_base_path / "hospital_temp" / "processing_cache"

            env_config = {
                "GOOGLE_API_KEY": "test_key_for_directory_creation",
                "OUTPUT_DIR": str(output_dir),
                "TEMP_DIR": str(temp_dir),
            }

            with patch.dict(os.environ, env_config):
                dir_settings = Settings()

                # Verify directories were created
                assert dir_settings.output_dir.exists()
                assert dir_settings.temp_dir.exists()
                assert dir_settings.output_dir.is_dir()
                assert dir_settings.temp_dir.is_dir()

                # Verify paths are correct
                assert dir_settings.output_dir == output_dir
                assert dir_settings.temp_dir == temp_dir

                # Test writing to created directories
                test_file = dir_settings.output_dir / "test_hospital_document.txt"
                test_file.write_text("Test hospital document content", encoding="utf-8")
                assert test_file.exists()
                assert (
                    test_file.read_text(encoding="utf-8")
                    == "Test hospital document content"
                )

    def test_supported_formats_validation_integration(self):
        """Test supported formats validation with real medical document formats."""
        # Valid medical document formats
        valid_format_configs = [
            "pdf",
            "pdf,jpg,png",
            "pdf, jpeg, png, tiff",
            "PDF,JPG,PNG,TIFF",  # Case insensitive
            " pdf , jpg , png ",  # With spaces
        ]

        for format_config in valid_format_configs:
            env_config = {
                "GOOGLE_API_KEY": "test_key_formats",
                "SUPPORTED_FORMATS": format_config,
            }

            with patch.dict(os.environ, env_config):
                format_settings = Settings()

                # Verify formats are parsed correctly
                assert isinstance(format_settings.supported_formats, list)
                for fmt in format_settings.supported_formats:
                    assert fmt in {"pdf", "jpg", "jpeg", "png", "tiff"}

        # Test invalid formats
        invalid_formats = ["pdf,doc", "xls,pdf", "pdf,invalid_format"]

        for invalid_format in invalid_formats:
            env_config = {
                "GOOGLE_API_KEY": "test_key_invalid",
                "SUPPORTED_FORMATS": invalid_format,
            }

            with patch.dict(os.environ, env_config):
                with pytest.raises(ValidationError) as exc_info:
                    Settings()
                assert "Unsupported format" in str(exc_info.value)

    def test_environment_validation_integration(self):
        """Test environment validation with real deployment scenarios."""
        # Valid environments
        valid_environments = [
            "development",
            "test",
            "production",
            "PRODUCTION",
            "Development",
        ]

        for env in valid_environments:
            env_config = {"GOOGLE_API_KEY": "test_key_env", "ENVIRONMENT": env}

            with patch.dict(os.environ, env_config):
                env_settings = Settings()
                assert env_settings.environment in {"development", "test", "production"}

        # Invalid environments
        invalid_environments = ["staging", "dev", "prod", "local", "invalid"]

        for invalid_env in invalid_environments:
            env_config = {
                "GOOGLE_API_KEY": "test_key_invalid_env",
                "ENVIRONMENT": invalid_env,
            }

            with patch.dict(os.environ, env_config):
                with pytest.raises(ValidationError) as exc_info:
                    Settings()
                assert "Invalid environment" in str(exc_info.value)

    def test_field_validation_ranges_integration(self):
        """Test field validation with real hospital operational ranges."""
        # Test valid ranges for hospital operations
        valid_configs = [
            {"MAX_RETRIES": "1", "expected": 1},
            {"MAX_RETRIES": "10", "expected": 10},
            {"RETRY_DELAY": "0.1", "expected": 0.1},
            {"RETRY_DELAY": "10.0", "expected": 10.0},
            {"BATCH_SIZE": "1", "expected": 1},
            {"BATCH_SIZE": "50", "expected": 50},
            {"MAX_CONCURRENT_REQUESTS": "1", "expected": 1},
            {"MAX_CONCURRENT_REQUESTS": "100", "expected": 100},
            {"MAX_FILE_SIZE_MB": "1", "expected": 1},
            {"MAX_FILE_SIZE_MB": "200", "expected": 200},
            {"CONFIDENCE_THRESHOLD": "0.0", "expected": 0.0},
            {"CONFIDENCE_THRESHOLD": "1.0", "expected": 1.0},
            {"GEMINI_TEMPERATURE": "0.0", "expected": 0.0},
            {"GEMINI_TEMPERATURE": "2.0", "expected": 2.0},
            {"GEMINI_MAX_TOKENS": "1024", "expected": 1024},
            {"GEMINI_MAX_TOKENS": "32768", "expected": 32768},
        ]

        for config in valid_configs:
            env_config = {
                "GOOGLE_API_KEY": "test_key_ranges",
                **{k: v for k, v in config.items() if k != "expected"},
            }

            with patch.dict(os.environ, env_config):
                range_settings = Settings()

                field_name = list(config.keys())[0].lower()
                expected_value = config["expected"]
                actual_value = getattr(range_settings, field_name)
                assert actual_value == expected_value

        # Test invalid ranges
        invalid_configs = [
            {"MAX_RETRIES": "0"},
            {"MAX_RETRIES": "11"},
            {"RETRY_DELAY": "0.05"},
            {"RETRY_DELAY": "11.0"},
            {"CONFIDENCE_THRESHOLD": "-0.1"},
            {"CONFIDENCE_THRESHOLD": "1.1"},
            {"GEMINI_TEMPERATURE": "-0.1"},
            {"GEMINI_TEMPERATURE": "2.1"},
        ]

        for invalid_config in invalid_configs:
            env_config = {"GOOGLE_API_KEY": "test_key_invalid_ranges", **invalid_config}

            with patch.dict(os.environ, env_config):
                with pytest.raises(ValidationError):
                    Settings()

    def test_obfuscation_method_validation_integration(self):
        """Test obfuscation method validation with real medical privacy methods."""
        # Valid obfuscation methods used in hospitals
        valid_methods = ["black_box", "blur", "redact"]

        for method in valid_methods:
            env_config = {
                "GOOGLE_API_KEY": "test_key_obfuscation",
                "OBFUSCATION_METHOD": method,
            }

            with patch.dict(os.environ, env_config):
                obf_settings = Settings()
                assert obf_settings.obfuscation_method == method

        # Invalid obfuscation methods
        invalid_methods = ["hide", "mask", "encrypt", "remove", "invalid"]

        for invalid_method in invalid_methods:
            env_config = {
                "GOOGLE_API_KEY": "test_key_invalid_obf",
                "OBFUSCATION_METHOD": invalid_method,
            }

            with patch.dict(os.environ, env_config):
                with pytest.raises(ValidationError) as exc_info:
                    Settings()
                assert "does not match" in str(exc_info.value).lower()

    def test_settings_missing_required_fields_integration(self):
        """Test settings validation with missing required fields."""
        # Clear GOOGLE_API_KEY to test required field validation
        env_without_api_key = {
            k: v for k, v in os.environ.items() if k != "GOOGLE_API_KEY"
        }

        with patch.dict(os.environ, env_without_api_key, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                Settings()

            error_message = str(exc_info.value)
            assert "google_api_key" in error_message.lower()
            assert "field required" in error_message.lower()

    def test_settings_thai_unicode_support_integration(self):
        """Test settings with Thai Unicode in paths and configurations."""
        with tempfile.TemporaryDirectory() as temp_base:
            # Create directories with Thai names
            thai_output_dir = Path(temp_base) / "โฟลเดอร์_ผลลัพธ์" / "เอกสาร_แพทย์"
            thai_temp_dir = Path(temp_base) / "โฟลเดอร์_ชั่วคราว" / "แคช_การประมวลผล"

            env_config = {
                "GOOGLE_API_KEY": "test_key_thai_unicode",
                "OUTPUT_DIR": str(thai_output_dir),
                "TEMP_DIR": str(thai_temp_dir),
                "DEFAULT_ORGANIZATION_ID": "โรงพยาบาล_จุฬาลงกรณ์_001",
            }

            with patch.dict(os.environ, env_config):
                thai_settings = Settings()

                # Verify Thai directories are created and accessible
                assert thai_settings.output_dir.exists()
                assert thai_settings.temp_dir.exists()
                assert "โฟลเดอร์_ผลลัพธ์" in str(thai_settings.output_dir)
                assert "แคช_การประมวลผล" in str(thai_settings.temp_dir)
                assert thai_settings.default_organization_id == "โรงพยาบาล_จุฬาลงกรณ์_001"

                # Test writing Thai content to these directories
                thai_test_file = thai_settings.output_dir / "ทดสอบ_เอกสาร_ไทย.txt"
                thai_content = "เนื้อหาทดสอบภาษาไทยในระบบ ChromoForge"
                thai_test_file.write_text(thai_content, encoding="utf-8")

                assert thai_test_file.exists()
                assert thai_test_file.read_text(encoding="utf-8") == thai_content


class TestConfigurationFunctions:
    """Test configuration helper functions with real data."""

    def test_get_gemini_config_integration(self):
        """Test Gemini configuration for real hospital deployment."""
        # Real hospital Gemini configuration
        env_config = {
            "GOOGLE_API_KEY": "AIzaSyD-hospital-production-key-2024",
            "GEMINI_MODEL": "gemini-2.0-flash-exp",
            "GEMINI_TEMPERATURE": "0.05",
            "GEMINI_MAX_TOKENS": "16384",
            "GEMINI_THINKING_BUDGET": "50000",
            "ENABLE_ULTRA_THINK": "true",
            "ENABLE_GOOGLE_SEARCH": "false",
            "ENABLE_URL_CONTEXT": "true",
            "THAI_CROSS_REFERENCE": "true",
            "CONTEXTUAL_NAME_MAPPING": "true",
            "MEDICAL_FIELD_EXTRACTION": "true",
        }

        with patch.dict(os.environ, env_config):
            # Force reload settings
            with patch("src.core.config.settings", Settings()):
                gemini_config = get_gemini_config()

                expected_config = {
                    "api_key": "AIzaSyD-hospital-production-key-2024",
                    "model": "gemini-2.0-flash-exp",
                    "temperature": 0.05,
                    "max_tokens": 16384,
                    "thinking_budget": 50000,
                    "enable_ultra_think": True,
                    "enable_google_search": False,
                    "enable_url_context": True,
                    "thai_cross_reference": True,
                    "contextual_name_mapping": True,
                    "medical_field_extraction": True,
                }

                for key, expected_value in expected_config.items():
                    assert gemini_config[key] == expected_value

    def test_get_processing_config_integration(self):
        """Test processing configuration for real hospital workloads."""
        env_config = {
            "GOOGLE_API_KEY": "test_key_processing",
            "MAX_RETRIES": "5",
            "RETRY_DELAY": "2.5",
            "BATCH_SIZE": "12",
            "MAX_CONCURRENT_REQUESTS": "25",
            "CONFIDENCE_THRESHOLD": "0.85",
        }

        with patch.dict(os.environ, env_config):
            with patch("src.core.config.settings", Settings()):
                processing_config = get_processing_config()

                expected_config = {
                    "max_retries": 5,
                    "retry_delay": 2.5,
                    "batch_size": 12,
                    "max_concurrent": 25,
                    "confidence_threshold": 0.85,
                }

                assert processing_config == expected_config

    def test_get_file_config_integration(self):
        """Test file configuration for real medical document processing."""
        with tempfile.TemporaryDirectory() as temp_base:
            output_dir = Path(temp_base) / "medical_documents_processed"
            temp_dir = Path(temp_base) / "document_processing_temp"

            env_config = {
                "GOOGLE_API_KEY": "test_key_file_config",
                "MAX_FILE_SIZE_MB": "100",
                "SUPPORTED_FORMATS": "pdf,jpg,png,tiff",
                "OUTPUT_DIR": str(output_dir),
                "TEMP_DIR": str(temp_dir),
                "OBFUSCATION_METHOD": "blur",
            }

            with patch.dict(os.environ, env_config):
                with patch("src.core.config.settings", Settings()):
                    file_config = get_file_config()

                    expected_config = {
                        "max_size_mb": 100,
                        "supported_formats": ["pdf", "jpg", "png", "tiff"],
                        "output_dir": output_dir,
                        "temp_dir": temp_dir,
                        "obfuscation_method": "blur",
                    }

                    assert file_config["max_size_mb"] == expected_config["max_size_mb"]
                    assert (
                        file_config["supported_formats"]
                        == expected_config["supported_formats"]
                    )
                    assert file_config["output_dir"] == expected_config["output_dir"]
                    assert file_config["temp_dir"] == expected_config["temp_dir"]
                    assert (
                        file_config["obfuscation_method"]
                        == expected_config["obfuscation_method"]
                    )

    def test_get_database_config_integration(self):
        """Test database configuration for real Supabase integration."""
        env_config = {
            "GOOGLE_API_KEY": "test_key_database",
            "NEXT_PUBLIC_SUPABASE_URL": "https://hospital-chromoforge.supabase.co",
            "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.anon.hospital",
            "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.service.hospital",
            "ENABLE_DATABASE_RECORDING": "true",
            "DEFAULT_ORGANIZATION_ID": "hospital-bangkok-central-001",
        }

        with patch.dict(os.environ, env_config):
            with patch("src.core.config.settings", Settings()):
                db_config = get_database_config()

                expected_config = {
                    "supabase_url": "https://hospital-chromoforge.supabase.co",
                    "supabase_anon_key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.anon.hospital",
                    "supabase_service_key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.service.hospital",
                    "enable_recording": True,
                    "default_organization_id": "hospital-bangkok-central-001",
                }

                assert db_config == expected_config

    def test_get_app_config_integration(self):
        """Test application configuration for real deployment."""
        env_config = {
            "GOOGLE_API_KEY": "test_key_app_config",
            "CHROMOFORGE_VERSION": "2.1.0-hospital-edition",
            "ENVIRONMENT": "production",
            "LOG_LEVEL": "WARNING",
            "LOG_FORMAT": "structured",
        }

        with patch.dict(os.environ, env_config):
            with patch("src.core.config.settings", Settings()):
                app_config = get_app_config()

                expected_config = {
                    "version": "2.1.0-hospital-edition",
                    "environment": "production",
                    "log_level": "WARNING",
                    "log_format": "structured",
                }

                assert app_config == expected_config


class TestGlobalSettingsInstance:
    """Test global settings instance with real scenarios."""

    def test_global_settings_initialization_integration(self):
        """Test global settings instance initialization."""
        # Ensure we have valid API key for global instance
        if not os.getenv("GOOGLE_API_KEY"):
            pytest.skip("GOOGLE_API_KEY not available for global settings test")

        # Global settings should be accessible
        assert settings is not None
        assert isinstance(settings, Settings)
        assert hasattr(settings, "google_api_key")
        assert hasattr(settings, "gemini_model")
        assert hasattr(settings, "thai_cross_reference")

    def test_global_settings_modification_integration(self):
        """Test runtime modification of global settings."""
        original_batch_size = settings.batch_size
        original_confidence = settings.confidence_threshold

        try:
            # Modify settings at runtime
            settings.batch_size = 15
            settings.confidence_threshold = 0.9

            assert settings.batch_size == 15
            assert settings.confidence_threshold == 0.9

            # Verify config functions use updated settings
            processing_config = get_processing_config()
            assert processing_config["batch_size"] == 15
            assert processing_config["confidence_threshold"] == 0.9

        finally:
            # Restore original values
            settings.batch_size = original_batch_size
            settings.confidence_threshold = original_confidence


@pytest.mark.integration
@pytest.mark.security
class TestConfigurationSecurity:
    """Security-focused configuration tests with real threat scenarios."""

    def test_sensitive_config_exposure_integration(self):
        """Test that sensitive configuration is not exposed inappropriately."""
        env_config = {
            "GOOGLE_API_KEY": "AIzaSyD-secret-hospital-key-do-not-log",
            "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.secret.service.key",
            "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.public.anon.key",
        }

        with patch.dict(os.environ, env_config):
            secure_settings = Settings()

            # Test string representation doesn't expose secrets
            settings_str = str(secure_settings)
            assert "AIzaSyD-secret-hospital-key" not in settings_str
            assert "secret.service.key" not in settings_str

            # Test dict representation via config functions
            gemini_config = get_gemini_config()
            database_config = get_database_config()

            # Keys should be accessible through proper channels
            assert gemini_config["api_key"] == "AIzaSyD-secret-hospital-key-do-not-log"
            assert (
                database_config["supabase_service_key"]
                == "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.secret.service.key"
            )

    def test_config_injection_prevention_integration(self):
        """Test prevention of configuration injection attacks."""
        # Attempt various injection patterns
        injection_attempts = [
            "normal_value; rm -rf /",
            "value$(malicious_command)",
            "value`malicious_command`",
            "value && malicious_command",
            "value | malicious_command",
            "value; DROP TABLE config;",
            "../../../etc/passwd",
            "file:///etc/passwd",
            "http://malicious.site/exploit",
        ]

        for injection_value in injection_attempts:
            env_config = {
                "GOOGLE_API_KEY": "test_key_injection_prevention",
                "DEFAULT_ORGANIZATION_ID": injection_value,
            }

            with patch.dict(os.environ, env_config):
                # Settings should load without executing injected code
                try:
                    injection_settings = Settings()
                    # Value should be stored as-is, not executed
                    assert injection_settings.default_organization_id == injection_value

                    # Verify no command execution occurred by checking the value is unchanged
                    db_config = get_database_config()
                    assert db_config["default_organization_id"] == injection_value

                except ValidationError:
                    # Some injection attempts might be caught by validation, which is good
                    pass

    def test_directory_traversal_prevention_integration(self):
        """Test prevention of directory traversal attacks in path configuration."""
        # Attempt directory traversal attacks
        traversal_attempts = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32",
            "/etc/shadow",
            "../../sensitive_directory",
            "~/../../root/.ssh",
            "%USERPROFILE%/../../../windows",
        ]

        for traversal_path in traversal_attempts:
            env_config = {
                "GOOGLE_API_KEY": "test_key_traversal_prevention",
                "OUTPUT_DIR": traversal_path,
                "TEMP_DIR": f"{traversal_path}_temp",
            }

            with patch.dict(os.environ, env_config):
                try:
                    traversal_settings = Settings()

                    # Verify paths are resolved safely
                    output_path = traversal_settings.output_dir
                    temp_path = traversal_settings.temp_dir

                    # Paths should be resolved but not allow access to system directories
                    assert output_path.exists()  # Directory creation should work
                    assert temp_path.exists()

                    # Verify we can't access sensitive system files
                    sensitive_files = ["passwd", "shadow", "system32", ".ssh"]
                    path_str = str(output_path).lower()

                    for sensitive in sensitive_files:
                        assert sensitive not in path_str

                except (ValidationError, PermissionError, OSError):
                    # Permission errors or validation errors are acceptable
                    pass


@pytest.mark.performance
class TestConfigurationPerformance:
    """Performance tests for configuration loading with real scenarios."""

    def test_settings_loading_performance_integration(self):
        """Test settings loading performance with realistic configuration."""
        # Large, realistic hospital configuration
        large_env_config = {
            "GOOGLE_API_KEY": "AIzaSyD-performance-test-key-" + "x" * 100,
            "NEXT_PUBLIC_SUPABASE_URL": "https://performance-test-hospital.supabase.co",
            "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
            + "x" * 500,
            "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
            + "x" * 500,
            "ENVIRONMENT": "production",
            "CHROMOFORGE_VERSION": "2.1.0-performance-test",
            "DEFAULT_ORGANIZATION_ID": "hospital-performance-test-bangkok-001-"
            + "x" * 50,
            "SUPPORTED_FORMATS": "pdf,jpg,jpeg,png,tiff,bmp,gif,webp",
            "MAX_RETRIES": "10",
            "RETRY_DELAY": "5.0",
            "BATCH_SIZE": "50",
            "MAX_CONCURRENT_REQUESTS": "100",
            "MAX_FILE_SIZE_MB": "200",
            "CONFIDENCE_THRESHOLD": "0.95",
            "GEMINI_MODEL": "gemini-2.0-flash-exp",
            "GEMINI_TEMPERATURE": "0.01",
            "GEMINI_MAX_TOKENS": "32768",
            "GEMINI_THINKING_BUDGET": "100000",
        }

        # Measure settings creation time
        start_time = time.time()

        with patch.dict(os.environ, large_env_config):
            for _ in range(100):  # Create settings 100 times
                Settings()

        total_time = time.time() - start_time
        avg_time = total_time / 100

        # Settings creation should be fast (< 10ms per instance)
        assert (
            avg_time < 0.01
        ), f"Settings creation too slow: {avg_time:.4f}s per instance"
        print(f"Settings creation: {avg_time*1000:.2f}ms per instance")

    def test_config_function_performance_integration(self):
        """Test configuration function performance with realistic usage."""
        env_config = {
            "GOOGLE_API_KEY": "test_key_config_perf",
            "GEMINI_MODEL": "gemini-2.0-flash-exp",
            "MAX_CONCURRENT_REQUESTS": "50",
            "CONFIDENCE_THRESHOLD": "0.8",
        }

        with patch.dict(os.environ, env_config):
            test_settings = Settings()

            with patch("src.core.config.settings", test_settings):
                # Measure config function performance
                functions_to_test = [
                    get_gemini_config,
                    get_processing_config,
                    get_file_config,
                    get_database_config,
                    get_app_config,
                ]

                for func in functions_to_test:
                    start_time = time.time()

                    for _ in range(1000):  # Call each function 1000 times
                        func()

                    total_time = time.time() - start_time
                    avg_time = total_time / 1000

                    # Config functions should be very fast (< 1ms per call)
                    assert (
                        avg_time < 0.001
                    ), f"{func.__name__} too slow: {avg_time:.6f}s per call"
                    print(f"{func.__name__}: {avg_time*1000:.3f}ms per call")

    def test_concurrent_settings_access_integration(self):
        """Test concurrent settings access performance."""
        import threading

        env_config = {
            "GOOGLE_API_KEY": "test_key_concurrent",
            "MAX_CONCURRENT_REQUESTS": "25",
            "BATCH_SIZE": "10",
        }

        with patch.dict(os.environ, env_config):
            test_settings = Settings()

            results = []
            errors = []

            def config_access_worker():
                """Worker function for concurrent config access."""
                try:
                    for _ in range(100):
                        # Access various config functions
                        gemini_cfg = get_gemini_config()
                        proc_cfg = get_processing_config()
                        file_cfg = get_file_config()

                        # Verify configs are consistent
                        assert gemini_cfg["api_key"] == "test_key_concurrent"
                        assert proc_cfg["max_concurrent"] == 25
                        assert proc_cfg["batch_size"] == 10

                        results.append(True)

                except Exception as e:
                    errors.append(str(e))

            # Start 10 concurrent threads
            threads = []
            start_time = time.time()

            for _ in range(10):
                thread = threading.Thread(target=config_access_worker)
                threads.append(thread)
                thread.start()

            # Wait for all threads
            for thread in threads:
                thread.join()

            total_time = time.time() - start_time

            # Verify no errors and good performance
            assert len(errors) == 0, f"Concurrent access errors: {errors}"
            assert len(results) == 1000  # 10 threads * 100 iterations each

            # Concurrent access should complete quickly
            assert total_time < 5.0, f"Concurrent access too slow: {total_time:.2f}s"
            print(f"Concurrent config access: {total_time:.2f}s for 1000 operations")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
