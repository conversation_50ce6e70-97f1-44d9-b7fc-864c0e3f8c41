#!/bin/bash
# ChromoForge Version Management Script
# Manages semantic versioning for the ChromoForge application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
VERSION_FILE="$PROJECT_ROOT/VERSION"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[VERSION]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to get current version
get_current_version() {
    if [ -f "$VERSION_FILE" ]; then
        cat "$VERSION_FILE"
    else
        echo "1.0.0"
    fi
}

# Function to validate semantic version format
validate_version() {
    local version="$1"
    if [[ ! $version =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        print_error "Invalid version format: $version"
        print_error "Version must follow semantic versioning (MAJOR.MINOR.PATCH)"
        return 1
    fi
    return 0
}

# Function to set version
set_version() {
    local new_version="$1"
    
    if ! validate_version "$new_version"; then
        return 1
    fi
    
    echo "$new_version" > "$VERSION_FILE"
    print_success "Version updated to: $new_version"
    
    # Update environment variable for Docker
    export CHROMOFORGE_VERSION="$new_version"
    print_status "Environment variable CHROMOFORGE_VERSION set to: $new_version"
}

# Function to bump version
bump_version() {
    local bump_type="$1"
    local current_version=$(get_current_version)
    
    # Parse current version
    IFS='.' read -r major minor patch <<< "$current_version"
    
    case "$bump_type" in
        "major")
            major=$((major + 1))
            minor=0
            patch=0
            ;;
        "minor")
            minor=$((minor + 1))
            patch=0
            ;;
        "patch")
            patch=$((patch + 1))
            ;;
        *)
            print_error "Invalid bump type: $bump_type"
            print_error "Valid types: major, minor, patch"
            return 1
            ;;
    esac
    
    local new_version="$major.$minor.$patch"
    set_version "$new_version"
}

# Function to show current version
show_version() {
    local current_version=$(get_current_version)
    print_status "Current ChromoForge version: $current_version"
    
    # Show Docker image tags that would be created
    print_status "Docker image tags:"
    echo "  - chromoforge:$current_version"
    echo "  - chromoforge:latest"
}

# Function to build Docker image with version tags
build_with_version() {
    local current_version=$(get_current_version)
    export CHROMOFORGE_VERSION="$current_version"
    
    print_status "Building Docker image with version: $current_version"
    
    cd "$PROJECT_ROOT"
    docker-compose build
    
    print_success "Docker image built with tags:"
    echo "  - chromoforge:$current_version"
    echo "  - chromoforge:latest"
}

# Function to show help
show_help() {
    echo "ChromoForge Version Management"
    echo ""
    echo "Usage: $0 <command> [arguments]"
    echo ""
    echo "Commands:"
    echo "  show                    Show current version"
    echo "  set <version>           Set specific version (e.g., 1.2.3)"
    echo "  bump <type>             Bump version (major|minor|patch)"
    echo "  build                   Build Docker image with current version"
    echo "  help                    Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 show"
    echo "  $0 set 1.2.3"
    echo "  $0 bump patch"
    echo "  $0 bump minor"
    echo "  $0 build"
}

# Main script logic
case "$1" in
    show)
        show_version
        ;;
    set)
        if [ -z "$2" ]; then
            print_error "Version required"
            echo "Usage: $0 set <version>"
            exit 1
        fi
        set_version "$2"
        ;;
    bump)
        if [ -z "$2" ]; then
            print_error "Bump type required"
            echo "Usage: $0 bump <major|minor|patch>"
            exit 1
        fi
        bump_version "$2"
        ;;
    build)
        build_with_version
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
