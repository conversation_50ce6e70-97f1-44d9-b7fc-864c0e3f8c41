# ChromoForge Project Overview

## Purpose
ChromoForge is a production-ready medical OCR pipeline application designed for processing Thai medical documents with advanced PII detection and obfuscation capabilities. The system extracts structured medical data while maintaining HIPAA compliance and data security.

## Tech Stack
- **Database**: Supabase (PostgreSQL with pgcrypto extension) - Optional via --database flag
- **Backend**: Python 3.12+ with type hints and async support
- **AI/ML**: Google Gemini 2.0 Flash API for OCR processing with ULTRA THINK mode
- **Security**: Row-level security (RLS), encrypted PII storage, audit logging
- **Storage**: Supabase Storage for document management (when database enabled)
- **Containerization**: Docker Desktop with docker-compose for development
- **Testing**: pytest with comprehensive coverage reporting

## Current State
- ✅ Fully implemented OCR pipeline with single entry point (`src/main.py`)
- ✅ Consolidated architecture with feature flags for enhanced functionality
- ✅ Docker-first development workflow with convenience scripts
- ✅ Comprehensive test suite (19 test modules) covering all functionality
- ✅ Production-ready with security, performance, and quality gates
- ✅ Enhanced medical data extraction with structured field parsing
- ✅ Multiple PDF obfuscation methods (black_box, blur, redact, white_box, hash_pattern)
- ✅ Optional database integration for persistent storage and tracking

## Key Features

### Core OCR Capabilities
- Mixed content OCR (handwritten, printed, Thai, English text)
- ULTRA THINK mode for enhanced accuracy (can be disabled for speed)
- Concurrent batch processing with configurable limits
- Circuit breakers and retry logic for reliability

### Enhanced Medical Extraction (--enhanced flag)
- Patient codes (TT prefix patterns: TT\d{5})
- Sample codes (6-character alphanumeric)
- Investigation types (K-TRACK, SPOT-MAS, K4CARE, NIPTSafe, GeneSAFE, etc.)
- Patient names (Thai and English with contextual detection)
- Date of birth (Gregorian YYYY-MM-DD and Buddhist Era DD/MM/YYYY formats)
- Contact information (Thai mobile numbers, addresses)
- Physician details and medical facility information

### PII Detection & Protection
- Thai National ID (13-digit with checksum validation)
- Hospital Numbers (HN patterns)
- Lab Numbers (VN patterns)
- Thai Names (with contextual detection)
- Phone Numbers (Thai mobile formats)
- Medical Record Numbers
- Multiple obfuscation methods with configurable confidence thresholds

### Database Integration (--database flag)
- Encrypted PII storage using pgcrypto with organization-specific keys
- Row-level security (RLS) policies for multi-tenant isolation
- Comprehensive audit logging for HIPAA compliance
- Soft deletes for data recovery
- Medical extractions table for structured data storage
- Document tracking with status management and metadata

## Architecture

### Project Structure
```
src/
├── main.py                 # Single consolidated entry point with CLI
├── core/                   # Core domain models and configuration
│   ├── config.py          # Pydantic settings management
│   ├── models.py          # Domain models (OCRResult, PIIMatch, etc.)
│   ├── constants.py       # Application constants
│   └── exceptions.py      # Custom exceptions
├── processing/            # Business logic layer
│   ├── ocr_processor.py   # Gemini OCR with ULTRA THINK
│   ├── pii_detector.py    # Thai PII detection patterns
│   ├── pdf_obfuscator.py  # PDF obfuscation methods
│   └── batch_processor.py # Batch and concurrent processing
├── security/              # Security and compliance
│   ├── pii_encryption.py  # PII encryption utilities
│   └── audit_logger.py    # HIPAA-compliant audit logging
├── utils/                 # Utilities and helpers
│   ├── logging_config.py  # Structured logging setup
│   └── utils.py          # Common utilities
└── database/              # Database integration (optional)
    ├── enhanced_schema.sql        # Database schema
    └── enhanced_medical_service.py # Supabase service layer
```

### Design Patterns
- **Single Entry Point**: All functionality accessible through src/main.py
- **Feature Flags**: Progressive enhancement via CLI flags
- **Dependency Injection**: Configuration-based service initialization
- **Repository Pattern**: Clean separation of data access logic
- **Circuit Breaker**: Automatic failure detection and recovery
- **Retry with Backoff**: Resilient API communication

## Development Approach

### Docker-First Development
- All development happens inside Docker containers
- No local Python environment required
- Consistent development environment across teams
- Easy onboarding with `./docker-run.sh` commands

### Quality Gates
1. **Code Formatting**: black (line-length 88)
2. **Import Sorting**: isort
3. **Linting**: flake8, pylint
4. **Type Checking**: mypy with strict mode
5. **Security Scanning**: bandit
6. **Test Coverage**: pytest with >80% coverage requirement
7. **Performance Tests**: Marked with @pytest.mark.performance
8. **Integration Tests**: Real API calls marked with @pytest.mark.integration

## Security Architecture
- **Defense in Depth**: Multiple security layers
- **Zero Trust**: Verify everything, trust nothing
- **Encryption at Rest**: All PII encrypted with pgcrypto
- **Audit Trail**: Immutable logging of all operations
- **Input Validation**: Strict validation at all entry points
- **Role-Based Access**: Granular permissions (admin, editor, viewer, analyst)

## Performance Optimizations
- **Concurrent Processing**: Configurable worker pool
- **Batch Operations**: Efficient multi-file processing
- **Caching**: Smart caching of OCR results
- **Resource Limits**: Memory and CPU constraints
- **Circuit Breakers**: Prevent cascading failures
- **Connection Pooling**: Efficient database connections

## Deployment & Operations
- **Container-Based**: Docker images with version tagging
- **Environment Config**: Single .env file approach
- **Health Checks**: Built-in monitoring endpoints
- **Logging**: Structured JSON logging for observability
- **Metrics**: Performance and error tracking
- **Graceful Shutdown**: Clean resource cleanup