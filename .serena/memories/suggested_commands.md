# Suggested Commands for ChromoForge Development

> **🐳 Docker-First Development**: ChromoForge uses Docker Desktop for containerized development. All commands should be run through Docker containers for consistency.

## Quick Start Commands

### Initial Setup
```bash
# Clone and setup
git clone <repository-url>
cd ChromoForge

# Copy environment template
cp .env.example .env
# Edit .env with your API keys

# Build Docker image
./docker-run.sh build

# Start container
./docker-run.sh start

# Verify setup
./docker-run.sh test
```

### Development Workflow

#### Accessing Development Environment
```bash
# Open shell in container for development
./docker-run.sh shell

# View container logs
./docker-run.sh logs

# Check container status
./docker-run.sh status
```

#### Inside Container Development Commands
```bash
# Once inside container via ./docker-run.sh shell

# Code quality checks
black src/ tests/ --line-length 88
isort src/ tests/
flake8 src/ tests/
mypy src/
bandit -r src/

# Run tests
pytest                    # All tests
pytest -v                 # Verbose output
pytest --cov=src          # With coverage
pytest -m integration     # Integration tests only
pytest -m "not slow"      # Skip slow tests

# Run specific test file
pytest tests/test_pii_detection_real.py -v
```

## OCR Pipeline Commands

### Basic Processing
```bash
# Process single file (from host)
./docker-run.sh process-file "original-pdf-examples/file.pdf"

# Process batch
./docker-run.sh process-batch

# Advanced processing (access shell first)
./docker-run.sh shell
# Then inside container:
python -m src.main --input file.pdf --output ./processed
```

### Enhanced Features
```bash
# Inside container (./docker-run.sh shell)

# Enable enhanced medical extraction
python -m src.main --input file.pdf --output ./processed --enhanced

# Enable database integration
python -m src.main --input file.pdf --output ./processed --database

# Combine features
python -m src.main --input file.pdf --output ./processed --enhanced --database

# Disable obfuscation (OCR only)
python -m src.main --input file.pdf --output ./processed --no-obfuscation
```

### Advanced Configuration
```bash
# Full featured processing with all options
python -m src.main --input ./documents --output ./processed \
  --enhanced \
  --database \
  --confidence-threshold 0.8 \
  --obfuscation-method blur \
  --max-concurrent 5 \
  --user-id "user-123" \
  --session-id "session-456" \
  --organization-id "org-789" \
  --verbose

# Directory processing with pattern
python -m src.main --input ./documents --output ./processed \
  --pattern "medical_*.pdf" \
  --no-recursive \
  --enhanced

# Performance mode (disable ULTRA THINK)
python -m src.main --input file.pdf --output ./processed \
  --disable-ultra-think \
  --max-concurrent 15
```

## CLI Flag Reference

### Required Arguments
- `--input, -i`: Input PDF file or directory
- `--output, -o`: Output directory for processed files

### Feature Flags
- `--enhanced`: Enable enhanced medical extraction
- `--database, --save-to-db`: Enable Supabase integration
- `--no-obfuscation`: OCR only, skip PII obfuscation

### Processing Options
- `--pattern`: File pattern (default: *.pdf)
- `--no-recursive`: Disable recursive scanning
- `--confidence-threshold`: PII threshold (0.0-1.0, default: 0.7)
- `--obfuscation-method`: Method choice (black_box|blur|redact|white_box|hash_pattern)
- `--max-concurrent`: Concurrent processing limit (default: 10)
- `--disable-ultra-think`: Disable enhanced OCR for speed

### Metadata Options
- `--organization-id`: Organization ID for multi-tenancy
- `--user-id`: User ID for audit logging
- `--session-id`: Session ID for tracking

### Utility Options
- `--verbose, -v`: Enable verbose logging
- `--version`: Show version information

## Database Commands

### Supabase Setup (if using --database)
```bash
# Initialize database schema
supabase db push

# Run migrations
supabase migration up

# Generate TypeScript types
supabase gen types typescript --local > types/database.types.ts

# View database logs
supabase logs --tail

# Reset local database
supabase db reset
```

### Database Queries (for debugging)
```sql
-- View recent documents
SELECT id, filename, status, created_at 
FROM documents 
ORDER BY created_at DESC 
LIMIT 10;

-- Check medical extractions
SELECT * FROM medical_extractions 
WHERE document_id = 'your-doc-id';

-- View audit logs
SELECT * FROM audit_logs 
WHERE table_name = 'documents' 
ORDER BY created_at DESC;
```

## Testing Commands

### Running Tests
```bash
# Inside container (./docker-run.sh shell)

# Full test suite with coverage
pytest --cov=src --cov-report=html

# Specific test categories
pytest -m integration        # Integration tests
pytest -m performance       # Performance tests
pytest -m security          # Security tests

# Test specific modules
pytest tests/test_ocr_processor_real.py
pytest tests/test_pii_detection_real.py
pytest tests/test_batch_processor_real.py

# Run tests in parallel
pytest -n auto              # Auto-detect CPU cores

# Generate coverage report
pytest --cov=src --cov-report=html
# View at htmlcov/index.html
```

### Quality Checks
```bash
# Inside container

# Format check
black src/ tests/ --check

# Import sorting check
isort src/ tests/ --check

# Linting
flake8 src/ tests/
pylint src/

# Type checking
mypy src/ --strict

# Security scanning
bandit -r src/ -f json
```

## Docker Management

### Container Operations
```bash
# Build image
./docker-run.sh build

# Start/stop container
./docker-run.sh start
./docker-run.sh stop

# View status
./docker-run.sh status

# Clean up everything
./docker-run.sh clean

# Show version
./docker-run.sh version
```

### Docker Compose Commands
```bash
# Manual compose operations
docker-compose up -d        # Start in background
docker-compose logs -f      # Follow logs
docker-compose down         # Stop and remove
docker-compose ps           # List containers
docker-compose exec chromoforge bash  # Shell access
```

## Development Workflows

### Feature Development Workflow
```bash
# 1. Start development environment
./docker-run.sh start
./docker-run.sh shell

# 2. Create feature branch
git checkout -b feature/new-feature

# 3. Make changes and test
# Edit files...
pytest tests/test_affected_module.py

# 4. Run quality checks
black src/ tests/
mypy src/
pytest

# 5. Commit changes
git add .
git commit -m "feat: add new feature"
```

### Debugging Workflow
```bash
# 1. Enable verbose logging
export LOG_LEVEL=DEBUG

# 2. Run with verbose flag
python -m src.main --input test.pdf --output ./debug --verbose

# 3. Check logs
tail -f logs/chromoforge.log

# 4. Use Python debugger
python -m pdb -m src.main --input test.pdf --output ./debug
```

### Performance Testing Workflow
```bash
# 1. Run performance benchmarks
pytest -m performance -v

# 2. Profile specific operation
python -m cProfile -o profile.stats -m src.main --input large.pdf --output ./perf

# 3. Analyze profile
python -m pstats profile.stats
```

## Troubleshooting Commands

### Common Issues
```bash
# Permission issues
chmod +x ./docker-run.sh

# Docker not running
docker info  # Check Docker status

# Container won't start
docker-compose logs  # Check error logs

# Test failures
pytest tests/test_setup.py  # Verify environment

# API key issues
echo $GOOGLE_API_KEY  # Verify env var
```

### Debugging Tools
```bash
# Inside container

# Interactive Python shell
python
>>> from src.processing.pii_detector import PIIDetector
>>> detector = PIIDetector()
>>> detector.detect_pii("Test text")

# Check installed packages
pip list

# Verify environment
python -c "import sys; print(sys.version)"
python -c "from src.core.config import get_settings; print(get_settings())"
```

## Git Commands

### Standard Git Workflow
```bash
# Status check
git status

# Stage changes
git add .
git add -p  # Interactive staging

# Commit with conventional commits
git commit -m "feat: add enhanced medical extraction"
git commit -m "fix: correct Thai ID validation"
git commit -m "docs: update API documentation"
git commit -m "test: add integration tests for OCR"
git commit -m "refactor: simplify PII detection logic"

# Push changes
git push origin feature/branch-name

# Create pull request
gh pr create --title "feat: add enhanced extraction" --body "Description..."
```

### Branch Management
```bash
# Create feature branch
git checkout -b feature/medical-extraction

# List branches
git branch -a

# Merge updates from main
git checkout main
git pull origin main
git checkout feature/branch
git merge main
```

## Production Commands

### Deployment Preparation
```bash
# Build production image
docker build -t chromoforge:prod --target production .

# Run security scan
docker scan chromoforge:prod

# Export image
docker save chromoforge:prod > chromoforge-prod.tar

# Load on production server
docker load < chromoforge-prod.tar
```

### Monitoring Commands
```bash
# Container health check
docker inspect chromoforge-app | jq '.[0].State.Health'

# Resource usage
docker stats chromoforge-app

# Application logs
docker logs chromoforge-app --tail 100 -f
```