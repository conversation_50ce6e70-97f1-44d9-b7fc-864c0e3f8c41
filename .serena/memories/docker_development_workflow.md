# Docker-First Development Workflow

## Overview
ChromoForge adopts a Docker-first development approach, ensuring consistent environments across all developers and eliminating "works on my machine" issues. All development, testing, and deployment operations occur within Docker containers.

## Key Benefits
1. **Zero Python Setup**: No local Python installation required
2. **Consistent Environment**: Same versions across all developers
3. **Isolated Dependencies**: No conflicts with other projects
4. **Easy Onboarding**: New developers productive in minutes
5. **Production Parity**: Development mirrors production environment

## Docker Architecture

### Container Structure
```
chromoforge:latest
├── Base: python:3.12-slim
├── System Dependencies
│   ├── libgl1-mesa-glx (OpenCV)
│   ├── libglib2.0-0 (Image processing)
│   └── libgomp1 (Performance)
├── Python Dependencies
│   ├── Core: pydantic, python-dotenv
│   ├── OCR: google-generativeai, PyPDF2
│   ├── Image: opencv-python, Pillow
│   ├── Database: supabase-py
│   └── Testing: pytest, pytest-cov
└── Application Code
    └── /app (working directory)
```

### Docker Compose Configuration
```yaml
services:
  chromoforge:
    image: chromoforge:${CHROMOFORGE_VERSION:-latest}
    container_name: chromoforge-app
    environment:
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - PYTHONUNBUFFERED=1
    volumes:
      - ./:/app
      - ./original-pdf-examples:/app/original-pdf-examples
    working_dir: /app
```

## Development Workflows

### 1. Initial Setup Workflow
```bash
# Step 1: Clone repository
git clone <repository>
cd ChromoForge

# Step 2: Configure environment
cp .env.example .env
# Edit .env with your API keys

# Step 3: Build container
./docker-run.sh build

# Step 4: Start development
./docker-run.sh start
./docker-run.sh shell
```

### 2. Daily Development Workflow
```bash
# Morning startup
./docker-run.sh start
./docker-run.sh shell

# Inside container:
# Run tests to verify environment
pytest tests/test_setup.py

# Start development work
# Edit files on host, changes reflected in container
# Run commands inside container shell
```

### 3. Testing Workflow
```bash
# Access container
./docker-run.sh shell

# Inside container:
# Run all tests
pytest

# Run specific tests
pytest tests/test_pii_detection_real.py -v

# Run with coverage
pytest --cov=src --cov-report=html

# Run quality checks
black src/ tests/
mypy src/
flake8 src/ tests/
```

### 4. OCR Processing Workflow
```bash
# Quick processing from host
./docker-run.sh process-file "path/to/file.pdf"

# Advanced processing
./docker-run.sh shell
# Inside container:
python -m src.main --input file.pdf --output results/ --enhanced --database
```

## docker-run.sh Command Reference

### Essential Commands
| Command | Purpose | Example |
|---------|---------|---------|
| `build` | Build Docker image | `./docker-run.sh build` |
| `start` | Start container | `./docker-run.sh start` |
| `stop` | Stop container | `./docker-run.sh stop` |
| `shell` | Open shell in container | `./docker-run.sh shell` |
| `logs` | View container logs | `./docker-run.sh logs` |
| `status` | Check container status | `./docker-run.sh status` |

### Processing Commands
| Command | Purpose | Example |
|---------|---------|---------|
| `test` | Run quick test | `./docker-run.sh test` |
| `process-file` | Process single PDF | `./docker-run.sh process-file "file.pdf"` |
| `process-batch` | Process directory | `./docker-run.sh process-batch` |
| `process-advanced` | Advanced options | `./docker-run.sh process-advanced "file.pdf" "output/" --enhanced` |

### Maintenance Commands
| Command | Purpose | Example |
|---------|---------|---------|
| `clean` | Remove all containers/images | `./docker-run.sh clean` |
| `version` | Show version info | `./docker-run.sh version` |

## Volume Mapping

### Default Volume Mounts
```bash
Host                           Container
./                         →   /app
./original-pdf-examples    →   /app/original-pdf-examples
```

### Working with Files
- **Edit on Host**: Use your preferred IDE/editor on host machine
- **Run in Container**: Execute all commands inside container
- **Output Files**: Generated in mounted directories, accessible from host

## Environment Management

### Development Environment Variables
```bash
# Required
GOOGLE_API_KEY=your_gemini_api_key

# Optional for database features
NEXT_PUBLIC_SUPABASE_URL=your_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key

# Development settings
LOG_LEVEL=DEBUG
ENABLE_ULTRA_THINK=true
MAX_CONCURRENT_REQUESTS=10
```

### Container Environment
- Python 3.12
- All dependencies pre-installed
- Hot reload enabled (code changes reflect immediately)
- Isolated from host system

## Best Practices

### 1. Always Use Container Shell
```bash
# Good: Run inside container
./docker-run.sh shell
pytest

# Avoid: Running on host
pytest  # May fail due to missing dependencies
```

### 2. Consistent Command Execution
```bash
# Processing commands
./docker-run.sh process-file "file.pdf"

# Development commands
./docker-run.sh shell
# Then run commands inside container
```

### 3. Container Lifecycle Management
```bash
# Start day
./docker-run.sh start

# Development work
./docker-run.sh shell

# End day
./docker-run.sh stop
```

### 4. Debugging Inside Container
```bash
# Interactive debugging
./docker-run.sh shell
python -m pdb -m src.main --input test.pdf --output debug/

# Check logs
docker logs chromoforge-app -f
```

## Troubleshooting

### Common Issues and Solutions

#### Docker Not Running
```bash
# Error: Docker daemon not running
# Solution: Start Docker Desktop application
```

#### Permission Denied
```bash
# Error: Permission denied on docker-run.sh
chmod +x ./docker-run.sh
```

#### Container Won't Start
```bash
# Check logs
docker-compose logs

# Remove and rebuild
./docker-run.sh clean
./docker-run.sh build
```

#### File Not Found in Container
```bash
# Ensure file is in mounted directory
# Only files under ./ are accessible in container
```

## Advanced Docker Usage

### Custom Docker Commands
```bash
# Execute specific command
docker-compose exec chromoforge python -c "print('Hello')"

# Run with different user
docker-compose exec --user root chromoforge bash

# One-off container
docker-compose run --rm chromoforge pytest
```

### Performance Optimization
```bash
# Limit resources
docker update --cpus="2" --memory="4g" chromoforge-app

# Check resource usage
docker stats chromoforge-app
```

### Multi-Stage Builds
The Dockerfile uses multi-stage builds:
1. **Base Stage**: Python and system dependencies
2. **Development Stage**: All dependencies + dev tools
3. **Production Stage**: Minimal runtime dependencies

## Integration with IDEs

### VS Code
```json
// .vscode/settings.json
{
  "python.defaultInterpreterPath": "/usr/local/bin/python",
  "python.terminal.executeInFileDir": false,
  "python.terminal.activateEnvironment": false,
  "terminal.integrated.defaultProfile.linux": "bash"
}
```

### PyCharm
- Configure remote interpreter pointing to Docker container
- Set working directory to `/app`
- Map local directory to container `/app`

## Production Deployment

### Building Production Image
```bash
# Build optimized image
docker build -t chromoforge:prod --target production .

# Test production image
docker run --env-file .env chromoforge:prod python -m src.main --help
```

### Container Registry
```bash
# Tag for registry
docker tag chromoforge:prod registry.example.com/chromoforge:v1.0.0

# Push to registry
docker push registry.example.com/chromoforge:v1.0.0
```

## Docker Security Best Practices

1. **Non-root User**: Container runs as non-root user
2. **Minimal Base Image**: Uses python:3.12-slim
3. **No Secrets in Image**: Use environment variables
4. **Regular Updates**: Rebuild regularly for security patches
5. **Scan Images**: Use `docker scan` before deployment