# ChromoForge Architecture and Design Patterns

## System Architecture

### High-Level Architecture
ChromoForge follows a layered architecture with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────┐
│                    CLI Interface (main.py)               │
├─────────────────────────────────────────────────────────┤
│                  Application Layer                       │
│              (ChromoForgeApp orchestrator)              │
├─────────────────────────────────────────────────────────┤
│                  Processing Layer                        │
│   ┌─────────────┬─────────────┬────────────────────┐   │
│   │ OCR Process │ PII Detect  │ PDF Obfuscator    │   │
│   │ (Gemini)    │ (Patterns)  │ (Multiple Methods) │   │
│   └─────────────┴─────────────┴────────────────────┘   │
├─────────────────────────────────────────────────────────┤
│                   Core Layer                             │
│   ┌─────────────┬─────────────┬────────────────────┐   │
│   │   Config    │   Models    │    Constants       │   │
│   │  (Pydantic) │  (Domain)   │   (Enums/Vals)     │   │
│   └─────────────┴─────────────┴────────────────────┘   │
├─────────────────────────────────────────────────────────┤
│              Security & Infrastructure                   │
│   ┌─────────────┬─────────────┬────────────────────┐   │
│   │ Encryption  │ Audit Log   │   Database         │   │
│   │  (AES/RSA)  │  (HIPAA)    │  (Supabase)        │   │
│   └─────────────┴─────────────┴────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

### Component Architecture

#### 1. CLI Layer (`src/main.py`)
- **Pattern**: Command Pattern with feature flags
- **Responsibilities**: 
  - Parse command-line arguments
  - Initialize application with configuration
  - Route to appropriate processing methods
  - Handle global error reporting

#### 2. Application Layer (`ChromoForgeApp`)
- **Pattern**: Facade Pattern
- **Responsibilities**:
  - Orchestrate component initialization
  - Manage processing workflows
  - Coordinate between different services
  - Handle transaction boundaries

#### 3. Processing Layer
- **Pattern**: Strategy Pattern for different processors
- **Components**:
  - `GeminiOCRProcessor`: OCR with ULTRA THINK mode
  - `PIIDetector`: Pattern-based PII detection
  - `PDFObfuscator`: Multiple obfuscation strategies
  - `BatchProcessor`: Concurrent processing coordinator

#### 4. Core Layer
- **Pattern**: Domain-Driven Design
- **Components**:
  - `Config`: Pydantic-based configuration management
  - `Models`: Domain entities (OCRResult, PIIMatch, ExtractedField)
  - `Constants`: Enums and constant values
  - `Exceptions`: Custom exception hierarchy

#### 5. Security Layer
- **Pattern**: Decorator Pattern for security concerns
- **Components**:
  - `PIIEncryption`: AES encryption for sensitive data
  - `AuditLogger`: HIPAA-compliant audit trails
  - Database security via RLS and encryption

## Design Patterns

### 1. Single Entry Point Pattern
```python
# All functionality through main.py with feature flags
python -m src.main --input file.pdf --output ./results --enhanced --database
```

### 2. Feature Flag Pattern
```python
# Progressive enhancement via CLI flags
if args.enhanced:
    medical_extractor = MedicalDataExtractor()
if args.database:
    db_client = SupabaseIntegration()
```

### 3. Strategy Pattern (PDF Obfuscation)
```python
class ObfuscationMethod(Enum):
    BLACK_BOX = "black_box"
    BLUR = "blur"
    REDACT = "redact"
    WHITE_BOX = "white_box"
    HASH_PATTERN = "hash_pattern"

# Strategy selection at runtime
method = ObfuscationMethod(args.obfuscation_method)
```

### 4. Repository Pattern (Database Integration)
```python
class SupabaseIntegration:
    async def save_ocr_result(self, result: OCRResult) -> str
    async def save_medical_extraction(self, extraction: dict) -> str
    async def get_document(self, doc_id: str) -> dict
```

### 5. Circuit Breaker Pattern
```python
@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    retry=retry_if_exception_type((HTTPError, ConnectionError))
)
async def call_external_api():
    # Prevents cascading failures
```

### 6. Builder Pattern (Configuration)
```python
class Settings(BaseSettings):
    # Environment-based configuration building
    google_api_key: str
    supabase_url: Optional[str] = None
    supabase_key: Optional[str] = None
    
    class Config:
        env_file = ".env"
```

### 7. Async/Await Pattern
```python
async def process_batch(self, files: List[Path]) -> List[ProcessingResult]:
    # Concurrent processing with asyncio
    tasks = [self.process_file(f) for f in files]
    return await asyncio.gather(*tasks)
```

## Data Flow Architecture

### Standard Processing Flow
```
1. Input PDF → 2. OCR Processing → 3. Text Extraction
                        ↓
              4. PII Detection → 5. Obfuscation
                        ↓
              6. Result Storage → 7. Output Files
```

### Enhanced Processing Flow (--enhanced)
```
1. Input PDF → 2. OCR with ULTRA THINK → 3. Text + Structure
                        ↓
              4. Medical Extraction → 5. PII Detection
                        ↓
              6. Field Validation → 7. Obfuscation
                        ↓
              8. Database Storage → 9. Audit Logging
```

## Security Architecture

### Defense in Depth
1. **Input Validation**: File type, size, content validation
2. **Process Isolation**: Docker containers for execution
3. **Data Encryption**: AES-256 for PII at rest
4. **Access Control**: RLS policies in database
5. **Audit Logging**: Immutable audit trail
6. **Output Sanitization**: Redacted PDFs with no PII

### Encryption Flow
```
PII Data → AES Encryption → Encrypted Storage
         ↓                           ↓
    Encryption Key → Key Vault → Decryption (authorized only)
```

## Performance Architecture

### Concurrency Model
- **Worker Pool**: Configurable concurrent processors
- **Batch Processing**: Efficient multi-file handling
- **Resource Limits**: Memory and CPU constraints
- **Circuit Breakers**: Prevent overload

### Optimization Strategies
1. **Lazy Loading**: Components initialized on demand
2. **Connection Pooling**: Reuse database connections
3. **Result Caching**: Cache OCR results for reprocessing
4. **Async I/O**: Non-blocking file and network operations

## Error Handling Architecture

### Exception Hierarchy
```
ChromoForgeError (base)
├── ConfigurationError
├── ProcessingError
│   ├── OCRError
│   ├── PIIDetectionError
│   └── ObfuscationError
├── DatabaseError
└── SecurityError
```

### Error Recovery Strategies
1. **Retry with Backoff**: Transient failures
2. **Fallback Methods**: Alternative processing paths
3. **Partial Success**: Process what's possible
4. **Detailed Logging**: Full error context

## Extensibility Points

### Adding New Features
1. **New Obfuscation Methods**: Extend ObfuscationMethod enum
2. **Additional PII Patterns**: Add to PIIDetector patterns
3. **New Medical Fields**: Extend MedicalDataExtractor
4. **Custom Processors**: Implement processor interface

### Integration Points
1. **External APIs**: Via configuration and dependency injection
2. **Database Backends**: Repository pattern allows swapping
3. **Storage Systems**: Configurable storage backends
4. **Monitoring**: Structured logging for observability

## Testing Architecture

### Test Pyramid
```
        E2E Tests (5%)
       /            \
   Integration (25%)
  /                  \
Unit Tests (70%)
```

### Test Organization
- **Unit Tests**: Individual component testing
- **Integration Tests**: Real API calls (marked)
- **E2E Tests**: Full pipeline validation
- **Performance Tests**: Benchmark critical paths
- **Security Tests**: Vulnerability scanning

## Deployment Architecture

### Container Structure
```
chromoforge:latest
├── Python 3.12 runtime
├── Application code
├── Dependencies
└── Configuration
```

### Environment Management
- **Development**: Docker Desktop with hot reload
- **Testing**: Isolated test containers
- **Production**: Orchestrated containers with monitoring