# ChromoForge Testing Strategy and Quality Gates

## Testing Philosophy
ChromoForge employs a comprehensive testing strategy emphasizing real-world validation, security verification, and performance benchmarking. All tests are designed to catch issues early and ensure production readiness.

## Test Suite Overview

### Test Coverage (19 Test Modules)
```
tests/
├── Core Tests (4 modules)
│   ├── test_core_config_real.py      # Configuration management
│   ├── test_core_constants_real.py   # Application constants
│   ├── test_core_exceptions_real.py  # Exception handling
│   └── test_core_models.py          # Domain models (implied)
├── Processing Tests (5 modules)
│   ├── test_ocr_processor_real.py    # OCR with real Gemini API
│   ├── test_pii_detection_real.py    # PII pattern matching
│   ├── test_pdf_obfuscation_real.py  # PDF manipulation
│   ├── test_batch_processor_real.py  # Concurrent processing
│   └── test_enhanced_obfuscation.py  # Enhanced features
├── Integration Tests (4 modules)
│   ├── test_gemini_integration_real.py   # Google Gemini API
│   ├── test_supabase_integration_real.py  # Database operations
│   ├── test_end_to_end_real.py          # Full pipeline
│   └── test_setup.py                     # Environment setup
├── Security Tests (2 modules)
│   ├── test_security_pii_encryption_real.py  # Encryption
│   └── test_security_audit_logger_real.py    # Audit logging
├── Utility Tests (2 modules)
│   ├── test_utils_logging_config_real.py  # Logging setup
│   └── test_utils_utils_real.py          # Common utilities
└── Specialized Tests (2 modules)
    ├── test_generic_schema.py          # Database schema
    └── test_obfuscation_diagnostic.py  # Diagnostic tools
```

## Testing Levels

### 1. Unit Tests (70% of tests)
- **Scope**: Individual functions and classes
- **Mocking**: External dependencies mocked
- **Speed**: <100ms per test
- **Coverage Target**: >90% for critical paths

**Example Patterns**:
```python
def test_pii_detection_thai_id():
    """Test Thai National ID detection with checksum validation"""
    detector = PIIDetector()
    text = "ID: 1234567890121"
    matches = detector.detect_pii(text)
    assert len(matches) == 1
    assert matches[0].type == PIIType.THAI_NATIONAL_ID
```

### 2. Integration Tests (25% of tests)
- **Scope**: Component interactions
- **Real APIs**: Marked with `@pytest.mark.integration`
- **Speed**: 1-5 seconds per test
- **Focus**: API contracts, data flow

**Example Patterns**:
```python
@pytest.mark.integration
async def test_gemini_ocr_real_document():
    """Test OCR with real Gemini API call"""
    processor = GeminiOCRProcessor()
    result = await processor.process_pdf("test.pdf")
    assert result.text
    assert result.confidence > 0.8
```

### 3. End-to-End Tests (5% of tests)
- **Scope**: Complete workflows
- **Real Data**: Actual PDF processing
- **Speed**: 10-30 seconds per test
- **Focus**: User scenarios

**Example**: `test_end_to_end_real.py`
- Process PDF → Extract text → Detect PII → Obfuscate → Save results

## Test Markers and Categories

### pytest Markers
```python
# Test categorization
@pytest.mark.integration  # Real API calls
@pytest.mark.performance  # Performance benchmarks
@pytest.mark.security     # Security validations
@pytest.mark.slow        # Long-running tests
@pytest.mark.database    # Database operations
```

### Running Specific Categories
```bash
# Inside Docker container
pytest -m integration    # Run integration tests only
pytest -m "not slow"     # Skip slow tests
pytest -m security       # Security tests only
```

## Quality Gates

### 1. Pre-Commit Checks
```bash
# Automated before commit
black src/ tests/ --check
isort src/ tests/ --check
flake8 src/ tests/
mypy src/
```

### 2. Continuous Integration Gates
```yaml
quality_gates:
  code_coverage:
    overall: ">= 80%"
    critical_paths: ">= 90%"
  
  performance:
    ocr_processing: "< 5s per page"
    pii_detection: "< 100ms per page"
    batch_processing: "linear scaling"
  
  security:
    bandit_issues: "0 high severity"
    dependency_scan: "no critical CVEs"
  
  integration:
    api_success_rate: ">= 99%"
    database_operations: "all passing"
```

### 3. Test Execution Requirements
1. **All tests must pass** before merge
2. **Coverage must not decrease** from baseline
3. **Performance benchmarks** must not regress
4. **Security scans** must be clean

## Test Data Management

### Test Fixtures
```python
# conftest.py patterns
@pytest.fixture
def sample_pdf_path():
    """Provide test PDF file path"""
    return Path("tests/fixtures/test_document.pdf")

@pytest.fixture
def mock_ocr_result():
    """Mock OCR result for unit tests"""
    return OCRResult(
        text="Sample medical text",
        confidence=0.95,
        metadata={}
    )
```

### Test Data Categories
1. **Synthetic Data**: Generated test cases
2. **Anonymized Samples**: Real structure, fake PII
3. **Edge Cases**: Boundary conditions
4. **Error Scenarios**: Invalid inputs

## Performance Testing

### Benchmarking Strategy
```python
@pytest.mark.performance
def test_ocr_processing_speed(benchmark):
    """Benchmark OCR processing performance"""
    processor = GeminiOCRProcessor()
    result = benchmark(processor.process_pdf, "test.pdf")
    assert benchmark.stats["mean"] < 5.0  # seconds
```

### Performance Metrics
- **OCR Processing**: <5s per page
- **PII Detection**: <100ms per page
- **PDF Obfuscation**: <2s per page
- **Batch Processing**: Linear scaling with concurrency

## Security Testing

### Security Test Categories
1. **Input Validation**: File upload security
2. **PII Protection**: Encryption verification
3. **Access Control**: RLS policy testing
4. **Audit Trail**: Logging completeness

### Security Validation
```python
@pytest.mark.security
def test_pii_encryption():
    """Verify PII is properly encrypted"""
    encryptor = PIIEncryption()
    sensitive_data = "1234567890121"  # Thai ID
    encrypted = encryptor.encrypt(sensitive_data)
    assert sensitive_data not in encrypted
    assert encryptor.decrypt(encrypted) == sensitive_data
```

## Test Environment Management

### Docker-Based Testing
```bash
# Access test environment
./docker-run.sh shell

# Run full test suite
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific module
pytest tests/test_ocr_processor_real.py -v
```

### Environment Variables for Testing
```bash
# Test-specific settings
TEST_MODE=true
DISABLE_ULTRA_THINK=true  # Speed up tests
MAX_CONCURRENT_REQUESTS=2  # Limit for tests
```

## Continuous Improvement

### Test Metrics Tracking
1. **Coverage Trends**: Monitor coverage over time
2. **Test Duration**: Track test suite performance
3. **Flaky Tests**: Identify and fix unstable tests
4. **Failure Analysis**: Root cause investigation

### Test Maintenance Guidelines
1. **Keep Tests Fast**: Optimize slow tests
2. **Maintain Independence**: Tests should not depend on each other
3. **Clear Naming**: Descriptive test names
4. **Regular Cleanup**: Remove obsolete tests

## Testing Best Practices

### 1. Arrange-Act-Assert Pattern
```python
def test_example():
    # Arrange
    detector = PIIDetector()
    test_text = "Patient ID: 1234567890121"
    
    # Act
    results = detector.detect_pii(test_text)
    
    # Assert
    assert len(results) == 1
    assert results[0].type == PIIType.THAI_NATIONAL_ID
```

### 2. Descriptive Test Names
- `test_thai_id_detection_with_valid_checksum`
- `test_ocr_fails_gracefully_on_corrupted_pdf`
- `test_batch_processing_respects_concurrency_limit`

### 3. Comprehensive Error Testing
- Test both success and failure paths
- Verify error messages are helpful
- Ensure graceful degradation

### 4. Real-World Scenarios
- Use actual Thai medical document patterns
- Test with various PDF formats
- Include edge cases from production

## Integration with CI/CD

### GitHub Actions Integration (Future)
```yaml
name: ChromoForge Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run Tests
        run: |
          docker-compose build
          docker-compose run chromoforge pytest
```

### Quality Reporting
- Coverage reports in HTML format
- Performance benchmarks tracked
- Security scan results archived
- Test failure notifications