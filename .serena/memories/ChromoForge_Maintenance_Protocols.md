# ChromoForge Docker Infrastructure - Maintenance Protocols

## Maintenance Schedule Framework

### Daily Monitoring (Automated)
```bash
# Service health check
docker ps --filter "name=chromoforge" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Resource usage monitoring
docker stats chromoforge-app --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# Log monitoring for errors
docker logs chromoforge-app --since 24h | grep -i "error\|exception\|fail" || echo "No errors found"
```

### Weekly Maintenance Tasks

#### Space Usage Assessment
```bash
# Check Docker space usage
docker system df

# Monitor volume growth
docker volume inspect chromoforge_data | jq '.[].UsageData.Size'

# Image size tracking
docker images chromoforge:* --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
```

#### Service Validation
```bash
# Comprehensive health check
./docker-run.sh test

# Integration test status review
pytest tests/test_*_real.py --tb=short -v

# Database connectivity verification
python -c "from src.core.config import get_settings; print(f'DB: {get_settings().supabase_url is not None}')"
```

### Monthly Activities

#### Performance Baseline Review
- Compare current metrics against baseline
- Identify performance degradation trends
- Update performance thresholds if needed
- Document capacity planning requirements

#### Security Posture Assessment
```bash
# Scan for vulnerable dependencies
docker run --rm -v $(pwd):/app safety check --json -r requirements.txt

# Validate .dockerignore effectiveness
find . -name ".env*" -o -name "*.key" -o -name "secrets" | wc -l  # Should be 0 in build context

# Review audit logs for anomalies
grep -i "security\|auth\|fail" logs/*.log | head -20
```

#### Test Suite Maintenance
- Address failing integration tests (current: 6/9)
- Validate schema alignment with Supabase
- Update test data and expectations
- Performance test baseline updates

### Quarterly Reviews

#### Comprehensive System Audit
- AgentOS compliance score review
- Security vulnerability assessment
- Performance optimization opportunities
- Resource utilization analysis

#### Configuration Management
- Environment variable audit
- Secrets rotation schedule
- Database connection string validation
- API key lifecycle management

#### Documentation Updates
- Architecture diagram refresh
- Runbook accuracy verification
- Troubleshooting guide updates
- Onboarding documentation review

## Maintenance Commands Reference

### Essential Monitoring Commands
```bash
# Quick health check
./docker-run.sh status

# Performance snapshot
docker stats --no-stream chromoforge-app

# Service logs (last hour)
docker logs chromoforge-app --since 1h

# Volume usage detail
docker volume inspect chromoforge_data --format '{{json .UsageData}}'

# Network connectivity test
docker exec chromoforge-app curl -f http://localhost:8000/health || echo "Health check failed"
```

### Cleanup Commands (Monthly)
```bash
# Safe cleanup (preserves active resources)
docker system prune -f

# Remove unused volumes (CAUTION: Review first)
docker volume ls --filter "dangling=true" --format "table {{.Name}}\t{{.CreatedAt}}"

# Clean build cache
docker builder prune -f

# Image cleanup (keep latest 2 versions)
docker images chromoforge --format "{{.Tag}}" | tail -n +3 | xargs -I {} docker rmi chromoforge:{} || true
```

### Emergency Procedures
```bash
# Quick rollback to last known good version
docker tag chromoforge:1.0.0 chromoforge:latest
./docker-run.sh restart

# Service recovery
./docker-run.sh stop
./docker-run.sh start

# Data backup before maintenance
docker run --rm -v chromoforge_data:/data -v $(pwd)/backup:/backup alpine tar czf /backup/data-$(date +%Y%m%d).tar.gz -C /data .

# Log export for troubleshooting
docker logs chromoforge-app > logs/maintenance-$(date +%Y%m%d-%H%M).log 2>&1
```

## Performance Monitoring Framework

### Key Performance Indicators (KPIs)
1. **OCR Processing Time**: Target <30s per document
2. **Memory Usage**: Baseline established, monitor for +20% growth
3. **Disk Space**: Alert at 80% capacity
4. **Error Rate**: Target <1% of processing requests
5. **API Response Time**: Target <2s for health checks

### Alerting Thresholds
- **Critical**: Service down, error rate >5%
- **Warning**: Memory usage +30%, disk space >70%
- **Info**: Performance degradation >20% from baseline

### Monitoring Data Collection
```bash
# Create monitoring data directory
mkdir -p monitoring/data

# Daily metrics collection (add to cron)
cat > monitoring/collect-metrics.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d-%H%M)
echo "$DATE,$(docker stats chromoforge-app --no-stream --format '{{.CPUPerc}},{{.MemUsage}},{{.PIDs}}')" >> monitoring/data/performance.csv
echo "$DATE,$(docker system df --format 'json' | jq -r '.Images[] | select(.Repository=="chromoforge") | .Size')" >> monitoring/data/storage.csv
EOF

chmod +x monitoring/collect-metrics.sh
```

## Troubleshooting Runbook

### Common Issues & Solutions

#### Service Won't Start
```bash
# Check Docker daemon
systemctl status docker

# Verify image integrity
docker image inspect chromoforge:latest | jq '.[].RootFS'

# Check port conflicts
netstat -tlnp | grep :8000

# Review startup logs
docker logs chromoforge-app --tail 50
```

#### Performance Degradation
```bash
# Resource utilization check
docker stats chromoforge-app

# Process analysis inside container
docker exec chromoforge-app top -bn1

# Database connection test
docker exec chromoforge-app python -c "from src.core.config import get_settings; print('DB OK' if get_settings().supabase_url else 'DB Missing')"
```

#### Integration Test Failures
```bash
# Run specific failing tests
pytest tests/test_supabase_integration_real.py -v

# Check database schema
python -c "from tests.conftest import check_database_schema; check_database_schema()"

# Verify API keys
python -c "from src.core.config import get_settings; s=get_settings(); print(f'Gemini: {bool(s.google_api_key)}, Supabase: {bool(s.supabase_url)}')"
```

### Escalation Procedures
1. **Level 1**: Automated recovery via restart
2. **Level 2**: Manual intervention with runbook
3. **Level 3**: Rollback to chromoforge:1.0.0
4. **Level 4**: Data recovery from backup
5. **Level 5**: Full system rebuild

## Optimization Roadmap

### Immediate Opportunities (Next 30 days)
- [ ] Volume optimization: Reduce 173.4MB to <100MB
- [ ] Integration test fixes: Address 6/9 failing tests
- [ ] Schema alignment: Supabase database consistency
- [ ] Monitoring dashboard: Real-time metrics

### Medium-term Goals (Next 90 days)
- [ ] Multi-stage Dockerfile: Further size reduction
- [ ] Performance benchmarking: Establish SLAs
- [ ] Security hardening: Container scanning integration
- [ ] Backup automation: Scheduled data protection

### Long-term Vision (Next 6 months)
- [ ] Kubernetes migration planning
- [ ] Horizontal scaling capability
- [ ] Advanced monitoring with APM
- [ ] CI/CD pipeline integration