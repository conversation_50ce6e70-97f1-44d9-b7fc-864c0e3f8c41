# Changelog

All notable changes to ChromoForge OCR Pipeline will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2025-08-05

### Added
- **Docker Configuration Improvements**: Complete overhaul of Docker setup with proper naming and versioning
- **Semantic Versioning**: Implemented proper semantic versioning starting with v1.0.0
- **Version Management System**: Added centralized version tracking with VERSION file and management scripts
- **Container Naming**: Renamed container from 'chromoforge-ocr' to 'chromoforge-app' for better clarity
- **Image Naming**: Changed image name from 'chromoforge-chromoforge:latest' to 'chromoforge:1.0.0' and 'chromoforge:latest'
- **Version Management Script**: Added `scripts/version.sh` for comprehensive version management
  - Show current version and Docker image tags
  - Set specific versions
  - Bump versions (major, minor, patch)
  - Build Docker images with proper version tags
- **Centralized Version Reading**: Updated constants.py to read version from VERSION file
- **Environment Variable Support**: Added CHROMOFORGE_VERSION environment variable for Docker builds
- **Enhanced Documentation**: Updated README-Docker.md with new naming conventions and version management
- **Version Command**: Added version command to docker-run.sh script

### Changed
- **Docker Compose Configuration**: Updated docker-compose.yml to use new naming and versioning scheme
- **Build Process**: Enhanced docker-run.sh build command to automatically set version from VERSION file
- **Documentation**: Updated all documentation to reflect new container and image names
- **Environment Configuration**: Added version management to .env.example

### Technical Details
- Container name: `chromoforge-ocr` → `chromoforge-app`
- Image name: `chromoforge-chromoforge:latest` → `chromoforge:1.0.0` and `chromoforge:latest`
- Version file: Added `VERSION` file in project root
- Version script: Added `scripts/version.sh` with comprehensive version management
- Docker tags: Now supports both versioned tags (e.g., `chromoforge:1.0.0`) and latest tag
- Environment variables: Added `CHROMOFORGE_VERSION` for build-time version injection

### Infrastructure
- **Docker Desktop Compatibility**: Maintained full Docker Desktop visibility and management capabilities
- **Hot Reload**: Preserved development hot-reload functionality
- **Volume Mounts**: Maintained all existing volume mount configurations
- **Network Configuration**: Preserved existing network setup

### Developer Experience
- **Version Commands**: Easy version management with `scripts/version.sh`
- **Build Integration**: Automatic version detection during Docker builds
- **Status Reporting**: Enhanced status commands show version information
- **Documentation**: Comprehensive documentation updates for new workflows

## [Unreleased]

### Planned
- Integration with CI/CD pipelines for automated version bumping
- Git tag synchronization with version releases
- Automated changelog generation
- Version-specific deployment strategies

---

## Version Management Commands

```bash
# Show current version
scripts/version.sh show

# Set specific version
scripts/version.sh set 1.2.3

# Bump version
scripts/version.sh bump patch    # 1.0.0 -> 1.0.1
scripts/version.sh bump minor    # 1.0.1 -> 1.1.0
scripts/version.sh bump major    # 1.1.0 -> 2.0.0

# Build with current version
scripts/version.sh build
```

## Docker Commands

```bash
# Build with version tags
./docker-run.sh build

# Show version information
./docker-run.sh version

# Check container status
./docker-run.sh status
```
