#!/bin/bash
# ChromoForge Safe Cleanup Execution Protocol
# Implements staged cleanup with comprehensive rollback procedures
# Target: 32.86GB → <7GB recovery with full safety validation

set -euo pipefail

# Script configuration
readonly SCRIPT_VERSION="1.0.0"
readonly TARGET_RECOVERY_GB=30
readonly SAFETY_BACKUP_DIR=".cleanup-safety"
readonly ROLLBACK_IMAGE_TAG="chromoforge-rollback"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly NC='\033[0m'
readonly BOLD='\033[1m'

# Logging functions
log() { echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"; }
success() { echo -e "${GRE<PERSON>}[SUCCESS]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
critical() { echo -e "${BOLD}${RED}[CRITICAL]${NC} $1"; }
phase() { echo -e "${BOLD}${PURPLE}[PHASE]${NC} $1"; }

# Error handling
trap 'handle_error $? $LINENO' ERR

handle_error() {
    local exit_code=$1
    local line_number=$2
    critical "Cleanup failed on line $line_number with exit code $exit_code"
    critical "Initiating emergency rollback procedures..."
    emergency_rollback
    exit $exit_code
}

# Safety validation functions
validate_docker_environment() {
    log "Validating Docker environment..."
    
    # Check Docker daemon
    if ! docker info > /dev/null 2>&1; then
        critical "Docker daemon not running"
        return 1
    fi
    
    # Check Docker Compose
    if ! docker-compose --version > /dev/null 2>&1; then
        critical "Docker Compose not available"
        return 1
    fi
    
    # Check for critical services
    if systemctl is-active --quiet docker 2>/dev/null; then
        success "Docker service is active"
    else
        warn "Cannot verify Docker service status"
    fi
    
    # Check available disk space (need at least 2GB free for safety operations)
    local available_space=$(df . | tail -1 | awk '{print $4}')
    local available_gb=$((available_space / 1024 / 1024))
    
    if [[ $available_gb -lt 2 ]]; then
        critical "Insufficient disk space for safety operations (need 2GB+)"
        return 1
    fi
    
    success "Docker environment validation passed"
    return 0
}

capture_baseline_state() {
    log "Capturing comprehensive baseline state..."
    
    # Create safety backup directory
    mkdir -p "$SAFETY_BACKUP_DIR"
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_prefix="$SAFETY_BACKUP_DIR/baseline_$timestamp"
    
    # Capture Docker system state
    docker system df > "${backup_prefix}_system_df.txt"
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.Size}}\t{{.CreatedAt}}" > "${backup_prefix}_images.txt"
    docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}\t{{.CreatedAt}}" > "${backup_prefix}_containers.txt"
    docker volume ls > "${backup_prefix}_volumes.txt"
    docker network ls > "${backup_prefix}_networks.txt"
    
    # Capture Docker Compose state
    if [[ -f "docker-compose.yml" ]]; then
        docker-compose ps > "${backup_prefix}_compose_services.txt" 2>/dev/null || true
        docker-compose config > "${backup_prefix}_compose_config.yml" 2>/dev/null || true
    fi
    
    # Capture build cache information
    docker builder du > "${backup_prefix}_builder_cache.txt" 2>/dev/null || echo "No build cache" > "${backup_prefix}_builder_cache.txt"
    
    # Calculate current usage
    local current_usage=$(docker system df --format "{{.Size}}" | head -1)
    local reclaimable=$(docker system df --format "{{.Reclaimable}}" | tail -n +2 | head -1)
    
    echo "Current Usage: $current_usage" > "${backup_prefix}_usage_summary.txt"
    echo "Reclaimable: $reclaimable" >> "${backup_prefix}_usage_summary.txt"
    echo "Timestamp: $(date)" >> "${backup_prefix}_usage_summary.txt"
    
    # Store critical configuration files
    [[ -f "docker-compose.yml" ]] && cp docker-compose.yml "${backup_prefix}_docker-compose.yml"
    [[ -f "Dockerfile" ]] && cp Dockerfile "${backup_prefix}_Dockerfile"
    [[ -f ".env" ]] && cp .env "${backup_prefix}_env" 2>/dev/null || true
    [[ -f "VERSION" ]] && cp VERSION "${backup_prefix}_VERSION"
    
    success "Baseline state captured to $SAFETY_BACKUP_DIR"
    export BASELINE_BACKUP_PREFIX="$backup_prefix"
    
    return 0
}

create_safety_image_backup() {
    log "Creating safety image backup..."
    
    # Export current working images
    local working_images=("chromoforge:latest" "chromoforge-dashboard:latest")
    
    for image in "${working_images[@]}"; do
        if docker images -q "$image" > /dev/null 2>&1; then
            local backup_name="${image/:/-}-rollback-$(date +%Y%m%d_%H%M%S).tar"
            log "Backing up $image to $backup_name..."
            
            if docker save "$image" > "$SAFETY_BACKUP_DIR/$backup_name"; then
                success "Backed up $image"
                echo "$image:$SAFETY_BACKUP_DIR/$backup_name" >> "$SAFETY_BACKUP_DIR/image_backups.list"
            else
                warn "Failed to backup $image"
            fi
        fi
    done
    
    return 0
}

validate_service_stop() {
    log "Validating service stop procedures..."
    
    # Check if any ChromoForge services are running
    local running_services=$(docker-compose ps -q 2>/dev/null | wc -l)
    
    if [[ $running_services -gt 0 ]]; then
        warn "ChromoForge services are currently running"
        log "Services must be stopped before cleanup"
        
        read -p "Stop services now? (y/N): " confirm
        if [[ $confirm =~ ^[Yy]$ ]]; then
            log "Stopping ChromoForge services..."
            docker-compose down --timeout 30
            
            # Verify stop
            sleep 5
            local remaining_services=$(docker-compose ps -q 2>/dev/null | wc -l)
            if [[ $remaining_services -eq 0 ]]; then
                success "All services stopped successfully"
            else
                error "Some services failed to stop gracefully"
                return 1
            fi
        else
            error "Cannot proceed with services running"
            return 1
        fi
    else
        success "No running services detected"
    fi
    
    return 0
}

# Staged cleanup execution
execute_stage_1_containers() {
    phase "STAGE 1: Container Cleanup (Lowest Risk)"
    
    log "Removing stopped containers..."
    
    # Count stopped containers
    local stopped_count=$(docker ps -aq -f status=exited | wc -l)
    
    if [[ $stopped_count -eq 0 ]]; then
        success "No stopped containers to remove"
        return 0
    fi
    
    log "Found $stopped_count stopped containers"
    
    # Remove stopped containers with confirmation
    if docker container prune --force; then
        success "Removed $stopped_count stopped containers"
        log "Estimated space recovered: ~9MB"
    else
        error "Failed to remove stopped containers"
        return 1
    fi
    
    return 0
}

execute_stage_2_networks() {
    phase "STAGE 2: Network Cleanup (Low Risk)"
    
    log "Removing unused networks..."
    
    # Capture network list before cleanup
    docker network ls > "$SAFETY_BACKUP_DIR/networks_before_cleanup.txt"
    
    if docker network prune --force; then
        success "Unused networks removed"
    else
        warn "Network cleanup had issues (non-critical)"
    fi
    
    return 0
}

execute_stage_3_volumes() {
    phase "STAGE 3: Volume Cleanup (MEDIUM RISK - Data Loss Possible)"
    
    log "Analyzing volume usage..."
    
    # List unused volumes
    local unused_volumes=$(docker volume ls -qf dangling=true)
    local volume_count=$(echo "$unused_volumes" | wc -w)
    
    if [[ $volume_count -eq 0 ]]; then
        success "No unused volumes to remove"
        return 0
    fi
    
    # Show volumes that will be removed
    log "Unused volumes detected:"
    docker volume ls -f dangling=true
    
    critical "WARNING: This will permanently delete volume data"
    critical "Data in these volumes CANNOT be recovered after deletion"
    
    read -p "Proceed with volume cleanup? Type 'DELETE' to confirm: " confirm
    if [[ "$confirm" != "DELETE" ]]; then
        log "Volume cleanup skipped by user"
        return 0
    fi
    
    log "Removing $volume_count unused volumes..."
    
    if docker volume prune --force; then
        success "Removed $volume_count unused volumes"
        log "Estimated space recovered: ~173MB"
    else
        error "Failed to remove unused volumes"
        return 1
    fi
    
    return 0
}

execute_stage_4_images() {
    phase "STAGE 4: Image Cleanup (MEDIUM RISK - Rebuild Required)"
    
    log "Analyzing image usage..."
    
    # Show current images
    log "Current images:"
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedSince}}"
    
    # Count unused images
    local unused_count=$(docker images -f "dangling=true" -q | wc -l)
    local all_unused=$(docker images --filter "dangling=false" --format "{{.Repository}}:{{.Tag}}" | grep -v "chromoforge" | wc -l)
    
    log "Dangling images: $unused_count"
    log "Non-ChromoForge images: $all_unused"
    
    critical "WARNING: This will remove ALL unused images"
    critical "You will need to rebuild or re-download removed images"
    
    read -p "Proceed with image cleanup? Type 'REBUILD' to confirm: " confirm
    if [[ "$confirm" != "REBUILD" ]]; then
        log "Image cleanup skipped by user"
        return 0
    fi
    
    log "Removing unused images..."
    
    if docker image prune --all --force; then
        success "Unused images removed"
        log "Estimated space recovered: ~17GB"
    else
        error "Failed to remove unused images"
        return 1
    fi
    
    return 0
}

execute_stage_5_build_cache() {
    phase "STAGE 5: Build Cache Cleanup (HIGH IMPACT - Rebuild Performance)"
    
    log "Analyzing build cache..."
    
    # Show build cache usage
    docker builder du 2>/dev/null || log "No build cache information available"
    
    critical "WARNING: This will remove ALL build cache"
    critical "Next builds will be slower but will free maximum space"
    
    read -p "Proceed with build cache cleanup? Type 'SLOW-BUILDS' to confirm: " confirm
    if [[ "$confirm" != "SLOW-BUILDS" ]]; then
        log "Build cache cleanup skipped by user"
        return 0
    fi
    
    log "Removing all build cache..."
    
    if docker builder prune --all --force; then
        success "Build cache removed"
        log "Estimated space recovered: ~15GB"
    else
        error "Failed to remove build cache"
        return 1
    fi
    
    return 0
}

# Post-cleanup validation
validate_cleanup_success() {
    phase "POST-CLEANUP VALIDATION"
    
    log "Validating cleanup results..."
    
    # Capture post-cleanup state
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local post_prefix="$SAFETY_BACKUP_DIR/post_cleanup_$timestamp"
    
    docker system df > "${post_prefix}_system_df.txt"
    docker images > "${post_prefix}_images.txt"
    docker ps -a > "${post_prefix}_containers.txt"
    
    # Calculate space recovered
    local before_file="${BASELINE_BACKUP_PREFIX}_usage_summary.txt"
    if [[ -f "$before_file" ]]; then
        local before_usage=$(grep "Current Usage:" "$before_file" | cut -d' ' -f3)
        local current_usage=$(docker system df --format "{{.Size}}" | head -1)
        
        log "Space usage comparison:"
        log "Before cleanup: $before_usage"
        log "After cleanup:  $current_usage"
        
        # Store results
        echo "Before: $before_usage" > "${post_prefix}_recovery_summary.txt"
        echo "After: $current_usage" >> "${post_prefix}_recovery_summary.txt"
        echo "Timestamp: $(date)" >> "${post_prefix}_recovery_summary.txt"
    fi
    
    return 0
}

test_build_capability() {
    log "Testing build capability post-cleanup..."
    
    # Test if we can still build ChromoForge
    if [[ -f "Dockerfile" ]]; then
        log "Attempting test build..."
        
        if timeout 300 docker build -t chromoforge-test-build . > /dev/null 2>&1; then
            success "Build capability verified"
            docker rmi chromoforge-test-build > /dev/null 2>&1
        else
            error "Build capability compromised"
            critical "Manual intervention may be required"
            return 1
        fi
    else
        warn "No Dockerfile found, skipping build test"
    fi
    
    return 0
}

test_service_startup() {
    log "Testing service startup capability..."
    
    if [[ -f "docker-compose.yml" ]]; then
        log "Attempting service startup test..."
        
        # Try to start services
        if docker-compose up -d --timeout 60; then
            sleep 10
            
            # Check if services are healthy
            local healthy_services=$(docker-compose ps --filter "status=running" -q | wc -l)
            
            if [[ $healthy_services -gt 0 ]]; then
                success "Service startup capability verified"
                docker-compose down --timeout 30
            else
                warn "Services started but may not be healthy"
                docker-compose down --timeout 30
                return 1
            fi
        else
            error "Service startup failed"
            return 1
        fi
    else
        warn "No docker-compose.yml found, skipping service test"
    fi
    
    return 0
}

# Rollback procedures
emergency_rollback() {
    critical "INITIATING EMERGENCY ROLLBACK"
    
    log "Stopping any running containers..."
    docker-compose down --timeout 10 2>/dev/null || true
    
    log "Restoring images from backup..."
    if [[ -f "$SAFETY_BACKUP_DIR/image_backups.list" ]]; then
        while IFS=: read -r image_name backup_file; do
            if [[ -f "$backup_file" ]]; then
                log "Restoring $image_name from $backup_file..."
                docker load < "$backup_file"
            fi
        done < "$SAFETY_BACKUP_DIR/image_backups.list"
    fi
    
    # Restore configuration files
    if [[ -n "${BASELINE_BACKUP_PREFIX:-}" ]]; then
        [[ -f "${BASELINE_BACKUP_PREFIX}_docker-compose.yml" ]] && cp "${BASELINE_BACKUP_PREFIX}_docker-compose.yml" docker-compose.yml
        [[ -f "${BASELINE_BACKUP_PREFIX}_Dockerfile" ]] && cp "${BASELINE_BACKUP_PREFIX}_Dockerfile" Dockerfile
        [[ -f "${BASELINE_BACKUP_PREFIX}_env" ]] && cp "${BASELINE_BACKUP_PREFIX}_env" .env
        [[ -f "${BASELINE_BACKUP_PREFIX}_VERSION" ]] && cp "${BASELINE_BACKUP_PREFIX}_VERSION" VERSION
    fi
    
    success "Emergency rollback completed"
    warn "Manual verification recommended"
}

planned_rollback() {
    phase "PLANNED ROLLBACK PROCEDURE"
    
    warn "Rolling back cleanup operations..."
    
    # Stop services
    log "Stopping services..."
    docker-compose down --timeout 30 2>/dev/null || true
    
    # Restore images
    log "Restoring backed up images..."
    if [[ -f "$SAFETY_BACKUP_DIR/image_backups.list" ]]; then
        while IFS=: read -r image_name backup_file; do
            if [[ -f "$backup_file" ]]; then
                log "Restoring $image_name..."
                if docker load < "$backup_file"; then
                    success "Restored $image_name"
                else
                    error "Failed to restore $image_name"
                fi
            fi
        done < "$SAFETY_BACKUP_DIR/image_backups.list"
    fi
    
    # Restore configuration
    if [[ -n "${BASELINE_BACKUP_PREFIX:-}" ]]; then
        log "Restoring configuration files..."
        
        [[ -f "${BASELINE_BACKUP_PREFIX}_docker-compose.yml" ]] && cp "${BASELINE_BACKUP_PREFIX}_docker-compose.yml" docker-compose.yml
        [[ -f "${BASELINE_BACKUP_PREFIX}_Dockerfile" ]] && cp "${BASELINE_BACKUP_PREFIX}_Dockerfile" Dockerfile
        [[ -f "${BASELINE_BACKUP_PREFIX}_VERSION" ]] && cp "${BASELINE_BACKUP_PREFIX}_VERSION" VERSION
        
        success "Configuration files restored"
    fi
    
    # Test restoration
    if test_build_capability && test_service_startup; then
        success "Rollback completed successfully"
        success "System restored to pre-cleanup state"
    else
        error "Rollback validation failed"
        warn "Manual intervention may be required"
        return 1
    fi
    
    return 0
}

# Main execution flow
execute_safe_cleanup() {
    phase "CHROMOFORGE SAFE CLEANUP EXECUTION"
    
    log "Starting comprehensive cleanup with safety protocols..."
    log "Target recovery: ${TARGET_RECOVERY_GB}GB+"
    
    # Pre-cleanup safety checks
    validate_docker_environment || { error "Environment validation failed"; return 1; }
    capture_baseline_state || { error "Baseline capture failed"; return 1; }
    create_safety_image_backup || { error "Safety backup failed"; return 1; }
    validate_service_stop || { error "Service stop validation failed"; return 1; }
    
    # Execute staged cleanup
    log "Beginning staged cleanup execution..."
    
    execute_stage_1_containers || { error "Stage 1 failed"; return 1; }
    execute_stage_2_networks || { error "Stage 2 failed"; return 1; }
    execute_stage_3_volumes || { error "Stage 3 failed"; return 1; }
    execute_stage_4_images || { error "Stage 4 failed"; return 1; }
    execute_stage_5_build_cache || { error "Stage 5 failed"; return 1; }
    
    # Post-cleanup validation
    validate_cleanup_success || { error "Cleanup validation failed"; return 1; }
    test_build_capability || { error "Build test failed"; return 1; }
    test_service_startup || { error "Service test failed"; return 1; }
    
    success "Safe cleanup execution completed successfully"
    
    # Show final results
    log "Final results:"
    docker system df
    
    return 0
}

# Command interface
show_help() {
    echo "ChromoForge Safe Cleanup Execution Protocol v$SCRIPT_VERSION"
    echo ""
    echo "USAGE:"
    echo "  $0 <command>"
    echo ""
    echo "COMMANDS:"
    echo "  execute         Execute full safe cleanup procedure"
    echo "  validate        Validate environment without cleanup"
    echo "  rollback        Roll back from previous cleanup"
    echo "  emergency       Emergency rollback procedure"
    echo "  status          Show current cleanup status"
    echo "  help            Show this help message"
    echo ""
    echo "SAFETY FEATURES:"
    echo "  • Comprehensive state backup"
    echo "  • Staged execution with confirmations"
    echo "  • Build capability validation"
    echo "  • Emergency rollback procedures"
    echo "  • Service startup testing"
    echo ""
    echo "TARGET: Recover ${TARGET_RECOVERY_GB}GB+ with zero data loss"
}

main() {
    case "${1:-help}" in
        execute)
            execute_safe_cleanup
            ;;
        validate)
            validate_docker_environment && capture_baseline_state
            ;;
        rollback)
            planned_rollback
            ;;
        emergency)
            emergency_rollback
            ;;
        status)
            if [[ -d "$SAFETY_BACKUP_DIR" ]]; then
                log "Cleanup safety backups available:"
                ls -la "$SAFETY_BACKUP_DIR"
            else
                log "No cleanup operations detected"
            fi
            docker system df
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            error "Unknown command: ${1:-}"
            show_help
            exit 1
            ;;
    esac
}

# Execute main with all arguments
main "$@"