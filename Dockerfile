# ChromoForge OCR Pipeline - Docker Configuration
FROM python:3.9-slim

# Set environment variables for Python
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app

# Set environment variables for hot reload (development)
ENV WATCHPACK_POLLING=true
ENV CHOKIDAR_USEPOLLING=true

# Install system dependencies for PDF processing
RUN apt-get update && apt-get install -y \
    poppler-utils \
    ghostscript \
    libmagickwand-dev \
    tesseract-ocr \
    tesseract-ocr-tha \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libfontconfig1 \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Create directories for processing
RUN mkdir -p /app/original-pdf-examples \
    /app/test-results \
    /app/batch-results \
    /app/processed \
    /app/temp

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Set proper permissions for main entry point
RUN chmod +x /app/src/main.py

# Create non-root user for security
RUN useradd -m -u 1000 chromoforge && \
    chown -R chromoforge:chromoforge /app
USER chromoforge

# Default command
CMD ["python", "-m", "src.main", "--help"]
