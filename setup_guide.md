# ChromoForge Setup Guide

## Prerequisites

✅ **Docker Desktop installed and running**
✅ **Git installed**
✅ **Terminal/Command prompt access**

## Quick Start

### 1. <PERSON><PERSON> and Setup Project

```bash
git clone <repository-url>
cd ChromoForge

# Build Docker image
./docker-run.sh build

# Start container
./docker-run.sh start
```

### 2. Basic Configuration (OCR Only)

For basic OCR functionality, you only need a Google Gemini API key:

1. **Get Google Gemini API Key**:
   - Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
   - Sign in with your Google account
   - Click "Create API Key"
   - Copy the API key

2. **Configure Environment**:
   ```bash
   cp .env.example .env
   # Edit .env and add your API key:
   GOOGLE_API_KEY=your-actual-google-api-key
   ```

3. **Test Basic Setup**:
   ```bash
   ./docker-run.sh test
   ```

4. **Run Basic OCR**:
   ```bash
   ./docker-run.sh process-file "original-pdf-examples/file.pdf"
   ```

### 3. Enhanced Features Setup (Optional)

To use `--enhanced` and `--database` flags, set up Supabase:

#### Step 3a: Set up Supabase Project

1. Go to [Supabase](https://supabase.com)
2. Sign up/Sign in
3. Click "New Project"
4. Choose your organization
5. Fill in project details:
   - Name: `ChromoForge`
   - Database Password: (choose a strong password)
   - Region: Choose closest to you
6. Wait for project creation (2-3 minutes)

#### Step 3b: Get Supabase Configuration

Once your project is ready:

1. Go to **Settings** → **API**
2. Copy these values:
   - **Project URL** (looks like: `https://xxxxx.supabase.co`)
   - **anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9`)
   - **service_role secret key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9`)

#### Step 3c: Update Environment Variables

Add Supabase credentials to your `.env` file:

```bash
# Required for OCR
GOOGLE_API_KEY=your-actual-google-api-key

# Optional - for enhanced features
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-actual-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-actual-service-role-key

# Optional - application settings
ENABLE_ULTRA_THINK=true
CONFIDENCE_THRESHOLD=0.7
OBFUSCATION_METHOD=black_box
MAX_CONCURRENT_REQUESTS=10
```

#### Step 3d: Set up Supabase Database

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy and run the contents of relevant migration files from `migrations/` directory
3. This creates the necessary tables and security policies

#### Step 3e: Test Enhanced Features

```bash
# Access container shell for advanced features
./docker-run.sh shell

# Inside container:
# Test enhanced medical extraction
python -m src.main --input file.pdf --output ./results --enhanced

# Test database integration
python -m src.main --input file.pdf --output ./results --database

# Test full features
python -m src.main --input file.pdf --output ./results --enhanced --database --verbose
```

## Configuration Options

### Environment Variables

Your `.env` file can include:

```bash
# Required
GOOGLE_API_KEY=your_gemini_api_key_here

# Optional - for database features
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key  
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_key

# Optional - application settings
ENABLE_ULTRA_THINK=true              # Enhanced OCR processing
CONFIDENCE_THRESHOLD=0.7             # PII detection sensitivity (0.0-1.0)
OBFUSCATION_METHOD=black_box         # Default obfuscation method
MAX_CONCURRENT_REQUESTS=10           # Concurrent processing limit
```

### CLI Flags Reference

#### Basic Usage
```bash
# Process single file
./docker-run.sh process-file "original-pdf-examples/file.pdf"

# Process directory
./docker-run.sh process-batch

# OCR only (access container shell for advanced options)
./docker-run.sh shell
# Inside container:
python -m src.main --input file.pdf --output ./results --no-obfuscation
```

#### Enhanced Features
```bash
# Access container shell for enhanced features
./docker-run.sh shell

# Inside container:
# Enhanced medical extraction
python -m src.main --input file.pdf --output ./results --enhanced

# Database integration
python -m src.main --input file.pdf --output ./results --database

# Combined features
python -m src.main --input file.pdf --output ./results --enhanced --database
```

#### Advanced Options
```bash
# Access container shell for advanced options
./docker-run.sh shell

# Inside container:
# Custom processing
python -m src.main --input ./docs --output ./results \
  --enhanced \
  --database \
  --confidence-threshold 0.8 \
  --obfuscation-method blur \
  --max-concurrent 5 \
  --verbose

# Fast processing (disable ultra-think)
python -m src.main --input file.pdf --output ./results --disable-ultra-think
```

## Verification

### Test Your Setup

1. **Basic Environment Test**:
   ```bash
   ./docker-run.sh test
   ```

2. **API Connection Test**:
   ```bash
   ./docker-run.sh shell
   # Inside container:
   python -c "
   import os
   from dotenv import load_dotenv
   load_dotenv()

   print('✓ Environment loaded')
   print(f'Google API Key: {\"✓ Set\" if os.getenv(\"GOOGLE_API_KEY\") else \"✗ Missing\"}')
   print(f'Supabase URL: {\"✓ Set\" if os.getenv(\"NEXT_PUBLIC_SUPABASE_URL\") else \"✗ Missing\"}')
   print(f'Supabase Anon Key: {\"✓ Set\" if os.getenv(\"NEXT_PUBLIC_SUPABASE_ANON_KEY\") else \"✗ Missing\"}')
   print(f'Supabase Service Key: {\"✓ Set\" if os.getenv(\"SUPABASE_SERVICE_ROLE_KEY\") else \"✗ Missing\"}')
   "
   ```

3. **Processing Test**:
   ```bash
   # Test with sample files (if available)
   ./docker-run.sh process-batch
   ```

4. **Full Test Suite**:
   ```bash
   ./docker-run.sh shell
   # Inside container:
   # Run comprehensive tests
   pytest

   # Run integration tests (requires API keys)
   pytest -m integration
   ```

## Feature Overview

### Core Features (Always Available)
- **OCR Processing**: Google Gemini 2.0 Flash
- **PII Detection**: Thai-specific patterns
- **PDF Obfuscation**: Multiple methods available
- **Batch Processing**: Directory processing with concurrency
- **Error Handling**: Circuit breakers and retry logic

### Enhanced Features (Requires `--enhanced` flag)
- **Medical Field Extraction**: Structured data parsing
- **Patient Code Extraction**: TT-prefixed codes
- **Sample Code Detection**: 6-character alphanumeric codes
- **Investigation Types**: K-TRACK, SPOT-MAS, K4CARE patterns
- **Name Extraction**: Thai and English patient names
- **Date Parsing**: Gregorian and Buddhist Era formats
- **Contact Information**: Phone numbers and addresses

### Database Features (Requires `--database` flag)
- **Supabase Integration**: Secure cloud storage
- **Encrypted PII Storage**: Organization-specific encryption
- **Audit Logging**: HIPAA-compliant tracking
- **Document Management**: Status tracking and metadata
- **Medical Records**: Structured data storage

## Troubleshooting

### Common Issues

1. **Missing API Key**:
   ```
   Error: GOOGLE_API_KEY not found
   Solution: Set GOOGLE_API_KEY in .env file
   ```

2. **Database Connection Failed**:
   ```
   Error: Supabase credentials not found
   Solution: Add Supabase credentials to .env or run without --database
   ```

3. **Import Errors**:
   ```
   Error: Module not found
   Solution: Ensure Docker container is running and properly built
   ```

4. **Permission Denied**:
   ```
   Error: Cannot write to output directory
   Solution: Check write permissions or create output directory manually
   ```

### Getting Help

1. **Check Logs**: Use `--verbose` flag for detailed logging
2. **Test Components**: Run individual tests in `tests/` directory
3. **Verify Environment**: Use the environment test script above
4. **Check Configuration**: Ensure all required environment variables are set

## Next Steps

After successful setup:

1. **Process Sample Documents**: Test with your own PDF files
2. **Explore CLI Options**: Try different flags and configurations
3. **Review Results**: Check output JSON files and obfuscated PDFs
4. **Configure for Production**: Set up appropriate security and monitoring
5. **Integrate with Systems**: Use as part of larger document processing workflows

## Production Deployment

For production use:
1. Use secure environment variable management
2. Set up proper database backups
3. Configure monitoring and alerting
4. Implement proper access controls
5. Consider containerization with Docker