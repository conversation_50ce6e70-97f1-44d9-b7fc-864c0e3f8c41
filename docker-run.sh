#!/bin/bash
# ChromoForge Docker Runner Script
# Provides easy commands to run ChromoForge OCR pipeline in Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[ChromoForge]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker Desktop."
        exit 1
    fi
}

# Function to build the Docker image
build() {
    print_status "Building ChromoForge Docker image..."

    # Set version from VERSION file
    if [ -f "VERSION" ]; then
        export CHROMOFORGE_VERSION=$(cat VERSION)
        print_status "Using version: $CHROMOFORGE_VERSION"
    else
        export CHROMOFORGE_VERSION="1.0.0"
        print_warning "VERSION file not found, using default: $CHROMOFORGE_VERSION"
    fi

    docker-compose build chromoforge
    print_success "Docker image built successfully!"
    print_status "Image tags: chromoforge:$CHROMOFORGE_VERSION, chromoforge:latest"
}

# Function to build the Dashboard Docker image
build_dashboard() {
    print_status "Building ChromoForge Dashboard Docker image..."

    # Set version from VERSION file
    if [ -f "VERSION" ]; then
        export CHROMOFORGE_VERSION=$(cat VERSION)
        print_status "Using version: $CHROMOFORGE_VERSION"
    else
        export CHROMOFORGE_VERSION="1.0.0"
        print_warning "VERSION file not found, using default: $CHROMOFORGE_VERSION"
    fi

    docker-compose build chromoforge-dashboard
    print_success "Dashboard Docker image built successfully!"
    print_status "Image tags: chromoforge-dashboard:$CHROMOFORGE_VERSION, chromoforge-dashboard:latest"
}

# Function to build both services
build_all() {
    print_status "Building all ChromoForge Docker images..."

    # Set version from VERSION file
    if [ -f "VERSION" ]; then
        export CHROMOFORGE_VERSION=$(cat VERSION)
        print_status "Using version: $CHROMOFORGE_VERSION"
    else
        export CHROMOFORGE_VERSION="1.0.0"
        print_warning "VERSION file not found, using default: $CHROMOFORGE_VERSION"
    fi

    docker-compose build
    print_success "All Docker images built successfully!"
    print_status "Built: chromoforge:$CHROMOFORGE_VERSION, chromoforge-dashboard:$CHROMOFORGE_VERSION"
}

# Function to start the container
start() {
    print_status "Starting ChromoForge container..."
    docker-compose up -d chromoforge
    print_success "ChromoForge container is running!"
    print_status "Use 'docker-compose logs -f chromoforge' to view logs"
}

# Function to start the dashboard
start_dashboard() {
    print_status "Starting ChromoForge Dashboard..."
    docker-compose up -d chromoforge-dashboard
    print_success "Dashboard is running at http://localhost:3001"
    print_status "Use 'docker-compose logs -f chromoforge-dashboard' to view logs"
}

# Function to start both services
start_all() {
    print_status "Starting all ChromoForge services..."
    docker-compose up -d
    print_success "All services are running!"
    print_status "Dashboard: http://localhost:3001"
    print_status "Use 'docker-compose logs -f' to view all logs"
}

# Function to start development environment with hot reload
dev_dashboard() {
    print_status "Starting ChromoForge development environment..."
    print_status "Dashboard with hot reload: http://localhost:3001"
    print_status "Press Ctrl+C to stop all services"
    docker-compose up chromoforge-dashboard
}

# Function to start full development environment
dev_all() {
    print_status "Starting full ChromoForge development environment..."
    print_status "Dashboard: http://localhost:3001"
    print_status "Press Ctrl+C to stop all services"
    docker-compose up
}

# Function to stop the container
stop() {
    print_status "Stopping ChromoForge container..."
    docker-compose down
    print_success "ChromoForge containers stopped!"
}

# Function to run quick test
test() {
    print_status "Running ChromoForge quick test..."
    docker-compose exec chromoforge python quick_test_ocr.py
}

# Function to run OCR pipeline on a single file
process_file() {
    if [ -z "$1" ]; then
        print_error "Please provide a file path"
        echo "Usage: $0 process-file <input_file> [output_dir]"
        echo "Example: $0 process-file 'original-pdf-examples/CSF TT04035.pdf' test-results"
        exit 1
    fi
    
    local input_file="$1"
    local output_dir="${2:-test-results}"
    
    print_status "Processing file: $input_file"
    docker-compose exec chromoforge python run_ocr_pipeline.py "$input_file" "$output_dir"
}

# Function to run advanced OCR pipeline
process_advanced() {
    if [ -z "$1" ]; then
        print_error "Please provide a file or directory path"
        echo "Usage: $0 process-advanced <input_path> [output_dir] [additional_args]"
        echo "Example: $0 process-advanced 'original-pdf-examples/CSF TT04035.pdf' test-results --verbose"
        exit 1
    fi
    
    local input_path="$1"
    local output_dir="${2:-test-results}"
    shift 2
    local additional_args="$@"
    
    print_status "Running advanced OCR pipeline on: $input_path"
    docker-compose exec chromoforge python -m src.main --input "$input_path" --output "$output_dir" $additional_args
}

# Function to run batch processing
process_batch() {
    local input_dir="${1:-original-pdf-examples}"
    local output_dir="${2:-batch-results}"
    
    print_status "Processing batch from directory: $input_dir"
    docker-compose exec chromoforge python -m src.main --input "$input_dir" --output "$output_dir" --verbose
}

# Function to open shell in container
shell() {
    print_status "Opening shell in ChromoForge container..."
    docker-compose exec chromoforge /bin/bash
}

# Function to open shell in dashboard container
dashboard_shell() {
    print_status "Opening shell in ChromoForge Dashboard container..."
    docker-compose exec chromoforge-dashboard /bin/bash
}

# Function to view logs
logs() {
    print_status "Viewing ChromoForge container logs..."
    docker-compose logs -f chromoforge
}

# Function to view dashboard logs
dashboard_logs() {
    print_status "Viewing ChromoForge Dashboard logs..."
    docker-compose logs -f chromoforge-dashboard
}

# Function to view all logs
logs_all() {
    print_status "Viewing all ChromoForge service logs..."
    docker-compose logs -f
}

# Function to check dashboard health
dashboard_health() {
    print_status "Checking ChromoForge Dashboard health..."
    if docker-compose exec chromoforge-dashboard curl -f http://localhost:3001/health > /dev/null 2>&1; then
        print_success "Dashboard is healthy and responding!"
    else
        print_error "Dashboard health check failed!"
        print_status "Checking container status..."
        docker-compose ps chromoforge-dashboard
    fi
}

# Function to show status
status() {
    print_status "ChromoForge container status:"
    docker-compose ps
}

# Function to clean up
clean() {
    print_status "Cleaning up ChromoForge containers and images..."
    docker-compose down --rmi all --volumes --remove-orphans
    print_success "Cleanup completed!"
}

# Function to show version
version() {
    if [ -f "VERSION" ]; then
        local current_version=$(cat VERSION)
        print_status "ChromoForge version: $current_version"
        print_status "Container name: chromoforge-app"
        print_status "Image name: chromoforge:$current_version"
    else
        print_warning "VERSION file not found"
    fi
}

# Function to show help
show_help() {
    echo "ChromoForge Docker Runner - AgentOS Container-First Development"
    echo ""
    echo "Usage: $0 <command> [arguments]"
    echo ""
    echo "Build Commands:"
    echo "  build                           Build ChromoForge OCR service"
    echo "  build-dashboard                 Build ChromoForge Dashboard"
    echo "  build-all                       Build all services"
    echo ""
    echo "Development Commands:"
    echo "  dev-dashboard                   Start dashboard with hot reload"
    echo "  dev-all                         Start all services with hot reload"
    echo "  start                           Start OCR service only"
    echo "  start-dashboard                 Start dashboard only"
    echo "  start-all                       Start all services"
    echo "  stop                            Stop all containers"
    echo ""
    echo "OCR Processing Commands:"
    echo "  test                            Run quick functionality test"
    echo "  process-file <file> [output]    Process a single PDF file"
    echo "  process-advanced <path> [args]  Run advanced OCR pipeline"
    echo "  process-batch [input] [output]  Process all PDFs in directory"
    echo ""
    echo "Container Management:"
    echo "  shell                           Open shell in OCR container"
    echo "  dashboard-shell                 Open shell in dashboard container"
    echo "  logs                            View OCR service logs"
    echo "  dashboard-logs                  View dashboard logs"
    echo "  logs-all                        View all service logs"
    echo "  status                          Show container status"
    echo "  dashboard-health                Check dashboard health"
    echo ""
    echo "Utility Commands:"
    echo "  version                         Show version information"
    echo "  clean                           Clean up containers and images"
    echo "  help                            Show this help message"
    echo ""
    echo "AgentOS Development Examples:"
    echo "  $0 build-all                    # Build complete stack"
    echo "  $0 dev-dashboard                # Dashboard development"
    echo "  $0 dev-all                      # Full development environment"
    echo "  $0 dashboard-health             # Check dashboard status"
    echo "  $0 start-all                    # Production-like start"
    echo ""
    echo "OCR Processing Examples:"
    echo "  $0 process-file 'original-pdf-examples/CSF TT04035.pdf'"
    echo "  $0 process-batch"
    echo ""
    echo "Dashboard URL: http://localhost:3001 (when running)"
}

# Main script logic
check_docker

case "$1" in
    # Build commands
    build)
        build
        ;;
    build-dashboard)
        build_dashboard
        ;;
    build-all)
        build_all
        ;;
    
    # Development commands
    dev-dashboard)
        dev_dashboard
        ;;
    dev-all)
        dev_all
        ;;
    start)
        start
        ;;
    start-dashboard)
        start_dashboard
        ;;
    start-all)
        start_all
        ;;
    stop)
        stop
        ;;
    
    # OCR processing commands
    test)
        test
        ;;
    process-file)
        shift
        process_file "$@"
        ;;
    process-advanced)
        shift
        process_advanced "$@"
        ;;
    process-batch)
        shift
        process_batch "$@"
        ;;
    
    # Container management
    shell)
        shell
        ;;
    dashboard-shell)
        dashboard_shell
        ;;
    logs)
        logs
        ;;
    dashboard-logs)
        dashboard_logs
        ;;
    logs-all)
        logs_all
        ;;
    dashboard-health)
        dashboard_health
        ;;
    status)
        status
        ;;
    
    # Utility commands
    version)
        version
        ;;
    clean)
        clean
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
