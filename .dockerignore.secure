# ChromoForge Security-Enhanced .dockerignore
# CRITICAL: Prevent credential leakage into Docker images
# Version: 2.0.0 - AgentOS Compliant

# ===================================================================
# CRITICAL SECURITY EXCLUSIONS - NEVER COPY TO IMAGES
# ===================================================================

# Environment files (HIGHEST PRIORITY - API KEYS)
.env*
*.env
.env.local
.env.production
.env.staging
.env.development
.env.test
.env.backup
.env.bak
.env.example

# Secret directories and files
secrets/
.secrets/
keys/
certificates/
certs/
ssl/
private/
auth/

# API credentials and authentication
*.key
*.pem
*.crt
*.cer
*.p12
*.pfx
*.jks
*.keystore
*.token
credentials.json
auth.json
service-account*.json
google-credentials*.json
aws-credentials*
azure-credentials*

# Database credentials
database.url
db.config
*.db.env
connection-strings*

# SSH and GPG keys
id_rsa*
id_dsa*
id_ecdsa*
id_ed25519*
*.pub
*.gpg
*.asc
known_hosts

# ===================================================================
# MEDICAL DATA PROTECTION (HIPAA COMPLIANCE)
# ===================================================================

# Patient data directories (NEVER COPY)
original-pdf-examples/
processed/
batch-results/
test-results/
results-v2/
diagnostic-results/
patient-data/
medical-records/
sample-documents/
test-obfuscation*/

# Medical document file types
*.pdf
*.PDF
*.jpg
*.jpeg
*.JPG
*.JPEG
*.png
*.PNG
*.tiff
*.tif
*.TIFF
*.TIF
*.bmp
*.BMP
*.gif
*.GIF
*.webp
*.WEBP

# OCR and processing results
*_ocr_results.json
*_extraction_results.json
*_pii_results.json
*_coordinates.json
*_diagnostic.json
*obfuscated_*.pdf

# Medical data exports
*.csv
*.xlsx
*.xls
*.xml
*.hl7
*.dicom
*.dcm

# ===================================================================
# DEVELOPMENT ARTIFACTS (BUILD OPTIMIZATION)
# ===================================================================

# Version control
.git/
.github/
.gitignore
.gitattributes
.gitmodules

# Documentation (reduces image size)
*.md
README*
CHANGELOG*
LICENSE*
CONTRIBUTING*
docs/
documentation/
wiki/

# IDE and editor files
.vscode/
.vscode-test/
*.code-workspace
.idea/
*.iml
*.ipr
*.iws
.atom/
*.sublime-*
*.swp
*.swo
*~
.netrwhist
.emacs*
.vim*

# ===================================================================
# BUILD AND CACHE EXCLUSIONS
# ===================================================================

# Python development
__pycache__/
*.py[cod]
*$py.class
*.so
.pytest_cache/
.mypy_cache/
.ruff_cache/
.coverage*
htmlcov/
.tox/
.nox/

# Virtual environments (never copy to container)
venv/
env/
ENV/
.venv/
.env/
.virtualenv/
pyvenv.cfg
Pipfile.lock
poetry.lock

# Node.js development (dashboard)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm/
.yarn-integrity
.eslintcache

# Build artifacts and temporary files
build/
dist/
*.egg-info/
target/
temp/
tmp/
*.tmp
*.temp
*.bak
*.backup
.cache/
cache/

# ===================================================================
# DOCKER AND CONTAINERIZATION
# ===================================================================

# Docker files (prevent recursive copying)
Dockerfile*
docker-compose*.yml
docker-compose*.yaml
.dockerignore*
.docker/
docker-logs/

# Container runtime files
.dockerignore
docker-compose.override.yml
docker-compose.local.yml

# ===================================================================
# LOGS AND MONITORING
# ===================================================================

# Application logs
*.log
*.log.*
logs/
log/
*.jsonl
*.ndjson
*.out
*.err
debug.log
debug/
*.debug

# System logs
syslog
audit-logs/
security-logs/

# ===================================================================
# OPERATING SYSTEM FILES
# ===================================================================

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
.fseventsd
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
ehthumbs.db
Thumbs.db
Thumbs.db:encryptable
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.directory
.Trash-*

# ===================================================================
# DATABASE AND STORAGE
# ===================================================================

# Local databases
*.db
*.sqlite*
*.db-journal
*.db-wal
*.db-shm

# Database dumps and backups
*.sql.gz
*.dump
*.backup
migrations/backup/
*.migration.backup

# ===================================================================
# TEST AND QUALITY ASSURANCE
# ===================================================================

# Test directories and files
tests/
test/
spec/
__tests__/
*.test.*
*.spec.*
test-reports/
test-output/
coverage/
*.lcov
junit.xml
coverage.json

# Quality assurance reports
.nyc_output
.hypothesis/
bandit-report*
security-reports/
vulnerability-scans/
compliance/
audit/
regulatory/

# ===================================================================
# BACKUP AND VERSION MANAGEMENT
# ===================================================================

# Backup files
*.backup
*.bak
backups/
*.backup/
VERSION.backup
VERSION.bak

# Development artifacts
dev-notes/
scratch/
playground/
benchmarks/
performance-results/

# ===================================================================
# CHROMOFORGE SPECIFIC EXCLUSIONS
# ===================================================================

# Project management
TO-DO.md
CLEANUP_*.md
BASELINE_*.md
PROJECT_HISTORY*.md
implementation-guide.md
setup_guide.md

# Processing artifacts
*.processing
*.in-progress
*.failed

# Configuration backups
config.backup
config.bak
*.config.backup

# Reports and analysis
reports/
csv-database/
fonts/

# ===================================================================
# COMPLIANCE AND REGULATORY (NEVER COPY)
# ===================================================================

# Compliance documentation (may contain sensitive info)
compliance/
audit/
regulatory/
hipaa/
gdpr/
sox/
pci/

# Security scan results
penetration-tests/
security-scans/
vulnerability-assessments/

# ===================================================================
# OPTIMIZATION PATTERNS
# ===================================================================

# Jupyter notebooks (development only)
*.ipynb
.ipynb_checkpoints/
.jupyter/

# Package manager files (already handled in Dockerfile)
requirements-dev.txt
requirements-test.txt
dev-requirements.txt
test-requirements.txt
optional-requirements.txt

# ===================================================================
# END SECURITY .DOCKERIGNORE
# ===================================================================

# VALIDATION RULES:
# 1. All .env* files must be excluded
# 2. No credential files should be copied
# 3. No patient data should be in images
# 4. Documentation files reduce image size
# 5. Development tools stay outside containers